import { ReactNode } from "react";
import { Header } from "./header";
import { Footer } from "./footer";
import { RecycleIcon } from "lucide-react";

type LayoutProps = {
  children: ReactNode;
};

// W2W marquee item component
const MarqueeItem = () => (
  <div className="flex items-center mx-6">
    <RecycleIcon className="text-white w-4 h-4 mr-1" />
    <div className="flex items-center">
      <span className="text-lg font-bold font-poppins text-white">W</span>
      <span className="text-lg font-bold font-poppins text-amber-100 mx-0.5">2</span>
      <span className="text-lg font-bold font-poppins text-white">W</span>
    </div>
    <span className="text-xs font-semibold text-white ml-1">WASTE TO WEALTH</span>
  </div>
);

export function Layout({ children }: LayoutProps) {
  // Create an array of repeating items
  const marqueeItems = Array.from({ length: 10 }, (_, i) => <MarqueeItem key={`item-${i}`} />);
  
  return (
    <div className="flex flex-col min-h-screen">
      {/* W2W Continuously Moving Panel */}
      <div className="bg-[#8bbe1b] overflow-hidden py-1.5 group shadow-md relative z-50">
        <div className="marquee-container flex whitespace-nowrap animate-marquee group-hover:pause-animation">
          {/* First set of items */}
          {marqueeItems}
          {/* Duplicate set for seamless looping */}
          {marqueeItems}
        </div>
      </div>
      
      <Header />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  );
}