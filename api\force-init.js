// Script to force initialize the database
import { db, pingDatabase } from './db.js';
import { initializeDatabase } from './init-db.js';

async function main() {
  try {
    console.log('Attempting to connect to database...');
    const connected = await pingDatabase();
    
    if (!connected) {
      console.error('Failed to connect to database');
      process.exit(1);
    }
    
    console.log('Database connected successfully');
    console.log('Force initializing database...');
    
    const result = await initializeDatabase(true);
    
    console.log('Database initialization result:', result);
    console.log('Database initialization complete');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during database initialization:', error);
    process.exit(1);
  }
}

main();
