import { ChevronRight, MapPin, Recycle, Search, Truck } from "lucide-react";
import { ContentOnlyLayout } from "@/components/layout/content-only-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Link } from "wouter";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

interface DropoffLocation {
  id: number;
  name: string;
  address: string;
  distance: string;
  contactNumber: string;
  hours: string;
  type: 'recycling-center' | 'electronics' | 'hazardous' | 'textiles';
  acceptedItems: string[];
}

const mockLocations: DropoffLocation[] = [
  {
    id: 1,
    name: "EcoRecycle Center",
    address: "123 Green Street, Vatika Mindscapes, Sector 27D, Faridabad",
    distance: "0.8 miles",
    contactNumber: "+91 8745632190",
    hours: "Mon-Sat: 8AM-5PM, Sun: 9AM-3PM",
    type: "recycling-center",
    acceptedItems: ["Paper", "Plastic", "Glass", "Metal", "Cardboard", "Electronics"]
  },
  {
    id: 2,
    name: "Electronics Recycling Hub",
    address: "456 Tech Avenue, Vatika Mindscapes, Sector 27D, Faridabad",
    distance: "1.5 miles",
    contactNumber: "+91 9876543210",
    hours: "Mon-Fri: 9AM-6PM, Sat: 10AM-4PM",
    type: "electronics",
    acceptedItems: ["Computers", "Phones", "TVs", "Batteries", "Appliances", "Cables"]
  },
  {
    id: 3,
    name: "Hazardous Waste Facility",
    address: "789 Safety Road, Vatika Mindscapes, Sector 27D, Faridabad",
    distance: "3.2 miles",
    contactNumber: "+91 9517538642",
    hours: "Tue-Sat: 9AM-4PM",
    type: "hazardous",
    acceptedItems: ["Paint", "Chemicals", "Oil", "Batteries", "Fluorescent Bulbs", "Pesticides"]
  },
  {
    id: 4,
    name: "Textile Recycling Depot",
    address: "321 Fabric Lane, Vatika Mindscapes, Sector 27D, Faridabad",
    distance: "2.1 miles",
    contactNumber: "+91 9876123450",
    hours: "Mon, Wed, Fri: 10AM-6PM, Sat: 9AM-5PM",
    type: "textiles",
    acceptedItems: ["Clothing", "Shoes", "Linens", "Towels", "Curtains", "Bags"]
  },
  {
    id: 5,
    name: "Community Recycling Center",
    address: "567 Local Drive, Vatika Mindscapes, Sector 27D, Faridabad",
    distance: "1.0 miles",
    contactNumber: "+91 9632587410",
    hours: "Mon-Sun: 7AM-7PM",
    type: "recycling-center",
    acceptedItems: ["Paper", "Plastic", "Glass", "Metal", "Cardboard", "Food Waste"]
  },
];

const DropoffLocationsPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  
  const filteredLocations = mockLocations.filter(location => {
    const matchesSearch = 
      location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.acceptedItems.some(item => item.toLowerCase().includes(searchTerm.toLowerCase()));
      
    const matchesFilter = 
      activeFilter === "all" || 
      location.type === activeFilter;
      
    return matchesSearch && matchesFilter;
  });

  return (
    <ContentOnlyLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-[#8bbe1b] transition-colors">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <span>Drop-off Locations</span>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
              Find Recycling Drop-off Locations
            </h1>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Discover convenient recycling centers near you for all your recyclable items. 
              Use our search tool to find the perfect place to drop off your materials.
            </p>
          </div>

          <div className="bg-amber-50 border border-amber-100 rounded-lg p-6 mb-10">
            <h2 className="text-xl font-bold mb-4 text-gray-800">Find a Drop-off Location</h2>
            
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search by location name, address, or material type..."
                className="pl-10 bg-white border-amber-200"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Tabs defaultValue="all" onValueChange={setActiveFilter} className="w-full">
              <TabsList className="w-full grid grid-cols-2 md:grid-cols-5 mb-4">
                <TabsTrigger value="all" className="text-xs sm:text-sm">All Locations</TabsTrigger>
                <TabsTrigger value="recycling-center" className="text-xs sm:text-sm">Recycling Centers</TabsTrigger>
                <TabsTrigger value="electronics" className="text-xs sm:text-sm">Electronics</TabsTrigger>
                <TabsTrigger value="hazardous" className="text-xs sm:text-sm">Hazardous Waste</TabsTrigger>
                <TabsTrigger value="textiles" className="text-xs sm:text-sm">Textiles</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="space-y-6 mb-12">
            {filteredLocations.length > 0 ? (
              filteredLocations.map(location => (
                <Card key={location.id} className="border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                      <div>
                        <CardTitle className="text-xl">{location.name}</CardTitle>
                        <CardDescription className="flex items-center gap-1 mt-1">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          {location.address}
                        </CardDescription>
                      </div>
                      <Badge 
                        className="self-start md:self-center"
                        variant="outline"
                      >
                        {location.distance}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-1">Hours of Operation:</h4>
                        <p className="text-gray-600 text-sm">{location.hours}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-1">Contact:</h4>
                        <p className="text-gray-600 text-sm">{location.contactNumber}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-1">Accepted Items:</h4>
                        <div className="flex flex-wrap gap-1">
                          {location.acceptedItems.map((item, index) => (
                            <span 
                              key={index} 
                              className="inline-block text-xs bg-[#8bbe1b]/10 text-[#6a9816] px-2 py-1 rounded-full"
                            >
                              {item}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12 bg-amber-50 rounded-lg">
                <Recycle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2">No locations found</h3>
                <p className="text-gray-600">
                  Try adjusting your search or filter criteria to find recycling drop-off locations.
                </p>
              </div>
            )}
          </div>

          <div className="bg-[#8bbe1b]/10 rounded-lg p-6 mb-8">
            <div className="flex items-start gap-4">
              <div className="bg-[#8bbe1b] rounded-full p-2 flex-shrink-0">
                <Truck className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2 text-gray-800">Not able to drop off your items?</h3>
                <p className="text-gray-600 mb-4">
                  For large items or bulk recycling, we offer pickup services. Schedule a convenient 
                  time and our team will collect your recyclables directly from your location.
                </p>
                <Button className="bg-[#8bbe1b] hover:bg-[#7aa518] text-white">
                  Schedule a Pickup
                </Button>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h3 className="text-xl font-bold mb-4 text-gray-800">Explore Related Sustainability Resources</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/recycling-guide" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Recycling Guide</h4>
                <p className="text-sm text-gray-600">Learn how to recycle properly</p>
              </Link>
              <Link href="/trade-in" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Trade-in Program</h4>
                <p className="text-sm text-gray-600">Exchange your old items</p>
              </Link>
              <Link href="/repair-services" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Repair Services</h4>
                <p className="text-sm text-gray-600">Fix instead of replace</p>
              </Link>
              <Link href="/sustainability" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Sustainability Tips</h4>
                <p className="text-sm text-gray-600">Live more eco-friendly</p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ContentOnlyLayout>
  );
};

export default DropoffLocationsPage;