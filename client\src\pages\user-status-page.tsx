import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { UserStatus } from "@/components/ui/user-status";
import { Link } from "wouter";

export default function UserStatusPage() {
  const { user, logout } = useAuth();
  const isLoggedIn = !!user;

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">User Authentication Status</h1>

      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-8">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Current Status</h2>
          <div className="flex items-center mb-4">
            <UserStatus className="mr-4" />
          </div>

          {isLoggedIn ? (
            <Button
              variant="destructive"
              onClick={() => logout()}
              className="mt-2"
            >
              Logout
            </Button>
          ) : (
            <Button asChild className="mt-2">
              <Link href="/auth">Login</Link>
            </Button>
          )}
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">User Details</h2>
          {isLoggedIn ? (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Property</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Value</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr>
                    <td className="py-3 px-4 text-sm font-medium">User ID</td>
                    <td className="py-3 px-4 text-sm">{user.id}</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-sm font-medium">Username</td>
                    <td className="py-3 px-4 text-sm">{user.username}</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-sm font-medium">Email</td>
                    <td className="py-3 px-4 text-sm">{user.email}</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-sm font-medium">Full Name</td>
                    <td className="py-3 px-4 text-sm">{user.fullName}</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-sm font-medium">Login Status</td>
                    <td className="py-3 px-4 text-sm text-green-600 font-medium">True</td>
                  </tr>
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded text-center">
              <p className="text-gray-500">You are not logged in</p>
              <p className="text-red-600 font-medium mt-1">Login Status: False</p>
              <Button asChild className="mt-4">
                <Link href="/auth">Login Now</Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}