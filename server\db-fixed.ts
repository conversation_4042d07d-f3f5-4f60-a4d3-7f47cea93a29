import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
// Import schema directly from shared directory
import * as schema from "../shared/schema.ts";
import dotenv from 'dotenv';
import fs from 'fs';

// Load the appropriate .env file based on environment
if (process.env.VERCEL) {
  // For Vercel environment
  if (fs.existsSync('.env.vercel')) {
    dotenv.config({ path: '.env.vercel' });
    console.log('Loaded .env.vercel file for Vercel environment');
  } else {
    dotenv.config();
  }
} else {
  // For regular environment
  dotenv.config();
}

// Get connection string from environment variables
// Try both POSTGRES_URL and DATABASE_URL to support different environments
const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;

if (!connectionString) {
  throw new Error("Neither POSTGRES_URL nor DATABASE_URL is defined in environment variables");
}

// Log database connection attempt for debugging
console.log(`Attempting to connect to database with ${process.env.VERCEL ? 'Vercel' : 'standard'} environment`);
console.log(`Database URL configured: ${connectionString ? '✓ Present' : '✗ Missing'}`);
console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`SSL Mode: ${process.env.PGSSLMODE || 'default'}`);
console.log(`SSL Reject Unauthorized: ${process.env.NODE_TLS_REJECT_UNAUTHORIZED || 'default'}`);


// Serverless-friendly connection pooling config
let globalClient: ReturnType<typeof postgres> | undefined = undefined;

const getClient = () => {
  // Use existing connection if available (handles Vercel serverless function reuse)
  if (globalClient) return globalClient;

  // Configure connection for serverless environment
  const clientConfig = {
    // For serverless, use lower connection counts
    max: 3,
    idle_timeout: 20,
    connect_timeout: 10,
    prepare: false,
    // Always use rejectUnauthorized: false for Vercel environment
    ssl: { rejectUnauthorized: false }
  };

  // For Vercel environment, explicitly set SSL options
  if (process.env.VERCEL) {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    process.env.PGSSLMODE = 'no-verify';
  }

  try {
    console.log('Connecting to PostgreSQL database...');
    globalClient = postgres(connectionString, clientConfig);
    console.log('Successfully connected to PostgreSQL database');
    return globalClient;
  } catch (error) {
    console.error('Failed to connect to PostgreSQL database:', error);
    throw error;
  }
};

// Create a client without drizzle first to test the connection
const client = getClient();

// Singleton database client - creates connection only when needed
export const db = drizzle(client, { schema });

// Check database connection
export async function pingDatabase() {
  try {
    const result = await db.select().from(schema.users).limit(1);
    console.log('Database ping successful');
    return true;
  } catch (error) {
    console.error('Database ping failed:', error);
    throw error; // Let the caller handle it - more visible for debugging
  }
}
