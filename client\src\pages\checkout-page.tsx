import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest, queryClient } from "@/lib/queryClient";
import {
  CreditCard,
  ChevronRight,
  Check,
  CreditCardIcon,
  Truck,
  Clock,
  ShieldCheck,
  SquareCheck,
  Loader2
} from "lucide-react";

// Base checkout form schema
const baseCheckoutSchema = z.object({
  fullName: z.string().min(3, "Full name is required"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  address: z.string().min(5, "Address is required"),
  city: z.string().min(2, "City is required"),
  state: z.string().min(2, "State is required"),
  zipCode: z.string().min(5, "Zip code is required"),
  country: z.string().min(2, "Country is required"),
  paymentMethod: z.enum(["credit", "paypal", "apple"]),
  cardNumber: z.string().optional(),
  cardName: z.string().optional(),
  cardExpiry: z.string().optional(),
  cardCvc: z.string().optional(),
  saveInfo: z.boolean().optional(),
});

// Shipping step schema - only validates shipping fields
const shippingSchema = baseCheckoutSchema.pick({
  fullName: true,
  email: true,
  phone: true,
  address: true,
  city: true,
  state: true,
  zipCode: true,
  country: true,
  saveInfo: true
});

// Payment step schema - validates payment method fields
const paymentSchema = baseCheckoutSchema.pick({
  paymentMethod: true,
  cardNumber: true,
  cardName: true,
  cardExpiry: true,
  cardCvc: true,
}).superRefine((data, ctx) => {
  // Only validate card details if the payment method is credit card
  if (data.paymentMethod === "credit") {
    // Required card number
    if (!data.cardNumber) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Card number is required",
        path: ["cardNumber"],
      });
    } else if (data.cardNumber.length < 13 || data.cardNumber.length > 19) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid card number length",
        path: ["cardNumber"],
      });
    }

    // Required name on card
    if (!data.cardName) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Name on card is required",
        path: ["cardName"],
      });
    } else if (data.cardName.length < 3) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Name must be at least 3 characters",
        path: ["cardName"],
      });
    }

    // Required expiration date
    if (!data.cardExpiry) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Expiration date is required",
        path: ["cardExpiry"],
      });
    }

    // Required CVC
    if (!data.cardCvc) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "CVC is required",
        path: ["cardCvc"],
      });
    } else if (data.cardCvc.length < 3 || data.cardCvc.length > 4) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "CVC must be 3-4 digits",
        path: ["cardCvc"],
      });
    }
  }
  // For other payment methods, we don't need any additional validation
});

// Complete checkout schema for the final step
const checkoutSchema = baseCheckoutSchema;

type CheckoutFormValues = z.infer<typeof baseCheckoutSchema>;

export default function CheckoutPage() {
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const [step, setStep] = useState<"shipping" | "payment" | "confirm">("shipping");

  // Redirect to login if not authenticated
  if (!user) {
    navigate("/auth");
    return null;
  }

  // Fetch cart items
  const { data: cartItems, isLoading, error } = useQuery({
    queryKey: ["/api/cart", user ? { userId: user.id } : null],
    enabled: !!user,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching cart items for checkout:', error);
    },
    // Ensure we always get an array even if the API fails
    select: (data) => {
      if (!data || !Array.isArray(data)) {
        console.warn('Cart data is not an array, returning empty array');
        return [];
      }
      return data.filter(item => item && item.product); // Filter out invalid items
    }
  });

  // Calculate cart totals
  const subtotal = Array.isArray(cartItems)
    ? cartItems.reduce((total: number, item: any) => {
        const price = item.product.discountPrice || item.product.price;
        return total + (price * item.quantity);
      }, 0)
    : 0;

  // Shipping is free if subtotal >= 50
  const shipping = subtotal >= 50 ? 0 : 9.99;
  const tax = subtotal * 0.07; // 7% tax
  const total = subtotal + shipping + tax;

  // Get the appropriate schema based on the current step
  const getSchemaForStep = React.useCallback(() => {
    switch(step) {
      case "shipping":
        return shippingSchema;
      case "payment":
        return paymentSchema;
      case "confirm":
        return checkoutSchema;
      default:
        return shippingSchema;
    }
  }, [step]);

  // For debugging
  console.log("Current step:", step);

  // Initialize form with user data if available
  const currentSchema = getSchemaForStep();

  const form = useForm<CheckoutFormValues>({
    resolver: zodResolver(currentSchema), // Use the schema for the current step
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      phone: user?.phone || "",
      address: user?.address || "",
      city: "",
      state: "",
      zipCode: "",
      country: "United States",
      paymentMethod: "credit",
      saveInfo: true,
    },
    mode: "onTouched", // Changed to onTouched for better UX
  });

  // Create order mutation
  const createOrderMutation = useMutation({
    mutationFn: async (data: CheckoutFormValues) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to place an order");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      console.log(`Creating order for user ${user.id}`);

      // Prepare the order data
      const orderData = {
        userId: parseInt(user.id.toString()),
        address: `${data.address}, ${data.city}, ${data.state} ${data.zipCode}, ${data.country}`,
        paymentMethod: data.paymentMethod,
        total: total,
        items: cartItems.map((item: any) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.product.discountPrice || item.product.price
        }))
      };

      console.log('Order data:', orderData);

      // Make a direct fetch request to ensure it works
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to create order: ${response.status} ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log('Order created successfully:', data);

      if (user) {
        // Force invalidate and refetch the cart and orders queries
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.invalidateQueries({ queryKey: ["/api/orders"] });
        queryClient.invalidateQueries({ queryKey: ["/api/orders", { userId: user.id }] });

        // Force refetch the cart and orders data
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/orders"] });
        queryClient.refetchQueries({ queryKey: ["/api/orders", { userId: user.id }] });
      }

      toast({
        title: "Order placed successfully",
        description: "Thank you for your purchase!",
      });

      // Navigate to the profile page with the orders tab active
      navigate("/profile?tab=orders");
    },
    onError: (error) => {
      console.error("Order error:", error);
      toast({
        title: "Failed to place order",
        description: "Please try again later",
        variant: "destructive",
      });
    },
  });

  // Set focus based on the current step
  useEffect(() => {
    // Clear any errors when changing steps
    form.clearErrors();

    if (step === "shipping") {
      form.setFocus("fullName");
    } else if (step === "payment") {
      form.setFocus("paymentMethod");
    }
  }, [step, form]);

  const onSubmit = async (data: CheckoutFormValues) => {
    try {
      console.log("Form submitted with data:", data);
      console.log("Current step:", step);

      if (step === "shipping") {
        // Validate shipping fields only
        await shippingSchema.parseAsync(data);
        setStep("payment");
      } else if (step === "payment") {
        // Validate payment fields only
        await paymentSchema.parseAsync(data);
        setStep("confirm");
      } else {
        // Process the complete order
        console.log("Creating order with:", data);
        createOrderMutation.mutate(data);
      }
    } catch (error) {
      console.error("Validation error:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-secondary" />
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !cartItems || !Array.isArray(cartItems) || cartItems.length === 0) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-sm p-8 max-w-md text-center">
            <h1 className="text-xl font-bold text-primary mb-2">No Items in Cart</h1>
            <p className="text-gray-500 mb-4">Your cart is empty. Please add some products before checkout.</p>
            <Button onClick={() => navigate("/products")}>
              Continue Shopping
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 bg-background">
        {/* Breadcrumb */}
        <div className="bg-white border-y border-gray-200">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center text-sm">
              <a href="/" className="text-gray-500 hover:text-secondary">Home</a>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <a href="/cart" className="text-gray-500 hover:text-secondary">Cart</a>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <span className="text-primary font-medium">Checkout</span>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl md:text-3xl font-bold font-poppins text-primary mb-6">
            Checkout
          </h1>

          {/* Checkout Progress */}
          <div className="bg-white rounded-lg shadow-sm p-4 mb-8">
            <div className="flex justify-between">
              <div className={`flex flex-col items-center ${step === "shipping" ? "text-secondary" : "text-primary"}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${step === "shipping" ? "bg-secondary text-white" : "bg-primary/10 text-primary"}`}>
                  {step === "shipping" ? 1 : <Check className="h-4 w-4" />}
                </div>
                <span className="text-sm font-medium">Shipping</span>
              </div>
              <div className="w-full max-w-[100px] flex items-center mx-2">
                <div className={`h-1 w-full ${step === "shipping" ? "bg-gray-200" : "bg-secondary"}`}></div>
              </div>
              <div className={`flex flex-col items-center ${step === "payment" ? "text-secondary" : step === "confirm" ? "text-primary" : "text-gray-400"}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                  step === "payment" ? "bg-secondary text-white" :
                  step === "confirm" ? "bg-primary/10 text-primary" :
                  "bg-gray-200 text-gray-400"
                }`}>
                  {step === "confirm" ? <Check className="h-4 w-4" /> : 2}
                </div>
                <span className="text-sm font-medium">Payment</span>
              </div>
              <div className="w-full max-w-[100px] flex items-center mx-2">
                <div className={`h-1 w-full ${step === "confirm" ? "bg-secondary" : "bg-gray-200"}`}></div>
              </div>
              <div className={`flex flex-col items-center ${step === "confirm" ? "text-secondary" : "text-gray-400"}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${step === "confirm" ? "bg-secondary text-white" : "bg-gray-200 text-gray-400"}`}>
                  3
                </div>
                <span className="text-sm font-medium">Confirmation</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Form Section */}
            <div className="lg:w-2/3">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)}>
                    {/* Shipping Information */}
                    {step === "shipping" && (
                      <div className="p-6">
                        <h2 className="text-lg font-medium mb-4">Shipping Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="fullName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Full Name</FormLabel>
                                <FormControl>
                                  <Input placeholder="John Doe" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email Address</FormLabel>
                                <FormControl>
                                  <Input type="email" placeholder="<EMAIL>" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Phone Number</FormLabel>
                                <FormControl>
                                  <Input placeholder="(*************" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                              <FormItem className="md:col-span-2">
                                <FormLabel>Address</FormLabel>
                                <FormControl>
                                  <Input placeholder="123 Main St" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="city"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>City</FormLabel>
                                <FormControl>
                                  <Input placeholder="New York" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name="state"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>State</FormLabel>
                                  <FormControl>
                                    <Input placeholder="NY" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="zipCode"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Zip Code</FormLabel>
                                  <FormControl>
                                    <Input placeholder="10001" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <FormField
                            control={form.control}
                            name="country"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Country</FormLabel>
                                <FormControl>
                                  <Input placeholder="United States" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="saveInfo"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-6">
                              <FormControl>
                                <input
                                  type="checkbox"
                                  className="form-checkbox h-4 w-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Save this information for next time</FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />

                        <div className="mt-6 flex justify-end">
                          <Button type="submit" className="w-full md:w-auto">
                            Continue to Payment
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Payment Information */}
                    {step === "payment" && (
                      <div className="p-6">
                        <h2 className="text-lg font-medium mb-4">Payment Method</h2>

                        <FormField
                          control={form.control}
                          name="paymentMethod"
                          render={({ field }) => (
                            <FormItem className="space-y-3">
                              <FormControl>
                                <RadioGroup
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  className="space-y-3"
                                >
                                  <div className="flex items-center space-x-2 border rounded-md p-3 cursor-pointer hover:border-secondary">
                                    <RadioGroupItem value="credit" id="credit" />
                                    <Label htmlFor="credit" className="flex items-center cursor-pointer">
                                      <CreditCardIcon className="h-5 w-5 mr-2 text-secondary" />
                                      Credit / Debit Card
                                    </Label>
                                  </div>

                                  <div className="flex items-center space-x-2 border rounded-md p-3 cursor-pointer hover:border-secondary">
                                    <RadioGroupItem value="paypal" id="paypal" />
                                    <Label htmlFor="paypal" className="flex items-center cursor-pointer">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#003087" className="mr-2">
                                        <path d="M9.112 7.571c-.263 1.677-.498 3.172-.727 4.62l-.57.38h1.112c1.35-8.192 1.473-8.386 2.9-8.386.52 0 .867.18 1.013.514.187.43.077 1.218-.246 2.668a241.96 241.96 0 00-.619 2.947c-.11.587-.157.668-.306.815-.182.181-.303.21-1.244.21h-.899l.15-.92c.269-1.654.47-2.536.727-3.241.17-.464.22-.938.107-1.004-.062-.036-.214-.05-.395-.039-.267.018-.322.036-.482.191-.161.155-.228.304-.526 1.173-.38 1.107-.593 2.974-1.072 9.419l-.069.985h1.15c1.15 0 1.152 0 1.257-.123.103-.12.122-.202.178-.741.035-.343.084-.705.11-.804l.047-.18h.941c.9 0 .95-.7.998-.124.089-.219.293-1.72.293-2.16 0-.442-.124-.624-.481-.722-.153-.042-.153-.042.016-.3.322-.49.367-1.204.098-1.587-.09-.128-.179-.183-.379-.235-.336-.087-.327-.116-.114-.335.428-.438.378-1.356-.1-1.813-.366-.351-.92-.459-2.157-.419-.901.029-.98.039-1.357.187-.438.172-.627.31-.832.605-.16.231-.293.656-.707 2.25l-.134.516.21-.192c.349-.32.575-.372 1.787-.414 1.342-.047 1.303-.078 1.426 1.09l.05.486-.404.014c-.667.024-1.03.137-1.302.407-.319.317-.442.805-.442 1.757 0 .404.018.652.062.791.153.492.493.72 1.024.684.392-.026.658-.142.93-.406.165-.16.246-.267.382-.505l.174-.302.083.138c.14.233.432.442.766.547.215.68.378.08 1.275.079l.992-.001-.128.176c-.28.387-.65.637-1.102.745-.192.046-.383.055-1.233.054-1.006-.001-1.003-.001-1.15.132-.137.125-.233.366-.233.587 0 .342.315.74.739.933.357.162.66.193 1.764.177.907-.013 1.025-.024 1.42-.123 1.052-.266 1.707-.906 2.128-2.08.15-.42.235-.865.512-2.692.167-1.107.38-2.41.652-3.996.158-.924.287-1.898.287-2.177 0-.569-.226-1.025-.642-1.294-.294-.19-.591-.238-1.403-.226-.748.01-1.023.045-1.34.168-.257.1-.606.316-.783.485l-.11.105.13-.214c.073-.12.178-.356.234-.527l.102-.31h-2.29z"/>
                                      </svg>
                                      PayPal
                                    </Label>
                                  </div>

                                  <div className="flex items-center space-x-2 border rounded-md p-3 cursor-pointer hover:border-secondary">
                                    <RadioGroupItem value="apple" id="apple" />
                                    <Label htmlFor="apple" className="flex items-center cursor-pointer">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="black" className="mr-2">
                                        <path d="M17.6 13.2c0-1.7 1.4-2.5 1.5-2.6-.8-1.2-2.1-1.3-2.5-1.3-1.1-.1-2.1.6-2.6.6-.6 0-1.4-.6-2.3-.6-1.2 0-2.3.7-2.9 1.8-1.2 2.1-.3 5.3.9 7 .6.9 1.3 1.8 2.2 1.8.9 0 1.2-.6 2.3-.6 1.1 0 1.4.6 2.4.5 1 0 1.6-.9 2.2-1.7.7-1 1-2 1-2.1-.1-.1-1.9-.7-1.9-2.8zM16.1 6c.5-.6.8-1.4.7-2.3-.7 0-1.6.5-2.1 1.1-.5.6-.9 1.4-.7 2.2.7.1 1.6-.4 2.1-1z"/>
                                      </svg>
                                      Apple Pay
                                    </Label>
                                  </div>
                                </RadioGroup>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {form.watch("paymentMethod") === "credit" && (
                          <div className="mt-6 space-y-4">
                            <FormField
                              control={form.control}
                              name="cardNumber"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Card Number</FormLabel>
                                  <FormControl>
                                    <Input placeholder="1234 5678 9012 3456" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="cardName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name on Card</FormLabel>
                                  <FormControl>
                                    <Input placeholder="John Doe" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="cardExpiry"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Expiration Date</FormLabel>
                                    <FormControl>
                                      <Input placeholder="MM/YY" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="cardCvc"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>CVC</FormLabel>
                                    <FormControl>
                                      <Input placeholder="123" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        )}

                        <div className="mt-6 flex flex-col sm:flex-row justify-between gap-3">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setStep("shipping")}
                          >
                            Back to Shipping
                          </Button>
                          <Button type="submit">
                            Review Order
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Order Confirmation */}
                    {step === "confirm" && (
                      <div className="p-6">
                        <h2 className="text-lg font-medium mb-4">Order Review</h2>

                        <div className="space-y-6">
                          <div className="border rounded-lg p-4 bg-gray-50">
                            <h3 className="font-medium mb-2">Shipping Information</h3>
                            <p className="text-gray-700">{form.getValues().fullName}</p>
                            <p className="text-gray-700">{form.getValues().email}</p>
                            <p className="text-gray-700">{form.getValues().phone}</p>
                            <p className="text-gray-700">{form.getValues().address}</p>
                            <p className="text-gray-700">
                              {form.getValues().city}, {form.getValues().state} {form.getValues().zipCode}
                            </p>
                            <p className="text-gray-700">{form.getValues().country}</p>
                          </div>

                          <div className="border rounded-lg p-4 bg-gray-50">
                            <h3 className="font-medium mb-2">Payment Method</h3>
                            {form.getValues().paymentMethod === "credit" && (
                              <div className="flex items-center">
                                <CreditCardIcon className="h-5 w-5 mr-2 text-secondary" />
                                <span>Credit Card (**** **** **** {form.getValues().cardNumber?.slice(-4) || "1234"})</span>
                              </div>
                            )}
                            {form.getValues().paymentMethod === "paypal" && (
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#003087" className="mr-2">
                                  <path d="M9.112 7.571c-.263 1.677-.498 3.172-.727 4.62l-.57.38h1.112c1.35-8.192 1.473-8.386 2.9-8.386.52 0 .867.18 1.013.514.187.43.077 1.218-.246 2.668a241.96 241.96 0 00-.619 2.947c-.11.587-.157.668-.306.815-.182.181-.303.21-1.244.21h-.899l.15-.92c.269-1.654.47-2.536.727-3.241.17-.464.22-.938.107-1.004-.062-.036-.214-.05-.395-.039-.267.018-.322.036-.482.191-.161.155-.228.304-.526 1.173-.38 1.107-.593 2.974-1.072 9.419l-.069.985h1.15c1.15 0 1.152 0 1.257-.123.103-.12.122-.202.178-.741.035-.343.084-.705.11-.804l.047-.18h.941c.9 0 .95-.7.998-.124.089-.219.293-1.72.293-2.16 0-.442-.124-.624-.481-.722-.153-.042-.153-.042.016-.3.322-.49.367-1.204.098-1.587-.09-.128-.179-.183-.379-.235-.336-.087-.327-.116-.114-.335.428-.438.378-1.356-.1-1.813-.366-.351-.92-.459-2.157-.419-.901.029-.98.039-1.357.187-.438.172-.627.31-.832.605-.16.231-.293.656-.707 2.25l-.134.516.21-.192c.349-.32.575-.372 1.787-.414 1.342-.047 1.303-.078 1.426 1.09l.05.486-.404.014c-.667.024-1.03.137-1.302.407-.319.317-.442.805-.442 1.757 0 .404.018.652.062.791.153.492.493.72 1.024.684.392-.026.658-.142.93-.406.165-.16.246-.267.382-.505l.174-.302.083.138c.14.233.432.442.766.547.215.68.378.08 1.275.079l.992-.001-.128.176c-.28.387-.65.637-1.102.745-.192.046-.383.055-1.233.054-1.006-.001-1.003-.001-1.15.132-.137.125-.233.366-.233.587 0 .342.315.74.739.933.357.162.66.193 1.764.177.907-.013 1.025-.024 1.42-.123 1.052-.266 1.707-.906 2.128-2.08.15-.42.235-.865.512-2.692.167-1.107.38-2.41.652-3.996.158-.924.287-1.898.287-2.177 0-.569-.226-1.025-.642-1.294-.294-.19-.591-.238-1.403-.226-.748.01-1.023.045-1.34.168-.257.1-.606.316-.783.485l-.11.105.13-.214c.073-.12.178-.356.234-.527l.102-.31h-2.29z"/>
                                </svg>
                                <span>PayPal</span>
                              </div>
                            )}
                            {form.getValues().paymentMethod === "apple" && (
                              <div className="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="black" className="mr-2">
                                  <path d="M17.6 13.2c0-1.7 1.4-2.5 1.5-2.6-.8-1.2-2.1-1.3-2.5-1.3-1.1-.1-2.1.6-2.6.6-.6 0-1.4-.6-2.3-.6-1.2 0-2.3.7-2.9 1.8-1.2 2.1-.3 5.3.9 7 .6.9 1.3 1.8 2.2 1.8.9 0 1.2-.6 2.3-.6 1.1 0 1.4.6 2.4.5 1 0 1.6-.9 2.2-1.7.7-1 1-2 1-2.1-.1-.1-1.9-.7-1.9-2.8zM16.1 6c.5-.6.8-1.4.7-2.3-.7 0-1.6.5-2.1 1.1-.5.6-.9 1.4-.7 2.2.7.1 1.6-.4 2.1-1z"/>
                                </svg>
                                <span>Apple Pay</span>
                              </div>
                            )}
                          </div>

                          <div className="border rounded-lg p-4">
                            <h3 className="font-medium mb-3">Order Items</h3>
                            <div className="space-y-3">
                              {Array.isArray(cartItems) && cartItems.map((item: any) => (
                                <div key={item.id} className="flex items-center">
                                  <div className="w-12 h-12 rounded-md overflow-hidden mr-3">
                                    <img
                                      src={item.product.imageUrl}
                                      alt={item.product.name}
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                  <div className="flex-1">
                                    <p className="font-medium">{item.product.name}</p>
                                    <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                                  </div>
                                  <div className="font-medium">
                                    ${((item.product.discountPrice || item.product.price) * item.quantity).toFixed(2)}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div className="mt-6 flex flex-col sm:flex-row justify-between gap-3">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setStep("payment")}
                          >
                            Back to Payment
                          </Button>
                          <Button
                            type="submit"
                            className="bg-secondary hover:bg-secondary/90"
                            disabled={createOrderMutation.isPending}
                          >
                            {createOrderMutation.isPending ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>Place Order</>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </form>
                </Form>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:w-1/3">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden sticky top-4">
                <div className="p-6">
                  <h2 className="text-lg font-medium mb-4">Order Summary</h2>

                  <div className="space-y-3 mb-4">
                    {Array.isArray(cartItems) && cartItems.slice(0, 3).map((item: any) => (
                      <div key={item.id} className="flex items-center">
                        <div className="w-12 h-12 rounded-md overflow-hidden mr-3">
                          <img
                            src={item.product.imageUrl}
                            alt={item.product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium truncate">{item.product.name}</p>
                          <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                        </div>
                        <div className="font-medium text-sm">
                          ${((item.product.discountPrice || item.product.price) * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    ))}

                    {Array.isArray(cartItems) && cartItems.length > 3 && (
                      <p className="text-sm text-gray-500 italic">
                        +{cartItems.length - 3} more items
                      </p>
                    )}
                  </div>

                  <Separator className="my-4" />

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping</span>
                      <span className="font-medium">{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tax (7%)</span>
                      <span className="font-medium">${tax.toFixed(2)}</span>
                    </div>
                  </div>

                  <Separator className="my-4" />

                  <div className="flex justify-between mb-6">
                    <span className="text-lg font-medium">Total</span>
                    <span className="text-xl font-bold text-primary">${total.toFixed(2)}</span>
                  </div>

                  <div className="space-y-3 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Truck className="h-4 w-4 mr-2 text-gray-500" />
                      <span>Free shipping on orders over $50</span>
                    </div>
                    <div className="flex items-center">
                      <ShieldCheck className="h-4 w-4 mr-2 text-gray-500" />
                      <span>Secure checkout</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gray-500" />
                      <span>Delivery in 3-5 business days</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
