import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import {
  ChevronLeft,
  Package,
  Truck,
  CreditCard,
  MapPin,
  Calendar,
  Clock,
  Check,
  AlertCircle,
  Loader2
} from "lucide-react";

import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";

export default function OrderDetailsPage() {
  const params = useParams();
  const [, navigate] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const orderId = params?.id ? parseInt(params.id) : null;

  // Store userId in localStorage for future requests
  useEffect(() => {
    if (user && user.id) {
      localStorage.setItem('userId', user.id.toString());
    }
  }, [user]);

  // Redirect to login if not authenticated
  if (!user) {
    navigate("/auth");
    return null;
  }

  if (!orderId) {
    navigate("/profile");
    return null;
  }

  // Define Order type for better type safety
  type Order = {
    id: number;
    status: string;
    total: number;
    createdAt: string;
    address: string;
    paymentMethod: string;
    userId: number;
  };

  type OrderItem = {
    id: number;
    orderId: number;
    productId: number;
    quantity: number;
    price: number;
    product: {
      id: number;
      name: string;
      imageUrl: string;
      category: {
        id: number;
        name: string;
      }
    }
  };

  // Fetch order details
  const {
    data: order,
    isLoading,
    error,
    refetch: refetchOrder
  } = useQuery<Order & { items: OrderItem[] }>({
    queryKey: [`/api/orders/${orderId}`, user ? { userId: user.id } : null],
    queryFn: async ({ queryKey }) => {
      console.log('Fetching order details with query key:', queryKey);

      // Extract userId from query key
      const userId = user?.id;

      if (!userId) {
        console.error('No userId available for fetching order details');
        throw new Error('User ID is required');
      }

      console.log(`Making direct fetch request for order ${orderId} with userId: ${userId}`);

      // Make a direct fetch request to ensure it works
      const response = await fetch(`/api/orders/${orderId}?userId=${userId}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to fetch order details: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Order data received:', data);

      return data;
    },
    enabled: !!user && !!orderId,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching order details:', error);
      toast({
        title: "Error",
        description: "Failed to load order details. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Define variables for order items
  const orderItems = order?.items || [];
  const isLoadingItems = isLoading;

  // Force refetch order when the component mounts
  useEffect(() => {
    if (user && orderId) {
      console.log('Refetching order details');
      refetchOrder();
    }
  }, [refetchOrder, user, orderId]);

  const getStatusStep = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 1;
      case 'processing':
        return 2;
      case 'shipped':
        return 3;
      case 'delivered':
        return 4;
      default:
        return 1;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-secondary" />
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-sm p-8 max-w-md text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-3" />
            <h1 className="text-xl font-bold text-primary mb-2">Order Not Found</h1>
            <p className="text-gray-500 mb-4">We couldn't find the order you're looking for. It may have been removed or you don't have permission to view it.</p>
            <Button onClick={() => navigate("/profile")}>
              Back to Profile
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Ensure order is properly typed to prevent TypeScript errors
  const statusStep = order ? getStatusStep(order.status) : 1;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 bg-background">
        <div className="container mx-auto px-4 py-8">
          <button
            onClick={() => navigate("/profile")}
            className="inline-flex items-center text-secondary hover:text-secondary/80 mb-6"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Profile
          </button>

          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl md:text-3xl font-bold font-poppins text-primary">
              Order #{order.id}
            </h1>
            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status || 'pending')}`}>
              {order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Pending'}
            </span>
          </div>

          {/* Order Timeline */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-lg font-medium mb-6">Order Status</h2>

            <div className="relative">
              {/* Status line */}
              <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

              {/* Status steps */}
              <div className="space-y-8 relative">
                {/* Pending */}
                <div className="flex">
                  <div className={`z-10 flex items-center justify-center w-12 h-12 rounded-full ${statusStep >= 1 ? 'bg-secondary text-white' : 'bg-gray-200'}`}>
                    {statusStep > 1 ? (
                      <Check className="h-6 w-6" />
                    ) : (
                      <Package className="h-6 w-6" />
                    )}
                  </div>
                  <div className="ml-4">
                    <h3 className="font-medium">Order Placed</h3>
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{formatDate(order.createdAt)}</span>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Your order has been received and is being processed.
                    </p>
                  </div>
                </div>

                {/* Processing */}
                <div className="flex">
                  <div className={`z-10 flex items-center justify-center w-12 h-12 rounded-full ${statusStep >= 2 ? 'bg-secondary text-white' : 'bg-gray-200'}`}>
                    {statusStep > 2 ? (
                      <Check className="h-6 w-6" />
                    ) : (
                      <Package className="h-6 w-6" />
                    )}
                  </div>
                  <div className="ml-4">
                    <h3 className="font-medium">Processing</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Your order is being prepared for shipping.
                    </p>
                  </div>
                </div>

                {/* Shipped */}
                <div className="flex">
                  <div className={`z-10 flex items-center justify-center w-12 h-12 rounded-full ${statusStep >= 3 ? 'bg-secondary text-white' : 'bg-gray-200'}`}>
                    {statusStep > 3 ? (
                      <Check className="h-6 w-6" />
                    ) : (
                      <Truck className="h-6 w-6" />
                    )}
                  </div>
                  <div className="ml-4">
                    <h3 className="font-medium">Shipped</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Your order is on its way to you.
                    </p>
                  </div>
                </div>

                {/* Delivered */}
                <div className="flex">
                  <div className={`z-10 flex items-center justify-center w-12 h-12 rounded-full ${statusStep >= 4 ? 'bg-secondary text-white' : 'bg-gray-200'}`}>
                    <Check className="h-6 w-6" />
                  </div>
                  <div className="ml-4">
                    <h3 className="font-medium">Delivered</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Your order has been delivered.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Order Details */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
                <div className="border-b border-gray-200 p-4 md:p-6">
                  <h2 className="text-lg font-medium">Order Items</h2>
                </div>

                {isLoadingItems ? (
                  <div className="p-4 flex justify-center">
                    <Loader2 className="h-6 w-6 animate-spin text-secondary" />
                  </div>
                ) : orderItems && Array.isArray(orderItems) && orderItems.length > 0 ? (
                  <div>
                    {orderItems.map((item: any) => (
                      <div key={item.id} className="p-4 md:p-6 border-b border-gray-200 last:border-0">
                        <div className="flex items-start">
                          <div className="h-20 w-20 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                            <img
                              src={item.product?.imageUrl || 'https://via.placeholder.com/100'}
                              alt={item.product?.name || 'Product'}
                              className="h-full w-full object-cover object-center"
                            />
                          </div>
                          <div className="ml-4 flex-1">
                            <div className="flex justify-between">
                              <div>
                                <h3 className="text-sm font-medium text-primary">{item.product?.name || 'Product'}</h3>
                                <p className="mt-1 text-xs text-gray-500">{item.product?.category?.name || 'Uncategorized'}</p>
                              </div>
                              <div className="text-right">
                                <p className="text-sm font-medium text-primary">
                                  ${item.price.toFixed(2)}
                                </p>
                                <p className="mt-1 text-xs text-gray-500">
                                  Qty: {item.quantity}
                                </p>
                              </div>
                            </div>
                            <div className="mt-2">
                              <p className="text-xs text-gray-500">
                                Subtotal: ${(item.price * item.quantity).toFixed(2)}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    <div className="p-4 md:p-6 bg-gray-50">
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-600">Subtotal</span>
                        <span className="font-medium">${(order.total * 0.93).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-600">Shipping</span>
                        <span className="font-medium">${(order.total * 0.07).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-base font-medium pt-2 border-t border-gray-200 mt-2">
                        <span>Total</span>
                        <span>${order.total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 md:p-6 text-center">
                    <p className="text-gray-500">No items found for this order.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Order Information */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm mb-6">
                <div className="p-4 md:p-6 border-b border-gray-200">
                  <h2 className="text-lg font-medium">Order Information</h2>
                </div>
                <div className="p-4 md:p-6 space-y-4">
                  <div className="flex items-start gap-3">
                    <Calendar className="w-5 h-5 text-gray-500 mt-1" />
                    <div>
                      <h3 className="font-medium text-sm">Order Date</h3>
                      <p className="text-gray-600 text-sm">{formatDate(order.createdAt)}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-gray-500 mt-1" />
                    <div>
                      <h3 className="font-medium text-sm">Shipping Address</h3>
                      <p className="text-gray-600 text-sm">{order.address}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <CreditCard className="w-5 h-5 text-gray-500 mt-1" />
                    <div>
                      <h3 className="font-medium text-sm">Payment Method</h3>
                      <p className="text-gray-600 text-sm capitalize">{order.paymentMethod || 'Credit Card'}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-4 md:p-6 border-b border-gray-200">
                  <h2 className="text-lg font-medium">Need Help?</h2>
                </div>
                <div className="p-4 md:p-6">
                  <p className="text-sm text-gray-600 mb-4">
                    If you have any questions or concerns about your order, please contact our customer support.
                  </p>
                  <Button variant="outline" className="w-full">
                    Contact Support
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}