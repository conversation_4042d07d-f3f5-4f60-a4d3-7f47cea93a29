{"version": 2, "devCommand": "node simple-direct-server.js", "buildCommand": "npm run build", "env": {"PORT": "3000", "NODE_TLS_REJECT_UNAUTHORIZED": "0", "PGSSLMODE": "no-verify", "DISABLE_SSL_VALIDATION": "true"}, "builds": [{"src": "simple-direct-server.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "simple-direct-server.js"}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "simple-direct-server.js"}]}