import express from 'express';
import serverless from 'serverless-http';

// Create express app
const app = express();

// Middleware to parse JSON requests
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    platform: 'netlify',
    message: 'Fallback API running. Please set up database environment variables.'
  });
});

// Debug endpoint
app.get('/api/debug', (req, res) => {
  res.status(200).json({
    environment: process.env.NODE_ENV || 'development',
    platform: 'netlify',
    database: {
      status: 'disconnected',
      url: process.env.DATABASE_URL ? 'configured' : 'missing',
      host: process.env.POSTGRES_HOST || 'not configured'
    },
    envVars: {
      netlify: process.env.NETLIFY || 'not set',
      nodeEnv: process.env.NODE_ENV || 'not set',
    },
    timestamp: new Date().toISOString(),
    message: 'Fallback API running. Please set up database environment variables in Netlify dashboard.'
  });
});

// Default handler for all other routes
app.all('/api/*', (req, res) => {
  res.status(200).json({
    message: 'API endpoint reached successfully (fallback mode)',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString(),
    notice: 'Database connections are not available. Please set up environment variables in Netlify dashboard.'
  });
});

// Export the serverless handler
export const handler = serverless(app);
