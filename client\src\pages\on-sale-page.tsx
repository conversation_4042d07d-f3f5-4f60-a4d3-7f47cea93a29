
import { useQuery } from "@tanstack/react-query";
import { ProductCard } from "@/components/ui/product-card";
import { Skeleton } from "@/components/ui/skeleton";
import { Product } from "@shared/schema";
import { useTitle } from "react-use";

export default function OnSalePage() {
  useTitle("On Sale | Eco Shop");

  const { data: products, isLoading } = useQuery<Product[]>({
    queryKey: ["/api/products/sale"],
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">On Sale</h1>
        <p className="text-gray-600">
          Great deals on eco-friendly and sustainable products
        </p>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="rounded-lg overflow-hidden">
              <Skeleton className="h-48 w-full" />
              <div className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-2" />
                <Skeleton className="h-6 w-1/3" />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {products && products.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <h3 className="text-xl font-semibold mb-2">No sale items found</h3>
              <p className="text-gray-600">
                Check back soon for special discounts and promotions
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
