// Database initialization script for Vercel
import { db } from './db.js';
import * as schema from './schema.js';
import { sql } from 'drizzle-orm';

// Initialize the database with sample data
export async function initializeDatabase(force = false) {
  console.log("Initializing database with sample data...");

  try {
    // Check if tables have data
    let categoryCount = [{ count: '0' }];
    let productCount = [{ count: '0' }];

    try {
      categoryCount = await db.select({ count: sql`count(*)` }).from(schema.categories);
      productCount = await db.select({ count: sql`count(*)` }).from(schema.products);
    } catch (countError) {
      console.error('Error counting existing data:', countError);
      // Continue with initialization even if counting fails
    }

    console.log(`Current database state: ${parseInt(categoryCount[0]?.count || '0')} categories, ${parseInt(productCount[0]?.count || '0')} products`);

    // Skip initialization if there are products and force is false
    if (!force && parseInt(productCount[0]?.count || '0') > 0) {
      console.log("Database already has products, skipping initialization");
      return true;
    }

    // If force is true, clear existing data
    if (force) {
      console.log("Force initialization requested, clearing existing data...");
      try {
        // Delete existing data in reverse order of dependencies
        await db.delete(schema.wishlistItems);
        await db.delete(schema.cartItems);
        await db.delete(schema.orderItems);
        await db.delete(schema.orders);
        await db.delete(schema.products);
        await db.delete(schema.categories);
        await db.delete(schema.users);
        console.log("Existing data cleared successfully");
      } catch (clearError) {
        console.error("Error clearing existing data:", clearError);
        // Continue with initialization even if clearing fails
      }
    }

    console.log("Adding categories...");
    // Add categories
    await db.insert(schema.categories).values([
      {
        name: "Recycled Electronics",
        imageUrl: "https://images.unsplash.com/photo-1605792657660-596af9009e82?w=3840&q=100"
      },
      {
        name: "Upcycled Fashion",
        imageUrl: "https://images.unsplash.com/photo-1544441893-675973e31985?w=3840&q=100"
      },
      {
        name: "Eco-friendly Home",
        imageUrl: "https://images.unsplash.com/photo-1616486338812-3caddf4fb915?w=3840&q=100"
      },
      {
        name: "Recycled Accessories",
        imageUrl: "https://images.unsplash.com/photo-1630059897392-cf891829848c?w=3840&q=100"
      }
    ]);

    console.log("Adding products...");
    await db.insert(schema.products).values([
      {
        name: "Cork Yoga Mat",
        description: "Natural cork yoga mat that's eco-friendly, non-slip and antimicrobial",
        price: 49.99,
        discountPrice: null,
        rating: 4.7,
        reviewCount: 18,
        imageUrl: "https://images.unsplash.com/photo-1601925260368-ae2f83cf8b7f?w=3840&q=100",
        categoryId: 3,
        inStock: true,
        isNew: true,
        isPopular: false,
        isSale: false,
        createdAt: new Date()
      },
      {
        name: "Recycled Denim Jacket",
        description: "Vintage denim jacket reimagined with sustainable materials",
        price: 129.99,
        discountPrice: 99.99,
        rating: 4.7,
        reviewCount: 45,
        imageUrl: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=3840&q=100",
        categoryId: 2,
        inStock: true,
        isNew: false,
        isPopular: false,
        isSale: true,
        createdAt: new Date()
      },
      {
        name: "Upcycled Leather Tote",
        description: "Handcrafted tote bag made from reclaimed leather",
        price: 189.99,
        discountPrice: null,
        rating: 4.8,
        reviewCount: 67,
        imageUrl: "https://images.unsplash.com/photo-1590874103328-eac38a683ce7?w=3840&q=100",
        categoryId: 2,
        inStock: true,
        isNew: false,
        isPopular: true,
        isSale: false,
        createdAt: new Date()
      },
      {
        name: "Bamboo Kitchen Set",
        description: "Complete kitchen utensil set made from sustainable bamboo",
        price: 79.99,
        discountPrice: 59.99,
        rating: 4.9,
        reviewCount: 234,
        imageUrl: "https://images.unsplash.com/photo-1610701596007-11502861dcfa?w=3840&q=100",
        categoryId: 3,
        inStock: true,
        isNew: false,
        isPopular: true,
        isSale: false,
        createdAt: new Date()
      },
      {
        name: "Ocean Plastic Watch",
        description: "Stylish timepiece crafted from recycled ocean plastics",
        price: 159.99,
        discountPrice: null,
        rating: 4.8,
        reviewCount: 128,
        imageUrl: "https://images.unsplash.com/photo-1542496658-e33a6d0d50f6?w=3840&q=100",
        categoryId: 4,
        inStock: true,
        isNew: true,
        isPopular: false,
        isSale: false,
        createdAt: new Date()
      }
    ]);

    // Demo user and sample data have been removed

    console.log("Database initialization complete");
    return true;
  } catch (error) {
    console.error("Error initializing database:", error);
    // Return false instead of throwing error to prevent crashing the application
    return false;
  }
}
