import { Link } from "wouter";
import { Category } from "@shared/schema";

type CategoryCardProps = {
  category: Category;
};

export function CategoryCard({ category }: CategoryCardProps) {
  return (
    <Link href={`/products?category=${category.id}`}>
      <div className="group relative rounded-lg overflow-hidden shadow-sm h-44 sm:h-60">
        <img 
          src={category.imageUrl || `https://images.unsplash.com/photo-1589503678474-a6261be1b19d?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80`} 
          alt={category.name} 
          className="w-full h-full object-cover group-hover:scale-105 transition duration-300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-primary/70 to-transparent flex items-end p-4">
          <h3 className="text-white font-medium font-poppins">{category.name}</h3>
        </div>
      </div>
    </Link>
  );
}
