// Request batching utility to combine multiple API calls into fewer requests
// This helps reduce the total number of HTTP requests and improves performance

interface BatchRequest {
  url: string;
  method: string;
  data?: any;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

class RequestBatcher {
  private batchQueue: BatchRequest[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly batchDelay = 50; // 50ms delay to batch requests
  private readonly maxBatchSize = 10; // Maximum number of requests to batch

  // Add a request to the batch queue
  public addToBatch(url: string, method: string = 'GET', data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({ url, method, data, resolve, reject });

      // If we've reached the max batch size, process immediately
      if (this.batchQueue.length >= this.maxBatchSize) {
        this.processBatch();
      } else {
        // Otherwise, set a timeout to process the batch
        this.scheduleBatchProcessing();
      }
    });
  }

  private scheduleBatchProcessing() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.processBatch();
    }, this.batchDelay);
  }

  private async processBatch() {
    if (this.batchQueue.length === 0) return;

    // Clear the timeout
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    // Get the current batch and clear the queue
    const currentBatch = [...this.batchQueue];
    this.batchQueue = [];

    // Group requests by method and similar endpoints
    const groupedRequests = this.groupRequests(currentBatch);

    // Process each group
    for (const group of groupedRequests) {
      await this.processRequestGroup(group);
    }
  }

  private groupRequests(requests: BatchRequest[]): BatchRequest[][] {
    const groups: { [key: string]: BatchRequest[] } = {};

    for (const request of requests) {
      // Create a group key based on method and base endpoint
      const baseUrl = request.url.split('?')[0]; // Remove query parameters
      const groupKey = `${request.method}:${baseUrl}`;

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(request);
    }

    return Object.values(groups);
  }

  private async processRequestGroup(requests: BatchRequest[]) {
    // If it's a single request, process normally
    if (requests.length === 1) {
      const request = requests[0];
      try {
        const response = await this.makeRequest(request.url, request.method, request.data);
        request.resolve(response);
      } catch (error) {
        request.reject(error);
      }
      return;
    }

    // For multiple similar requests, try to optimize
    const firstRequest = requests[0];
    const baseUrl = firstRequest.url.split('?')[0];

    // Special handling for product requests
    if (baseUrl.includes('/api/products') && firstRequest.method === 'GET') {
      await this.batchProductRequests(requests);
    } else {
      // For other requests, process them individually but in parallel
      await Promise.allSettled(
        requests.map(async (request) => {
          try {
            const response = await this.makeRequest(request.url, request.method, request.data);
            request.resolve(response);
          } catch (error) {
            request.reject(error);
          }
        })
      );
    }
  }

  private async batchProductRequests(requests: BatchRequest[]) {
    // Extract product IDs from individual product requests
    const productIds: number[] = [];
    const individualRequests: BatchRequest[] = [];

    for (const request of requests) {
      const match = request.url.match(/\/api\/products\/(\d+)$/);
      if (match) {
        productIds.push(parseInt(match[1]));
      } else {
        individualRequests.push(request);
      }
    }

    // If we have multiple product ID requests, batch them
    if (productIds.length > 1) {
      try {
        const batchUrl = `/api/products/batch?ids=${productIds.join(',')}`;
        const batchResponse = await this.makeRequest(batchUrl, 'GET');
        
        // Distribute the batched response to individual requests
        for (let i = 0; i < productIds.length; i++) {
          const productId = productIds[i];
          const request = requests.find(r => r.url.includes(`/products/${productId}`));
          if (request && batchResponse[productId]) {
            request.resolve(batchResponse[productId]);
          } else if (request) {
            request.reject(new Error(`Product ${productId} not found in batch response`));
          }
        }
      } catch (error) {
        // If batch request fails, fall back to individual requests
        for (const request of requests) {
          if (request.url.match(/\/api\/products\/\d+$/)) {
            try {
              const response = await this.makeRequest(request.url, request.method, request.data);
              request.resolve(response);
            } catch (err) {
              request.reject(err);
            }
          }
        }
      }
    }

    // Process any remaining individual requests
    for (const request of individualRequests) {
      try {
        const response = await this.makeRequest(request.url, request.method, request.data);
        request.resolve(response);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  private async makeRequest(url: string, method: string, data?: any): Promise<any> {
    const options: RequestInit = {
      method,
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }
}

// Create a singleton instance
export const requestBatcher = new RequestBatcher();

// Helper function to use the batcher
export const batchedFetch = (url: string, method: string = 'GET', data?: any): Promise<any> => {
  return requestBatcher.addToBatch(url, method, data);
};
