import { ChevronRight, Leaf, BarChart, Home, ShoppingBag, Car, Utensils, Droplets, Zap, ThumbsUp, Send } from "lucide-react";
import { ContentOnlyLayout } from "@/components/layout/content-only-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";

interface SustainabilityTip {
  id: number;
  title: string;
  description: string;
  impact: string;
  difficulty: "Easy" | "Medium" | "Advanced";
  category: "home" | "shopping" | "transport" | "food" | "water" | "energy";
  icon: JSX.Element;
}

const sustainabilityTips: SustainabilityTip[] = [
  {
    id: 1,
    title: "Use Reusable Shopping Bags",
    description: "Switch to cloth or other reusable bags instead of single-use plastic bags. Keep several in your car or by the door so you don't forget them when shopping.",
    impact: "Reduces plastic waste and petroleum consumption. A single reusable bag can replace hundreds of plastic bags over its lifetime.",
    difficulty: "Easy",
    category: "shopping",
    icon: <ShoppingBag className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 2,
    title: "Install a Water-Saving Showerhead",
    description: "Replace your standard showerhead with a low-flow or water-saving model. Modern efficient showerheads provide great pressure while using much less water.",
    impact: "Can reduce shower water usage by up to 40%, saving thousands of liters of water annually per household.",
    difficulty: "Easy",
    category: "water",
    icon: <Droplets className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 3,
    title: "Switch to LED Light Bulbs",
    description: "Replace incandescent and CFL bulbs with LED alternatives. They last longer, use less energy, and are available in a variety of color temperatures.",
    impact: "LEDs use up to 90% less energy than incandescent bulbs and last 15-25 times longer, significantly reducing energy consumption and waste.",
    difficulty: "Easy",
    category: "energy",
    icon: <Zap className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 4,
    title: "Start Composting Food Scraps",
    description: "Set up a compost bin for fruit and vegetable scraps, coffee grounds, and yard waste. Turn waste into nutrient-rich soil for your garden or houseplants.",
    impact: "Diverts up to 30% of household waste from landfills and reduces methane emissions, while creating free, high-quality soil amendment.",
    difficulty: "Medium",
    category: "food",
    icon: <Utensils className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 5,
    title: "Reduce Meat Consumption",
    description: "Incorporate more plant-based meals into your diet. Try having meat-free days each week or using meat as a side rather than the main component of meals.",
    impact: "Livestock production accounts for approximately 14.5% of global greenhouse gas emissions. Even small reductions in meat consumption can have significant environmental benefits.",
    difficulty: "Medium",
    category: "food",
    icon: <Utensils className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 6,
    title: "Use Public Transportation or Carpool",
    description: "Leave your car at home and take public transportation, bike, or walk when possible. For longer commutes, arrange carpools with colleagues or neighbors.",
    impact: "Reduces carbon emissions, traffic congestion, and air pollution. Taking public transit instead of driving can reduce your carbon footprint by up to 30%.",
    difficulty: "Medium",
    category: "transport",
    icon: <Car className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 7,
    title: "Install a Programmable Thermostat",
    description: "Use a smart or programmable thermostat to automatically adjust your home's temperature when you're asleep or away. Set it a few degrees lower in winter and higher in summer.",
    impact: "Can reduce heating and cooling energy usage by 10-15%, saving money and reducing greenhouse gas emissions.",
    difficulty: "Medium",
    category: "home",
    icon: <Home className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 8,
    title: "Shop Secondhand First",
    description: "Before buying new, check thrift stores, online marketplaces, and community exchange groups for used options. Clothing, furniture, and electronics are often available gently used.",
    impact: "Extends the lifecycle of products, reduces manufacturing demand, and keeps usable items out of landfills.",
    difficulty: "Easy",
    category: "shopping",
    icon: <ShoppingBag className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 9,
    title: "Install Low-Flow Toilets",
    description: "Upgrade to water-efficient toilets or install a dual-flush conversion kit on your existing toilet. These use significantly less water per flush.",
    impact: "Low-flow toilets can save up to 16,000 liters of water per year for a family of four, reducing water consumption and utility bills.",
    difficulty: "Advanced",
    category: "water",
    icon: <Droplets className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 10,
    title: "Install Solar Panels",
    description: "Consider adding solar panels to your home to generate clean, renewable electricity. Many regions offer incentives and tax credits for solar installation.",
    impact: "Dramatically reduces or eliminates electricity-related carbon emissions. A typical home solar system can offset 100,000+ kg of carbon dioxide over its lifetime.",
    difficulty: "Advanced",
    category: "energy",
    icon: <Zap className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 11,
    title: "Create a Rain Garden",
    description: "Build a rain garden in a low spot in your yard using native plants. This captures rainwater runoff and allows it to slowly infiltrate into the ground.",
    impact: "Reduces stormwater runoff, prevents erosion, filters pollutants, and provides habitat for pollinators and wildlife.",
    difficulty: "Advanced",
    category: "home",
    icon: <Home className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 12,
    title: "Use Electric or Manual Yard Equipment",
    description: "Replace gas-powered lawn mowers, leaf blowers, and trimmers with electric or manual alternatives. Battery-powered options are increasingly powerful and affordable.",
    impact: "Gas-powered yard equipment produces significant air pollution. Electric equipment produces no direct emissions and much less noise pollution.",
    difficulty: "Medium",
    category: "home",
    icon: <Home className="h-8 w-8 text-[#8bbe1b]" />
  }
];

const SustainabilityPage = () => {
  return (
    <ContentOnlyLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-[#8bbe1b] transition-colors">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <span>Sustainability Tips</span>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
              Sustainability Tips & Practices
            </h1>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Small changes in our daily habits can make a big difference for the planet. 
              Explore our collection of practical sustainability tips that help reduce your 
              environmental footprint while often saving money too.
            </p>
          </div>

          <div className="bg-amber-50 border border-amber-100 rounded-lg p-6 mb-12">
            <div className="flex items-center gap-4 mb-4">
              <div className="bg-[#8bbe1b]/20 p-3 rounded-full">
                <Leaf className="w-6 h-6 text-[#8bbe1b]" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">Why Sustainable Living Matters</h2>
            </div>
            <p className="text-gray-600 mb-6">
              Living sustainably isn't just good for the planet—it's often better for your health, wellbeing, and wallet too. 
              By making thoughtful choices about how we consume, move, and live, we can collectively reduce pollution, conserve 
              natural resources, and protect biodiversity while building more resilient communities.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg border border-amber-100 flex flex-col items-center text-center">
                <BarChart className="h-8 w-8 text-[#8bbe1b] mb-2" />
                <h3 className="font-semibold text-gray-800 mb-1">Reduce Carbon Footprint</h3>
                <p className="text-xs text-gray-600">Lower greenhouse gas emissions that contribute to climate change</p>
              </div>
              <div className="bg-white p-4 rounded-lg border border-amber-100 flex flex-col items-center text-center">
                <Droplets className="h-8 w-8 text-[#8bbe1b] mb-2" />
                <h3 className="font-semibold text-gray-800 mb-1">Conserve Resources</h3>
                <p className="text-xs text-gray-600">Preserve water, energy, and raw materials for future generations</p>
              </div>
              <div className="bg-white p-4 rounded-lg border border-amber-100 flex flex-col items-center text-center">
                <ThumbsUp className="h-8 w-8 text-[#8bbe1b] mb-2" />
                <h3 className="font-semibold text-gray-800 mb-1">Save Money</h3>
                <p className="text-xs text-gray-600">Many sustainable practices reduce utility bills and other expenses</p>
              </div>
              <div className="bg-white p-4 rounded-lg border border-amber-100 flex flex-col items-center text-center">
                <Leaf className="h-8 w-8 text-[#8bbe1b] mb-2" />
                <h3 className="font-semibold text-gray-800 mb-1">Healthier Living</h3>
                <p className="text-xs text-gray-600">Eco-friendly choices often lead to healthier environments and lifestyles</p>
              </div>
            </div>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Sustainable Living Tips</h2>
            
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="w-full grid grid-cols-3 md:grid-cols-7 mb-6">
                <TabsTrigger value="all" className="text-xs">All Tips</TabsTrigger>
                <TabsTrigger value="home" className="text-xs">Home</TabsTrigger>
                <TabsTrigger value="shopping" className="text-xs">Shopping</TabsTrigger>
                <TabsTrigger value="transport" className="text-xs">Transport</TabsTrigger>
                <TabsTrigger value="food" className="text-xs">Food</TabsTrigger>
                <TabsTrigger value="water" className="text-xs">Water</TabsTrigger>
                <TabsTrigger value="energy" className="text-xs">Energy</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {sustainabilityTips.map((tip) => (
                    <SustainabilityTipCard key={tip.id} tip={tip} />
                  ))}
                </div>
              </TabsContent>

              {["home", "shopping", "transport", "food", "water", "energy"].map((category) => (
                <TabsContent key={category} value={category}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {sustainabilityTips
                      .filter((tip) => tip.category === category)
                      .map((tip) => (
                        <SustainabilityTipCard key={tip.id} tip={tip} />
                      ))
                    }
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </div>

          <div className="bg-[#8bbe1b]/10 rounded-lg p-6 mb-12">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="md:w-2/3">
                <h3 className="text-xl font-bold mb-3 text-gray-800">Track Your Impact</h3>
                <p className="text-gray-600 mb-4">
                  Sign up for our sustainability newsletter to receive monthly challenges, 
                  personalized tips, and tools to track your environmental impact. Join our 
                  community of eco-conscious individuals making a difference.
                </p>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Input 
                    type="email" 
                    placeholder="Your email address" 
                    className="bg-white border-amber-200"
                  />
                  <Button className="bg-[#8bbe1b] hover:bg-[#7aa518] text-white flex gap-2 items-center whitespace-nowrap">
                    <Send className="h-4 w-4" />
                    <span>Subscribe</span>
                  </Button>
                </div>
              </div>
              <div className="md:w-1/3 flex justify-center">
                <div className="bg-white p-4 rounded-full border-2 border-[#8bbe1b] h-32 w-32 flex items-center justify-center flex-col">
                  <Leaf className="h-10 w-10 text-[#8bbe1b] mb-1" />
                  <span className="text-xs text-center text-gray-600">Join 5,000+ eco-conscious subscribers</span>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h3 className="text-xl font-bold mb-4 text-gray-800">Explore Related Sustainability Resources</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/recycling-guide" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Recycling Guide</h4>
                <p className="text-sm text-gray-600">Learn how to recycle properly</p>
              </Link>
              <Link href="/dropoff-locations" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Drop-off Locations</h4>
                <p className="text-sm text-gray-600">Find nearby recycling centers</p>
              </Link>
              <Link href="/trade-in" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Trade-in Program</h4>
                <p className="text-sm text-gray-600">Exchange your old items</p>
              </Link>
              <Link href="/repair-services" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Repair Services</h4>
                <p className="text-sm text-gray-600">Fix instead of replace</p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ContentOnlyLayout>
  );
};

interface SustainabilityTipCardProps {
  tip: SustainabilityTip;
}

const SustainabilityTipCard = ({ tip }: SustainabilityTipCardProps) => {
  return (
    <Card className="border-amber-100 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-3">
          {tip.icon}
          <div>
            <CardTitle className="text-lg">{tip.title}</CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <span className={`text-xs px-2 py-0.5 rounded-full ${
                tip.difficulty === "Easy" 
                  ? "bg-green-100 text-green-700" 
                  : tip.difficulty === "Medium"
                  ? "bg-yellow-100 text-yellow-700"
                  : "bg-orange-100 text-orange-700"
              }`}>
                {tip.difficulty}
              </span>
              <CardDescription>{tip.category.charAt(0).toUpperCase() + tip.category.slice(1)}</CardDescription>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p className="text-gray-600 text-sm">{tip.description}</p>
          <div className="bg-[#8bbe1b]/10 p-3 rounded text-sm">
            <span className="font-semibold text-[#6a9816]">Impact:</span>{" "}
            <span className="text-gray-700">{tip.impact}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SustainabilityPage;