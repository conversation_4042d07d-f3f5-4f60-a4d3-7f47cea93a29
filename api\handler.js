// Simplified API handler for Vercel
import express from 'express';
import { db, pingDatabase } from './db.js';
import * as schema from './schema.js';
import { eq, sql } from 'drizzle-orm';
import { initializeDatabase } from './init-db.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { addEndpoints } from './endpoints.js';

// Create Express app
const app = express();
app.use(express.json());

// Basic middleware for logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`);
  next();
});

// Initialize database endpoint
app.get('/api/init-db', async (req, res) => {
  try {
    const force = req.query.force === 'true';
    console.log(`Database initialization requested (force=${force})`);
    const result = await initializeDatabase(force);
    res.json({
      status: 'ok',
      message: 'Database initialization successfully',
      force,
      result
    });

    // Log database state after initialization
    const categoryCount = await db.select({ count: sql`count(*)` }).from(schema.categories);
    const productCount = await db.select({ count: sql`count(*)` }).from(schema.products);
    const userCount = await db.select({ count: sql`count(*)` }).from(schema.users);
    console.log(`Database state after initialization: ${parseInt(categoryCount[0]?.count || '0')} categories, ${parseInt(productCount[0]?.count || '0')} products, ${parseInt(userCount[0]?.count || '0')} users`);
  } catch (error) {
    console.error('Database initialization failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Database initialization failed',
      error: error.message
    });
  }
});

// Debug endpoint to check database connection and environment
app.get('/api/debug', async (req, res) => {
  try {
    // Get environment variables
    const env = {
      NODE_ENV: process.env.NODE_ENV || 'development',
      VERCEL: process.env.VERCEL ? true : false,
      DATABASE_URL: process.env.DATABASE_URL ? '✓ Present' : '✗ Missing',
      POSTGRES_URL: process.env.POSTGRES_URL ? '✓ Present' : '✗ Missing',
      NODE_TLS_REJECT_UNAUTHORIZED: process.env.NODE_TLS_REJECT_UNAUTHORIZED,
      PGSSLMODE: process.env.PGSSLMODE,
      PG_SSL_REJECT_UNAUTHORIZED: process.env.PG_SSL_REJECT_UNAUTHORIZED
    };

    // Test database connection
    let dbStatus = 'unknown';
    let dbError = null;
    let tables = {};

    try {
      // Check if we can connect to the database
      await pingDatabase();
      dbStatus = 'connected';

      // Try to count records in each table
      const userCount = await db.select({ count: sql`count(*)` }).from(schema.users);
      const productCount = await db.select({ count: sql`count(*)` }).from(schema.products);
      const categoryCount = await db.select({ count: sql`count(*)` }).from(schema.categories);

      tables = {
        users: userCount[0]?.count || 0,
        products: productCount[0]?.count || 0,
        categories: categoryCount[0]?.count || 0
      };
    } catch (error) {
      dbStatus = 'error';
      dbError = error.message;
    }

    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: env,
      database: {
        status: dbStatus,
        error: dbError,
        tables
      }
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Debug check failed',
      error: error.message
    });
  }
});

// Direct database initialization endpoint
app.get('/api/direct-init-db', async (req, res) => {
  try {
    // Import the standalone database initialization script
    const { default: initializeDatabase } = await import('../standalone-db-init.js');

    // Run the initialization
    const result = await initializeDatabase();

    if (result) {
      return res.status(200).json({ message: 'Database initialized successfully' });
    } else {
      return res.status(500).json({ message: 'Database initialization failed' });
    }
  } catch (error) {
    console.error('Error initializing database:', error);
    return res.status(500).json({ message: 'Database initialization failed', error: error.message });
  }
});

// Database initialization endpoint
app.get('/api/init-db', async (req, res) => {
  try {
    const force = req.query.force === 'true';
    const { initializeDatabase } = await import('./init-db.js');

    console.log(`Initializing database with force=${force}`);
    const result = await initializeDatabase(force);

    if (result) {
      return res.status(200).json({ message: 'Database initialized successfully' });
    } else {
      return res.status(500).json({ message: 'Database initialization failed' });
    }
  } catch (error) {
    console.error('Error initializing database:', error);
    return res.status(500).json({ message: 'Database initialization failed', error: error.message });
  }
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const dbConnected = await pingDatabase();
    res.json({
      status: 'ok',
      database: dbConnected ? 'connected' : 'disconnected',
      environment: process.env.NODE_ENV || 'development',
      vercel: process.env.VERCEL ? true : false
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      error: error.message
    });
  }
});

// Basic implementation of endpoints (will be overridden by endpoints.js if it loads correctly)

// Categories endpoint
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await db.select().from(schema.categories);

    if (!categories || categories.length === 0) {
      // Return sample categories if none are found
      console.log('No categories found, returning sample data');
      return res.json([
        { id: 1, name: 'Recycled Electronics', imageUrl: 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg' },
        { id: 2, name: 'Upcycled Fashion', imageUrl: 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg' },
        { id: 3, name: 'Eco-friendly Home', imageUrl: 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg' },
        { id: 4, name: 'Recycled Accessories', imageUrl: 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg' }
      ]);
    }

    console.log(`Fetched ${categories.length} categories`);
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    // Return sample categories on error
    console.log('Error fetching categories, returning sample data');
    return res.json([
      { id: 1, name: 'Recycled Electronics', imageUrl: 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg' },
      { id: 2, name: 'Upcycled Fashion', imageUrl: 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg' },
      { id: 3, name: 'Eco-friendly Home', imageUrl: 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg' },
      { id: 4, name: 'Recycled Accessories', imageUrl: 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg' }
    ]);
  }
});

// Products endpoint
app.get('/api/products', async (req, res) => {
  try {
    const products = await db.select().from(schema.products);

    if (!products || products.length === 0) {
      // Return sample products if none are found
      console.log('No products found, returning sample data');
      return res.json([
        {
          id: 1,
          name: "Refurbished Smartphone",
          description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
          price: 329.99,
          discountPrice: 299.99,
          rating: 4.5,
          reviewCount: 34,
          imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
          categoryId: 1,
          inStock: true,
          isNew: true,
          isPopular: false,
          isSale: true
        },
        {
          id: 2,
          name: "Upcycled Denim Jacket",
          description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
          price: 89.99,
          discountPrice: null,
          rating: 4.8,
          reviewCount: 42,
          imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
          categoryId: 2,
          inStock: true,
          isNew: true,
          isPopular: true,
          isSale: false
        },
        {
          id: 3,
          name: "Recycled Metal Water Bottle",
          description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
          price: 29.99,
          discountPrice: 24.99,
          rating: 4.9,
          reviewCount: 120,
          imageUrl: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg",
          categoryId: 4,
          inStock: true,
          isNew: false,
          isPopular: true,
          isSale: true
        },
        {
          id: 4,
          name: "Bamboo Desk Organizer",
          description: "Sustainable bamboo desk organizer with multiple compartments for all your office essentials.",
          price: 49.99,
          discountPrice: null,
          rating: 4.6,
          reviewCount: 28,
          imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg",
          categoryId: 3,
          inStock: true,
          isNew: false,
          isPopular: false,
          isSale: false
        }
      ]);
    }

    console.log(`Fetched ${products.length} products`);
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    // Return sample products on error
    console.log('Error fetching products, returning sample data');
    return res.json([
      {
        id: 1,
        name: "Refurbished Smartphone",
        description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
        price: 329.99,
        discountPrice: 299.99,
        rating: 4.5,
        reviewCount: 34,
        imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
        categoryId: 1,
        inStock: true,
        isNew: true,
        isPopular: false,
        isSale: true
      },
      {
        id: 2,
        name: "Upcycled Denim Jacket",
        description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
        price: 89.99,
        discountPrice: null,
        rating: 4.8,
        reviewCount: 42,
        imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
        categoryId: 2,
        inStock: true,
        isNew: true,
        isPopular: true,
        isSale: false
      }
    ]);
  }
});

// Product by ID endpoint
app.get('/api/products/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const [product] = await db.select().from(schema.products).where(eq(schema.products.id, id));

    if (!product) {
      // If product not found, return a sample product for testing
      console.log(`Product with ID ${id} not found, returning sample product`);
      return res.json({
        id: id,
        name: "Sample Product",
        description: "This is a sample product for testing purposes.",
        price: 99.99,
        discountPrice: 79.99,
        rating: 4.5,
        reviewCount: 10,
        imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg",
        categoryId: 1,
        inStock: true,
        isNew: true,
        isPopular: true,
        isSale: false,
        createdAt: new Date()
      });
    }

    res.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ message: 'Failed to fetch product' });
  }
});

// Logout endpoint
app.post('/api/logout', async (req, res) => {
  try {
    // In a real app, we would invalidate the session
    // For now, just return success
    res.status(200).json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Error logging out:', error);
    res.status(500).json({ message: 'Failed to logout', error: error.message });
  }
});

// User endpoint
app.get('/api/user', async (req, res) => {
  try {
    // Get user ID from query parameter
    const userId = req.query.userId;

    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Find user by ID
    const [user] = await db.select().from(schema.users).where(eq(schema.users.id, parseInt(userId)));

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Return user data (excluding password)
    const { password: _, ...userData } = user;
    res.json(userData);
  } catch (error) {
    console.error('Error fetching current user:', error);
    res.status(500).json({ message: 'Failed to fetch user', error: error.message });
  }
});

// Featured products endpoint
app.get('/api/products/featured', async (req, res) => {
  try {
    const products = await db.select()
      .from(schema.products)
      .where(eq(schema.products.isPopular, true))
      .limit(8);

    res.json(products);
  } catch (error) {
    console.error('Error fetching featured products:', error);
    res.status(500).json({ message: 'Failed to fetch featured products' });
  }
});

// New arrivals endpoint
app.get('/api/products/new', async (req, res) => {
  try {
    const products = await db.select()
      .from(schema.products)
      .where(eq(schema.products.isNew, true))
      .limit(8);

    res.json(products);
  } catch (error) {
    console.error('Error fetching new products:', error);
    res.status(500).json({ message: 'Failed to fetch new products' });
  }
});

// Placeholder image endpoint
app.get('/api/placeholder/:width/:height', async (req, res) => {
  try {
    const { width, height } = req.params;
    const color = req.query.color || 'cccccc';
    const text = req.query.text || `${width}x${height}`;

    // Generate SVG placeholder
    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
        <rect width="${width}" height="${height}" fill="#${color}" />
        <text x="50%" y="50%" font-family="Arial" font-size="${Math.min(parseInt(width), parseInt(height)) / 10}px" fill="#666666" text-anchor="middle" dominant-baseline="middle">${text}</text>
      </svg>
    `;

    // Set headers
    res.setHeader('Content-Type', 'image/svg+xml');
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

    // Send SVG
    res.send(svg);
  } catch (error) {
    console.error('Error generating placeholder image:', error);
    res.status(500).json({ message: 'Failed to generate placeholder image', error: error.message });
  }
});

// User registration endpoint
app.post('/api/register', async (req, res) => {
  try {
    const { username, password, email, fullName } = req.body;
    console.log('Registration attempt:', { username, email });

    if (!username || !password || !email) {
      return res.status(400).json({ message: 'Username, password, and email are required' });
    }

    // Check if username already exists
    const existingUser = await db.select().from(schema.users).where(eq(schema.users.username, username));
    if (existingUser.length > 0) {
      return res.status(409).json({ message: 'Username already exists' });
    }

    // Create new user
    const newUser = {
      username,
      password, // In a real app, you would hash this password
      email,
      fullName: fullName || '',
      isLoggedIn: true
    };

    // Insert user into database
    const result = await db.insert(schema.users).values(newUser).returning();

    // Return user data (excluding password)
    const { password: _, ...userData } = result[0] || newUser;
    console.log('User registered successfully:', username);
    res.status(201).json({
      message: 'Registration successful',
      user: userData
    });
  } catch (error) {
    console.error('Error during registration:', error);
    res.status(500).json({ message: 'Registration failed', error: error.message });
  }
});

// Login endpoint
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('Login attempt:', { username });

    if (!username || !password) {
      return res.status(400).json({ message: 'Username and password are required' });
    }

    // Find user by username
    const [user] = await db.select().from(schema.users).where(eq(schema.users.username, username));

    if (!user) {
      console.log('User not found:', username);
      return res.status(401).json({ message: 'Invalid username or password' });
    }

    // In a real app, you would hash the password and compare it
    // For simplicity, we're just comparing the plain text password
    if (user.password !== password) {
      console.log('Invalid password for user:', username);
      return res.status(401).json({ message: 'Invalid username or password' });
    }

    // Update user login status
    await db.update(schema.users)
      .set({ isLoggedIn: true })
      .where(eq(schema.users.id, user.id));

    // Return user data (excluding password)
    const { password: _, ...userData } = user;
    console.log('User logged in successfully:', username);
    res.json({
      message: 'Login successful',
      user: userData
    });
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ message: 'Login failed', error: error.message });
  }
});

// Serve the actual frontend from the dist directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Try to find the dist directory
const possibleDistPaths = [
  path.join(process.cwd(), 'dist'),
  path.join(__dirname, '../dist'),
  path.join(process.cwd(), '../dist')
];

let distDir = '';
for (const dir of possibleDistPaths) {
  if (fs.existsSync(dir)) {
    distDir = dir;
    console.log(`Found frontend build directory at ${dir}`);
    break;
  }
}

if (!distDir) {
  console.warn('Could not find frontend build directory. Using fallback.');
  distDir = path.join(__dirname, 'public');
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
    console.log(`Created fallback public directory at ${distDir}`);
  }
}

// Serve static files from the dist directory
app.use(express.static(distDir));
console.log(`Serving static files from ${distDir}`);

// Serve index.html for all routes that don't match API routes
// This is important for SPA routing
app.get('*', (req, res, next) => {
  // Skip API routes
  if (req.path.startsWith('/api/')) {
    return next();
  }

  const indexPath = path.join(distDir, 'index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
    console.log(`Serving frontend for path: ${req.path}`);
  } else {
    // Fallback if index.html doesn't exist
    res.send('<h1>Ecommerce App</h1><p>Frontend build not found. Please build the frontend first.</p>');
  }
});

// Try to add all additional endpoints from endpoints.js
try {
  console.log('Attempting to load additional endpoints from endpoints.js...');
  addEndpoints(app);
  console.log('Successfully loaded additional endpoints from endpoints.js');
} catch (error) {
  console.error('Error loading additional endpoints from endpoints.js:', error);
  console.log('Using basic endpoint implementations as fallback');
}

// API 404 handler for API routes only
app.use('/api/*', (req, res) => {
  res.status(404).json({ message: 'API endpoint not found' });
});

export default app;
