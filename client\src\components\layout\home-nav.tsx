import { Link } from "wouter";
import { useEffect, useRef, useState } from "react";

type HomeNavProps = {
  homeNavMerged: boolean;
  onNavMergeChange?: (isMerged: boolean) => void;
};

export function HomeNav({ homeNavMerged, onNavMergeChange }: HomeNavProps) {
  const homeNavRef = useRef<HTMLDivElement>(null);
  
  // Effect for tracking scroll position and updating nav visibility
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const shouldMerge = scrollPosition > 150;
      
      if (onNavMergeChange && shouldMerge !== homeNavMerged) {
        onNavMergeChange(shouldMerge);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [homeNavMerged, onNavMergeChange]);
  
  return (
    <div 
      ref={homeNavRef}
      className={`flex items-center space-x-2 transition-all duration-500 ${
        homeNavMerged ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100'
      }`}
    >
      <Link href="/new-arrivals" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1">
        Recently Landed
      </Link>
      <Link href="/on-sale" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1">
        On Sale
      </Link>
      <Link href="/popular-items" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1">
        Popular
      </Link>
    </div>
  );
}