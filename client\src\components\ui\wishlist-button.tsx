import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useWishlist } from "@/hooks/use-wishlist";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { Heart, Loader2 } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

interface WishlistButtonProps {
  productId: number;
  variant?: "default" | "outline" | "ghost";
  size?: "icon" | "default" | "sm" | "lg";
  className?: string;
}

export function WishlistButton({
  productId,
  variant = "outline",
  size = "icon",
  className = "",
}: WishlistButtonProps) {
  const { user } = useAuth();
  const [, navigate] = useLocation();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const [isInWishlistState, setIsInWishlistState] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Check if product is in wishlist
  const {
    data: wishlistCheckData,
    isLoading: isCheckingWishlist
  } = useQuery({
    queryKey: [`/api/wishlist/check/${productId}`, user ? { userId: user.id } : null],
    enabled: !!user && !!productId,
    // Store userId in localStorage for future requests
    onSuccess: () => {
      if (user && user.id) {
        localStorage.setItem('userId', user.id.toString());
      }
    }
  });

  // Get wishlist items to find the item ID if needed
  const {
    data: wishlistItems
  } = useQuery({
    queryKey: ["/api/wishlist"],
    enabled: !!user,
  });

  useEffect(() => {
    if (wishlistCheckData && typeof wishlistCheckData === 'object' && 'isInWishlist' in wishlistCheckData) {
      setIsInWishlistState(!!wishlistCheckData.isInWishlist);
    } else if (user && wishlistItems && Array.isArray(wishlistItems)) {
      // Fallback method: check if product exists in the wishlist items
      const productInWishlist = isInWishlist(productId);
      setIsInWishlistState(productInWishlist);
    }
  }, [wishlistCheckData, wishlistItems, user, productId, isInWishlist]);

  const handleWishlistClick = async () => {
    if (!user) {
      navigate("/auth");
      return;
    }

    setIsLoading(true);

    try {
      if (isInWishlistState) {
        // Use the product ID directly to remove from wishlist
        console.log(`Removing product with ID: ${productId} from wishlist for user: ${user.id}`);

        const response = await fetch(`/api/wishlist/${productId}?userId=${user.id}`, {
          method: 'DELETE',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        // Log the response status
        console.log(`Wishlist item removal response status: ${response.status}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Error response from server (${response.status}):`, errorText);
          throw new Error(`Failed to remove from wishlist: ${response.status}`);
        }

        // Try to parse the response as JSON
        try {
          const responseData = await response.json();
          console.log('Wishlist item removal response:', responseData);
        } catch (e) {
          console.log('No JSON response from wishlist item removal');
        }

        setIsInWishlistState(false);
        toast({
          title: "Removed from wishlist",
          description: "Item removed from your wishlist.",
        });
      } else {
        // Direct API call to ensure userId is included
        console.log(`Adding product ${productId} to wishlist for user ${user.id}`);

        const response = await fetch('/api/wishlist', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: user.id,
            productId: productId
          })
        });

        // Log the response status
        console.log(`Wishlist item add response status: ${response.status}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Error response from server (${response.status}):`, errorText);
          throw new Error(`Failed to add to wishlist: ${response.status}`);
        }

        // Try to parse the response as JSON
        try {
          const responseData = await response.json();
          console.log('Wishlist item add response:', responseData);
        } catch (e) {
          console.log('No JSON response from wishlist item add');
        }

        setIsInWishlistState(true);
        toast({
          title: "Added to wishlist",
          description: "Item added to your wishlist successfully.",
        });
      }

      // Invalidate both queries to ensure data is fresh
      queryClient.invalidateQueries({ queryKey: [`/api/wishlist/check/${productId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/wishlist/check/${productId}`, user ? { userId: user.id } : null] });
      queryClient.invalidateQueries({ queryKey: ["/api/wishlist"] });
      queryClient.invalidateQueries({ queryKey: ["/api/wishlist", user ? { userId: user.id } : null] });
    } catch (error) {
      console.error("Wishlist operation failed:", error);
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleWishlistClick}
      className={`${className} ${isInWishlistState ? "text-red-500 hover:text-red-600" : ""}`}
      type="button"
      aria-label={isInWishlistState ? "Remove from wishlist" : "Add to wishlist"}
      disabled={isLoading || isCheckingWishlist}
    >
      {(isLoading || isCheckingWishlist) ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Heart className={`h-4 w-4 ${isInWishlistState ? "fill-current" : ""}`} />
      )}
    </Button>
  );
}