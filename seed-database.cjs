// This script can be run manually to seed the database
// Using CommonJS format for compatibility
require('dotenv').config();

// Set up database connection
const { Client } = require('pg');

async function seedDatabase() {
  console.log('Starting database seeding process...');
  
  try {
    // Create Postgres client
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is not set');
    }
    
    const client = new Client({ connectionString });
    await client.connect();
    
    console.log('Connected to database');

    // Check if categories table has data
    const categoriesResult = await client.query('SELECT COUNT(*) FROM categories');
    const categoriesCount = parseInt(categoriesResult.rows[0].count);
    
    if (categoriesCount === 0) {
      console.log("Adding categories...");
      // Add sample data for categories
      const categoryValues = [
        ['Recycled Electronics', 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'],
        ['Upcycled Fashion', 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'],
        ['Eco-friendly Home', 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'],
        ['Recycled Accessories', 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1']
      ];
      
      for (const [name, imageUrl] of categoryValues) {
        await client.query(
          'INSERT INTO categories (name, image_url) VALUES ($1, $2)',
          [name, imageUrl]
        );
      }
      
      console.log("Categories added successfully!");
    } else {
      console.log(`Categories already exist: ${categoriesCount} categories found`);
    }
    
    // Check if products table has data
    const productsResult = await client.query('SELECT COUNT(*) FROM products');
    const productsCount = parseInt(productsResult.rows[0].count);
    
    if (productsCount === 0) {
      console.log("Adding products...");
      
      // Add sample products with various tags
      const productValues = [
        // NEW ARRIVALS - Products tagged as 'New'
        {
          name: "Refurbished Smartphone",
          description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
          price: 329.99,
          categoryId: 1, // Recycled Electronics
          imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
          rating: 4.5,
          reviewCount: 34,
          inStock: true,
          isNew: true,
          isPopular: false,
          isSale: false
        },
        {
          name: "Recycled Plastic Watch",
          description: "Stylish watch made from ocean plastic. Water-resistant and eco-conscious design.",
          price: 79.99,
          categoryId: 4, // Recycled Accessories
          imageUrl: "https://images.pexels.com/photos/3908800/pexels-photo-3908800.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
          rating: 4.1,
          reviewCount: 27,
          inStock: true,
          isNew: true,
          isPopular: false,
          isSale: false
        },
        // POPULAR ITEMS
        {
          name: "Recycled Metal Water Bottle",
          description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
          price: 29.99,
          categoryId: 4, // Recycled Accessories
          imageUrl: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
          rating: 4.9,
          reviewCount: 120,
          inStock: true,
          isNew: false,
          isPopular: true,
          isSale: false
        },
        // ON SALE ITEMS
        {
          name: "Recycled Glass Vase",
          description: "Elegant vase made from 100% recycled glass. Each piece is unique with slight variations in color.",
          price: 49.99,
          discountPrice: 34.99,
          categoryId: 3, // Eco-friendly Home
          imageUrl: "https://images.pexels.com/photos/5702315/pexels-photo-5702315.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
          rating: 4.4,
          reviewCount: 28,
          inStock: true,
          isNew: false,
          isPopular: false,
          isSale: true
        }
      ];
      
      for (const product of productValues) {
        const now = new Date();
        await client.query(
          `INSERT INTO products (
            name, description, price, discount_price, rating, review_count, image_url, 
            category_id, in_stock, is_new, is_popular, is_sale, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)`,
          [
            product.name, 
            product.description, 
            product.price, 
            product.discountPrice || null, 
            product.rating, 
            product.reviewCount,
            product.imageUrl,
            product.categoryId,
            product.inStock,
            product.isNew,
            product.isPopular,
            product.isSale,
            now
          ]
        );
      }
      
      console.log("Products added successfully!");
    } else {
      console.log(`Products already exist: ${productsCount} products found`);
    }
    
    console.log("Database seeding complete");
    
    // Close the database connection
    await client.end();
  } catch (error) {
    console.error("Error seeding database:", error);
    process.exit(1);
  }
}

// Run the seeding function
seedDatabase();