import { QueryClient } from "@tanstack/react-query";

// This function ensures any data returned is safe to use with common array operations
const makeSafeData = (data: any): any => {
  // If it's null or undefined, return an empty array
  if (data === null || data === undefined) {
    return [];
  }

  // If it's already an array, return it
  if (Array.isArray(data)) {
    return data;
  }

  // If it's an object with a reduce method, it might be array-like, so keep it
  if (typeof data === 'object' && typeof data.reduce === 'function') {
    return data;
  }

  // If it's an object that looks like it should be an array (has numeric keys and length)
  if (typeof data === 'object' && 'length' in data) {
    try {
      // Try to convert to a proper array
      return Array.from(data);
    } catch (e) {
      // If conversion fails, wrap it in an array
      return [data];
    }
  }

  // For other objects or primitive values, wrap in an array
  return [data];
};

// Create a custom query client that wraps all data responses
export const createSafeQueryClient = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Add global error handling
        retry: 1,
        staleTime: 5 * 60 * 1000, // 5 minutes
        // Process all data through our safety wrapper
        select: (data: any) => {
          try {
            return makeSafeData(data);
          } catch (error) {
            console.error("Error in query data processing:", error);
            // Return empty array as safe fallback
            return [];
          }
        },
      },
    },
  });

  return queryClient;
};
