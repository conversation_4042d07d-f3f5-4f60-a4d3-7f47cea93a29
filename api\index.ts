// Simplified API handler for Vercel deployment
import dotenv from 'dotenv';
import { Request, Response } from 'express';

// Define types for imported modules
type ExpressApp = {
  (req: Request, res: Response): void;
  use: (path: string, handler: (req: Request, res: Response) => void) => void;
};

type PingDatabaseFn = () => Promise<boolean>;
type InitializeDatabaseFn = (force?: boolean) => Promise<boolean>;

// Log the directory structure to help debug path issues
console.log('Current directory:', process.cwd());
console.log('Using direct Vercel API handler...');

// Import the direct handler for Vercel
let app: ExpressApp;
let pingDatabase: PingDatabaseFn;
let initializeDatabase: InitializeDatabaseFn;

try {
  // Import the simplified handler for Vercel
  const handlerModule = await import('./handler.js');
  app = handlerModule.default;

  const dbModule = await import('./db.js');
  pingDatabase = dbModule.pingDatabase;

  const initDbModule = await import('./init-db.js');
  initializeDatabase = initDbModule.initializeDatabase;

  console.log('Successfully imported Vercel API handler');
} catch (error: unknown) {
  console.error('Error importing Vercel API handler:', error);
  // Create fallback minimal Express app to avoid crashing
  const express = await import('express');
  app = express.default();
  app.use('*', (req: Request, res: Response) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: 'API handler initialization failed', details: errorMessage });
  });
  pingDatabase = async () => false;
  initializeDatabase = async () => false;
}

// Load environment variables based on environment
// Important: in Vercel, environment variables are automatically injected, so check if they exist first
if (!process.env.DATABASE_URL && !process.env.POSTGRES_URL) {
  // For Vercel development environment, use .env.vercel if it exists
  if (process.env.VERCEL && process.env.NODE_ENV !== 'production') {
    try {
      const fs = await import('fs');
      if (fs.existsSync('.env.vercel')) {
        dotenv.config({ path: '.env.vercel' });
        console.log('Loaded .env.vercel file for Vercel development');
      } else {
        dotenv.config({ path: '.env' });
        console.log('Loaded .env file (no .env.vercel found)');
      }
    } catch (error: unknown) {
      console.error('Error loading environment variables for Vercel:', error);
      dotenv.config({ path: '.env' });
    }
  } else {
    // For other environments
    dotenv.config({
      path: process.env.NODE_ENV === 'production' ? '.env.production' : '.env'
    });
    console.log(`Loaded ${process.env.NODE_ENV === 'production' ? '.env.production' : '.env'} file`);
  }
}

// Set SSL options for database connections in Vercel environment
if (process.env.VERCEL) {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  process.env.PGSSLMODE = 'no-verify';
  process.env.PG_SSL_REJECT_UNAUTHORIZED = '0';
}

console.log('API handler environment:', process.env.NODE_ENV || 'development');
console.log('Database URL configured:', process.env.DATABASE_URL ? '✓ Present' : '✗ Missing');
console.log('Vercel deployment:', process.env.VERCEL ? '✓ Yes' : '✗ No');

// Verify database connection on first request
let initialized = false;
async function ensureInitialized() {
  if (!initialized) {
    try {
      console.log('Initializing Vercel API handler...');

      // Verify database connection
      const dbConnected = await pingDatabase();
      console.log('Database connection check:', dbConnected ? '✓ Connected' : '✗ Failed');

      // Try to initialize the database if connected
      if (dbConnected) {
        try {
          console.log('Attempting to initialize database...');
          // Force initialization in production to ensure data is present
          const force = process.env.NODE_ENV === 'production';
          await initializeDatabase(force);
          console.log(`Database initialization successful (force=${force})`);
        } catch (initError: unknown) {
          console.error('Database initialization error:', initError);
          // Continue even if initialization fails
        }
      }

      initialized = true;
    } catch (error: unknown) {
      console.error('Initialization error in Vercel API handler:', error);
      // Don't mark as initialized on error to retry on next request
      // But don't block the request either
    }
  }
}

// Remove the direct database initialization import to fix the module not found error

// Create a handler for Vercel serverless functions
export default async function handler(req: Request, res: Response) {
  // Check if this is a direct database initialization request
  if (req.url && req.url.startsWith('/api/direct-init-db')) {
    console.log('Direct database initialization requested');
    try {
      // Force initialization in production
      const force = req.query?.force === 'true';
      await initializeDatabase(force);
      return res.status(200).json({ success: true, message: 'Database initialized successfully' });
    } catch (error: unknown) {
      console.error('Error in direct database initialization:', error);
      return res.status(500).json({ success: false, message: 'Failed to initialize database', error: String(error) });
    }
  }

  // Make sure the database is connected, but continue even if it fails
  try {
    await ensureInitialized();
  } catch (error: unknown) {
    console.error('Failed to initialize in Vercel handler:', error);
    // Continue handling the request despite initialization errors
  }

  // Forward the request to the Express app
  return app(req, res);
}
