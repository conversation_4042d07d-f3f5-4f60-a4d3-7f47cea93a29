// Additional API endpoints for Vercel
import { eq, sql, like, and, or, desc, asc } from 'drizzle-orm';
import { db } from './db.js';
import * as schema from './schema.js';

// Function to add all endpoints to the Express app
export function addEndpoints(app) {
  // Database initialization endpoint
  app.get('/api/init-db', async (req, res) => {
    try {
      const force = req.query.force === 'true';
      const { initializeDatabase } = await import('./init-db.js');

      console.log(`Initializing database with force=${force}`);
      const result = await initializeDatabase(force);

      if (result) {
        return res.status(200).json({ message: 'Database initialized successfully' });
      } else {
        return res.status(500).json({ message: 'Database initialization failed' });
      }
    } catch (error) {
      console.error('Error initializing database:', error);
      return res.status(500).json({ message: 'Database initialization failed', error: error.message });
    }
  });

  // Logout endpoint
  app.post('/api/logout', async (req, res) => {
    try {
      // In a real app, we would invalidate the session
      // For now, just return success
      res.status(200).json({ message: 'Logged out successfully' });
    } catch (error) {
      console.error('Error logging out:', error);
      res.status(500).json({ message: 'Failed to logout', error: error.message });
    }
  });
  // Current user endpoint
  app.get('/api/user', async (req, res) => {
    try {
      // Get user ID from query parameter
      const userId = req.query.userId;

      if (!userId) {
        return res.status(401).json({ message: 'Not authenticated' });
      }

      // Find user by ID
      const [user] = await db.select().from(schema.users).where(eq(schema.users.id, parseInt(userId)));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Return user data (excluding password)
      const { password: _, ...userData } = user;
      res.json(userData);
    } catch (error) {
      console.error('Error fetching current user:', error);
      res.status(500).json({ message: 'Failed to fetch user', error: error.message });
    }
  });

  // Placeholder image endpoint
  app.get('/api/placeholder/:width/:height', async (req, res) => {
    try {
      const { width, height } = req.params;
      const color = req.query.color || 'cccccc';
      const text = req.query.text || `${width}x${height}`;

      // Generate SVG placeholder
      const svg = `
        <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <rect width="${width}" height="${height}" fill="#${color}" />
          <text x="50%" y="50%" font-family="Arial" font-size="${Math.min(parseInt(width), parseInt(height)) / 10}px" fill="#666666" text-anchor="middle" dominant-baseline="middle">${text}</text>
        </svg>
      `;

      // Set headers
      res.setHeader('Content-Type', 'image/svg+xml');
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

      // Send SVG
      res.send(svg);
    } catch (error) {
      console.error('Error generating placeholder image:', error);
      res.status(500).json({ message: 'Failed to generate placeholder image', error: error.message });
    }
  });

  // Enhanced products endpoint with filtering
  app.get('/api/products', async (req, res) => {
    try {
      const {
        category,
        search,
        minPrice,
        maxPrice,
        isNew,
        isPopular,
        isSale,
        limit = '20',
        offset = '0'
      } = req.query;

      console.log('Fetching products with filters:', req.query);

      // Build query conditions
      let conditions = [];

      if (category) {
        conditions.push(eq(schema.products.categoryId, parseInt(category)));
      }

      if (search) {
        conditions.push(
          or(
            like(schema.products.name, `%${search}%`),
            like(schema.products.description, `%${search}%`)
          )
        );
      }

      if (minPrice) {
        conditions.push(sql`${schema.products.price} >= ${parseFloat(minPrice)}`);
      }

      if (maxPrice) {
        conditions.push(sql`${schema.products.price} <= ${parseFloat(maxPrice)}`);
      }

      if (isNew === 'true') {
        conditions.push(eq(schema.products.isNew, true));
      }

      if (isPopular === 'true') {
        conditions.push(eq(schema.products.isPopular, true));
      }

      if (isSale === 'true') {
        conditions.push(eq(schema.products.isSale, true));
      }

      // Execute query with conditions
      let query = db.select().from(schema.products);

      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      // Add limit and offset
      query = query
        .limit(parseInt(limit))
        .offset(parseInt(offset))
        .orderBy(desc(schema.products.createdAt));

      const products = await query;

      console.log(`Fetched ${products.length} products with filters:`, req.query);
      res.json(products);
    } catch (error) {
      console.error('Error fetching products:', error);
      res.status(500).json({ message: 'Failed to fetch products', error: error.message });
    }
  });

  // Categories endpoint
  app.get('/api/categories', async (req, res) => {
    try {
      const categories = await db.select().from(schema.categories);

      if (!categories || categories.length === 0) {
        // Return sample categories if none are found
        console.log('No categories found, returning sample data');
        return res.json([
          { id: 1, name: 'Recycled Electronics', imageUrl: 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg' },
          { id: 2, name: 'Upcycled Fashion', imageUrl: 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg' },
          { id: 3, name: 'Eco-friendly Home', imageUrl: 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg' },
          { id: 4, name: 'Recycled Accessories', imageUrl: 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg' }
        ]);
      }

      console.log(`Fetched ${categories.length} categories`);
      res.json(categories);
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Return sample categories on error
      console.log('Error fetching categories, returning sample data');
      return res.json([
        { id: 1, name: 'Recycled Electronics', imageUrl: 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg' },
        { id: 2, name: 'Upcycled Fashion', imageUrl: 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg' },
        { id: 3, name: 'Eco-friendly Home', imageUrl: 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg' },
        { id: 4, name: 'Recycled Accessories', imageUrl: 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg' }
      ]);
    }
  });

  // Featured products endpoint
  app.get('/api/products/featured', async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : 8;

      const products = await db.select()
        .from(schema.products)
        .where(eq(schema.products.isPopular, true))
        .limit(limit)
        .orderBy(desc(schema.products.createdAt));

      console.log(`Fetched ${products.length} featured products`);
      res.json(products);
    } catch (error) {
      console.error('Error fetching featured products:', error);
      res.status(500).json({ message: 'Failed to fetch featured products', error: error.message });
    }
  });

  // New arrivals endpoint
  app.get('/api/products/new', async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : 8;

      const products = await db.select()
        .from(schema.products)
        .where(eq(schema.products.isNew, true))
        .limit(limit)
        .orderBy(desc(schema.products.createdAt));

      console.log(`Fetched ${products.length} new products`);
      res.json(products);
    } catch (error) {
      console.error('Error fetching new products:', error);
      res.status(500).json({ message: 'Failed to fetch new products', error: error.message });
    }
  });

  // On sale products endpoint
  app.get('/api/products/sale', async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : 8;

      const products = await db.select()
        .from(schema.products)
        .where(eq(schema.products.isSale, true))
        .limit(limit)
        .orderBy(desc(schema.products.createdAt));

      console.log(`Fetched ${products.length} sale products`);
      res.json(products);
    } catch (error) {
      console.error('Error fetching sale products:', error);
      res.status(500).json({ message: 'Failed to fetch sale products', error: error.message });
    }
  });

  // Product by ID endpoint
  app.get('/api/products/:id', async (req, res) => {
    try {
      const productId = parseInt(req.params.id);

      const [product] = await db.select()
        .from(schema.products)
        .where(eq(schema.products.id, productId));

      if (!product) {
        // If product not found, return a sample product for testing
        console.log(`Product with ID ${productId} not found, returning sample product`);
        return res.json({
          id: productId,
          name: "Sample Product",
          description: "This is a sample product for testing purposes.",
          price: 99.99,
          discountPrice: 79.99,
          rating: 4.5,
          reviewCount: 10,
          imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg",
          categoryId: 1,
          inStock: true,
          isNew: true,
          isPopular: true,
          isSale: false,
          createdAt: new Date()
        });
      }

      console.log(`Fetched product with ID ${productId}`);
      res.json(product);
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({ message: 'Failed to fetch product', error: error.message });
    }
  });

  // Cart endpoints
  app.get('/api/cart', async (req, res) => {
    try {
      const userId = req.query.userId;

      // Log the request for debugging
      console.log('Cart request received:', {
        userId,
        headers: req.headers,
        cookies: req.cookies,
        query: req.query
      });

      // If no userId is provided, return an empty array instead of an error
      // This helps prevent client-side errors when authentication state is uncertain
      if (!userId) {
        console.log('No userId provided for cart request, returning empty array');
        return res.json([]);
      }

      // Get cart items for user
      const cartItems = await db.select().from(schema.cartItems).where(eq(schema.cartItems.userId, parseInt(userId)));
      console.log(`Found ${cartItems.length} cart items for user ${userId}`);

      // Get product details for each cart item
      const cartWithProducts = [];
      for (const item of cartItems) {
        try {
          const [product] = await db.select().from(schema.products).where(eq(schema.products.id, item.productId));
          if (product) {
            cartWithProducts.push({
              ...item,
              product
            });
          }
        } catch (productError) {
          console.error(`Error fetching product ${item.productId} for cart:`, productError);
          // Continue with other items even if one fails
        }
      }

      console.log(`Returning ${cartWithProducts.length} cart items with products`);
      res.json(cartWithProducts);
    } catch (error) {
      console.error('Error fetching cart:', error);
      // Return empty array on error to prevent client-side crashes
      res.json([]);
    }
  });

  app.post('/api/cart', async (req, res) => {
    try {
      const { userId, productId, quantity } = req.body;

      // Log the request for debugging
      console.log('Add to cart request received:', {
        userId,
        productId,
        quantity,
        headers: req.headers,
        cookies: req.cookies
      });

      if (!userId || !productId) {
        console.log('Missing userId or productId in cart add request');
        return res.status(400).json({ message: 'User ID and Product ID are required' });
      }

      // Check if product exists
      const [product] = await db.select().from(schema.products).where(eq(schema.products.id, parseInt(productId)));

      if (!product) {
        console.log(`Product with ID ${productId} not found for cart add`);
        return res.status(404).json({ message: 'Product not found' });
      }

      // Check if item already exists in cart
      const existingItems = await db.select()
        .from(schema.cartItems)
        .where(
          sql`${schema.cartItems.userId} = ${parseInt(userId)} AND ${schema.cartItems.productId} = ${parseInt(productId)}`
        );

      if (existingItems.length > 0) {
        // Update quantity
        console.log(`Updating quantity for existing cart item for user ${userId} and product ${productId}`);
        try {
          const updatedItem = await db.update(schema.cartItems)
            .set({ quantity: (existingItems[0].quantity || 0) + (quantity || 1) })
            .where(eq(schema.cartItems.id, existingItems[0].id))
            .returning();

          console.log('Successfully updated cart item quantity:', updatedItem[0]);
          res.json(updatedItem[0]);
        } catch (updateError) {
          console.error('Database error updating cart quantity:', updateError);
          res.status(500).json({ message: 'Database error updating cart', error: updateError.message });
        }
      } else {
        // Add new item
        console.log(`Adding new item to cart for user ${userId} and product ${productId}`);
        try {
          const newItem = await db.insert(schema.cartItems)
            .values({
              userId: parseInt(userId),
              productId: parseInt(productId),
              quantity: quantity || 1,
              createdAt: new Date()
            })
            .returning();

          console.log('Successfully added item to cart:', newItem[0]);
          res.status(201).json(newItem[0]);
        } catch (insertError) {
          console.error('Database error adding to cart:', insertError);
          res.status(500).json({ message: 'Database error adding to cart', error: insertError.message });
        }
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      res.status(500).json({ message: 'Failed to add to cart', error: error.message });
    }
  });

  app.delete('/api/cart/:id', async (req, res) => {
    try {
      const itemId = parseInt(req.params.id);

      // Log the request for debugging
      console.log('Remove from cart request received:', {
        itemId,
        headers: req.headers,
        cookies: req.cookies
      });

      if (isNaN(itemId)) {
        console.log('Invalid cart item ID:', req.params.id);
        return res.status(400).json({ message: 'Invalid cart item ID' });
      }

      try {
        // Delete cart item
        const result = await db.delete(schema.cartItems).where(eq(schema.cartItems.id, itemId)).returning();
        console.log(`Deleted cart item ${itemId}, affected rows:`, result.length);

        res.status(204).end();
      } catch (deleteError) {
        console.error(`Database error deleting cart item ${itemId}:`, deleteError);
        res.status(500).json({ message: 'Database error removing from cart', error: deleteError.message });
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
      res.status(500).json({ message: 'Failed to remove from cart', error: error.message });
    }
  });

  // Wishlist endpoints
  app.get('/api/wishlist', async (req, res) => {
    try {
      const userId = req.query.userId;

      // Log the request for debugging
      console.log('Wishlist request received:', {
        userId,
        headers: req.headers,
        cookies: req.cookies,
        query: req.query
      });

      // If no userId is provided, return an empty array instead of an error
      // This helps prevent client-side errors when authentication state is uncertain
      if (!userId) {
        console.log('No userId provided for wishlist request, returning empty array');
        return res.json([]);
      }

      // Get wishlist items for user
      const wishlistItems = await db.select().from(schema.wishlistItems).where(eq(schema.wishlistItems.userId, parseInt(userId)));
      console.log(`Found ${wishlistItems.length} wishlist items for user ${userId}`);

      // Get product details for each wishlist item
      const wishlistWithProducts = [];
      for (const item of wishlistItems) {
        try {
          const [product] = await db.select().from(schema.products).where(eq(schema.products.id, item.productId));
          if (product) {
            wishlistWithProducts.push({
              ...item,
              product
            });
          }
        } catch (productError) {
          console.error(`Error fetching product ${item.productId} for wishlist:`, productError);
          // Continue with other items even if one fails
        }
      }

      console.log(`Returning ${wishlistWithProducts.length} wishlist items with products`);
      res.json(wishlistWithProducts);
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      // Return empty array on error to prevent client-side crashes
      res.json([]);
    }
  });

  app.post('/api/wishlist', async (req, res) => {
    try {
      const { userId, productId } = req.body;

      // Log the request for debugging
      console.log('Add to wishlist request received:', {
        userId,
        productId,
        headers: req.headers,
        cookies: req.cookies
      });

      if (!userId || !productId) {
        console.log('Missing userId or productId in wishlist add request');
        return res.status(400).json({ message: 'User ID and Product ID are required' });
      }

      // Check if product exists
      const [product] = await db.select().from(schema.products).where(eq(schema.products.id, parseInt(productId)));

      if (!product) {
        console.log(`Product with ID ${productId} not found for wishlist add`);
        return res.status(404).json({ message: 'Product not found' });
      }

      // Check if item already exists in wishlist
      const existingItems = await db.select()
        .from(schema.wishlistItems)
        .where(
          sql`${schema.wishlistItems.userId} = ${parseInt(userId)} AND ${schema.wishlistItems.productId} = ${parseInt(productId)}`
        );

      if (existingItems.length > 0) {
        // Item already exists
        console.log(`Item already exists in wishlist for user ${userId} and product ${productId}`);
        res.json(existingItems[0]);
      } else {
        // Add new item
        console.log(`Adding new item to wishlist for user ${userId} and product ${productId}`);
        try {
          const newItem = await db.insert(schema.wishlistItems)
            .values({
              userId: parseInt(userId),
              productId: parseInt(productId),
              createdAt: new Date()
            })
            .returning();

          console.log('Successfully added item to wishlist:', newItem[0]);
          res.status(201).json(newItem[0]);
        } catch (insertError) {
          console.error('Database error adding to wishlist:', insertError);
          res.status(500).json({ message: 'Database error adding to wishlist', error: insertError.message });
        }
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      res.status(500).json({ message: 'Failed to add to wishlist', error: error.message });
    }
  });

  app.delete('/api/wishlist/:id', async (req, res) => {
    try {
      const itemId = parseInt(req.params.id);

      // Log the request for debugging
      console.log('Remove from wishlist request received:', {
        itemId,
        headers: req.headers,
        cookies: req.cookies
      });

      if (isNaN(itemId)) {
        console.log('Invalid wishlist item ID:', req.params.id);
        return res.status(400).json({ message: 'Invalid wishlist item ID' });
      }

      try {
        // Delete wishlist item
        const result = await db.delete(schema.wishlistItems).where(eq(schema.wishlistItems.id, itemId)).returning();
        console.log(`Deleted wishlist item ${itemId}, affected rows:`, result.length);

        res.status(204).end();
      } catch (deleteError) {
        console.error(`Database error deleting wishlist item ${itemId}:`, deleteError);
        res.status(500).json({ message: 'Database error removing from wishlist', error: deleteError.message });
      }
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      res.status(500).json({ message: 'Failed to remove from wishlist', error: error.message });
    }
  });

  // Order endpoints
  app.get('/api/orders', async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        return res.status(400).json({ message: 'User ID is required' });
      }

      // Get orders for user
      const orders = await db.select().from(schema.orders).where(eq(schema.orders.userId, parseInt(userId)));

      // Get order items for each order
      const ordersWithItems = [];
      for (const order of orders) {
        const orderItems = await db.select().from(schema.orderItems).where(eq(schema.orderItems.orderId, order.id));

        // Get product details for each order item
        const itemsWithProducts = [];
        for (const item of orderItems) {
          const [product] = await db.select().from(schema.products).where(eq(schema.products.id, item.productId));
          if (product) {
            itemsWithProducts.push({
              ...item,
              product
            });
          }
        }

        ordersWithItems.push({
          ...order,
          items: itemsWithProducts
        });
      }

      res.json(ordersWithItems);
    } catch (error) {
      console.error('Error fetching orders:', error);
      res.status(500).json({ message: 'Failed to fetch orders', error: error.message });
    }
  });

  app.get('/api/orders/:id', async (req, res) => {
    try {
      const orderId = parseInt(req.params.id);

      // Get order
      const [order] = await db.select().from(schema.orders).where(eq(schema.orders.id, orderId));

      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // Get order items
      const orderItems = await db.select().from(schema.orderItems).where(eq(schema.orderItems.orderId, orderId));

      // Get product details for each order item
      const itemsWithProducts = [];
      for (const item of orderItems) {
        const [product] = await db.select().from(schema.products).where(eq(schema.products.id, item.productId));
        if (product) {
          itemsWithProducts.push({
            ...item,
            product
          });
        }
      }

      res.json({
        ...order,
        items: itemsWithProducts
      });
    } catch (error) {
      console.error('Error fetching order:', error);
      res.status(500).json({ message: 'Failed to fetch order', error: error.message });
    }
  });

  app.post('/api/orders', async (req, res) => {
    try {
      const { userId, items, address, paymentMethod } = req.body;

      if (!userId || !items || !address || !paymentMethod) {
        return res.status(400).json({ message: 'User ID, items, address, and payment method are required' });
      }

      // Calculate total
      let total = 0;
      for (const item of items) {
        const [product] = await db.select().from(schema.products).where(eq(schema.products.id, item.productId));
        if (product) {
          total += (product.discountPrice || product.price) * item.quantity;
        }
      }

      // Create order
      const [order] = await db.insert(schema.orders)
        .values({
          userId: parseInt(userId),
          status: 'pending',
          total,
          address,
          paymentMethod,
          createdAt: new Date()
        })
        .returning();

      // Create order items
      const orderItems = [];
      for (const item of items) {
        const [product] = await db.select().from(schema.products).where(eq(schema.products.id, item.productId));
        if (product) {
          const [orderItem] = await db.insert(schema.orderItems)
            .values({
              orderId: order.id,
              productId: item.productId,
              quantity: item.quantity,
              price: product.discountPrice || product.price,
              createdAt: new Date()
            })
            .returning();

          orderItems.push({
            ...orderItem,
            product
          });
        }
      }

      // Clear cart after order is created
      if (userId) {
        await db.delete(schema.cartItems).where(eq(schema.cartItems.userId, parseInt(userId)));
      }

      res.status(201).json({
        ...order,
        items: orderItems
      });
    } catch (error) {
      console.error('Error creating order:', error);
      res.status(500).json({ message: 'Failed to create order', error: error.message });
    }
  });

  return app;
}
