// Vercel serverless handler with optimized imports for production
import express from 'express';
import dotenv from 'dotenv';
import postgres from 'postgres';

// Track database connection status
let dbStatus = {
  connected: false,
  lastAttempt: null,
  error: null,
  connectionString: null
};

// Ensure environment variables are loaded
if (!process.env.DATABASE_URL && !process.env.POSTGRES_URL) {
  try {
    // For Vercel development environment
    if (process.env.VERCEL && process.env.NODE_ENV !== 'production') {
      const fs = await import('fs').then(m => m.default || m);
      if (fs.existsSync('.env.vercel')) {
        dotenv.config({ path: '.env.vercel' });
        console.log('Loaded .env.vercel file for Vercel development');
      } else {
        dotenv.config();
        console.log('Loaded default .env file (no .env.vercel found)');
      }
    }
    // For production environment
    else if (process.env.NODE_ENV === 'production') {
      dotenv.config({ path: '.env.production' });
      console.log('Loaded .env.production file');
    }
    // For other environments
    else {
      dotenv.config();
      console.log('Loaded default .env file');
    }
  } catch (error) {
    console.error('Error loading environment variables:', error);
    // Fallback to default .env
    try {
      dotenv.config();
    } catch (e) {
      console.error('Failed to load fallback environment variables:', e);
    }
  }
}

// Set SSL options for database connections in Vercel environment
if (process.env.VERCEL) {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  process.env.PGSSLMODE = 'no-verify';
  process.env.PG_SSL_REJECT_UNAUTHORIZED = '0';
}

// Debug logging to help identify issues
console.log('Serverless function initialization:', {
  environment: process.env.NODE_ENV || 'development',
  databaseConfigured: !!process.env.DATABASE_URL,
  vercel: !!process.env.VERCEL
});

// Database connection function
async function connectToDatabase() {
  try {
    dbStatus.lastAttempt = new Date().toISOString();

    // Safety check for connection string - try both DATABASE_URL and POSTGRES_URL
    const connectionString = process.env.DATABASE_URL || process.env.POSTGRES_URL;
    if (!connectionString) {
      dbStatus.error = 'Neither DATABASE_URL nor POSTGRES_URL environment variable found';
      dbStatus.connected = false;
      return null;
    }

    // Store sanitized connection string for diagnostics (hide password)
    let sanitizedUrl = connectionString;
    try {
      // Simple sanitization - replace password with asterisks
      sanitizedUrl = sanitizedUrl.replace(/(://[^:]+:)([^@]+)(@)/, '$1******$3');
    } catch (e) {
      console.error('Error sanitizing URL:', e);
    }
    dbStatus.connectionString = sanitizedUrl;

    // Configure connection options for Supabase in serverless environment
    const sql = postgres(connectionString, {
      ssl: {
        rejectUnauthorized: false // Required for Supabase connections
      },
      max: 1, // Limited connections for serverless
      idle_timeout: 2, // Short timeout for serverless
      connect_timeout: 5 // Quick timeout for serverless
    });

    console.log('Attempting database connection with:', {
      environment: process.env.NODE_ENV || 'development',
      vercel: !!process.env.VERCEL,
      ssl: { rejectUnauthorized: false },
      connectionStringPresent: !!connectionString
    });

    // Test connection with simple query
    const result = await sql`SELECT 1 as test`;
    if (result && result[0] && result[0].test === 1) {
      dbStatus.connected = true;
      dbStatus.error = null;
      console.log('Successfully connected to database');
      return sql;
    } else {
      throw new Error('Database connection test failed');
    }
  } catch (error) {
    console.error('Database connection error:', error);
    dbStatus.connected = false;
    dbStatus.error = error.message;
    return null;
  }
}

// Create Express app for serverless environment
const app = express();

// Initialize database connection
let db = null;
connectToDatabase().then(connection => {
  db = connection;
}).catch(error => {
  console.error('Failed to initialize database:', error);
});

// Basic API routes for health check and diagnostics
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', environment: process.env.NODE_ENV, vercel: !!process.env.VERCEL });
});

app.get('/api/debug', async (req, res) => {
  // Return detailed environment and connection info to help debug
  // Try to connect if not already connected
  if (!dbStatus.connected) {
    db = await connectToDatabase();
  }

  res.json({
    database: {
      configured: !!process.env.DATABASE_URL,
      connected: dbStatus.connected,
      lastAttempt: dbStatus.lastAttempt,
      connectionString: dbStatus.connectionString,
      error: dbStatus.error
    },
    environment: {
      nodeEnv: process.env.NODE_ENV,
      vercel: !!process.env.VERCEL,
      postgresHost: process.env.POSTGRES_HOST || 'not set'
    },
    timestamp: new Date().toISOString()
  });
});

// Fallback for other API routes
app.use('/api/*', (req, res) => {
  res.status(200).json({ message: 'API endpoint reached. Database connections temporarily disabled during deployment.' });
});

// Default handler for any other request
app.use('*', (req, res) => {
  res.status(200).send(`
    <html>
      <head>
        <title>E-commerce App</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
          h1 { color: #333; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        </style>
      </head>
      <body>
        <h1>E-commerce Application</h1>
        <div class="card">
          <h2>Deployment Status</h2>
          <p>Your application is deployed and the frontend is ready.</p>
          <p>API functionality is being configured for production.</p>
        </div>
        <div class="card">
          <h2>Environment</h2>
          <pre>NODE_ENV: ${process.env.NODE_ENV || 'development'}</pre>
          <pre>Database: ${process.env.DATABASE_URL ? 'Configured' : 'Not configured'}</pre>
          <pre>Vercel: ${process.env.VERCEL ? 'Yes' : 'No'}</pre>
        </div>
      </body>
    </html>
  `);
});

// Export as default handler for Vercel
export default async function handler(req, res) {
  // Log request for debugging
  console.log(`Processing ${req.method} request to ${req.url}`);

  // Special handling for health and debug endpoints
  if (req.url === '/api/health') {
    // Try to connect if not already connected
    if (!dbStatus.connected) {
      db = await connectToDatabase();
    }

    return res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: {
        configured: !!process.env.DATABASE_URL,
        connected: dbStatus.connected,
        lastAttempt: dbStatus.lastAttempt
      }
    });
  }

  if (req.url === '/api/debug') {
    // Try to connect if not already connected
    if (!dbStatus.connected) {
      db = await connectToDatabase();
    }

    return res.status(200).json({
      environment: process.env.NODE_ENV || 'development',
      vercel: !!process.env.VERCEL,
      database: {
        configured: !!process.env.DATABASE_URL,
        connected: dbStatus.connected,
        lastAttempt: dbStatus.lastAttempt,
        connectionString: dbStatus.connectionString,
        error: dbStatus.error
      },
      timestamp: new Date().toISOString()
    });
  }

  // Pass all other requests to the Express app
  return app(req, res);
}
