import type { Express } from "express";
import { createServer, type Server } from "http";
import { dbStorage as storage } from "./database-storage.js";
import { setupAuth } from "./auth.js";
import { sendOTP } from "./email.js";
import { z } from "zod";
// Import schema from local compatibility layer instead of @shared/schema.js
import { insertCartItemSchema, insertWishlistItemSchema } from "./schema.js";


export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication routes
  setupAuth(app);

  // Authentication routes are now all in auth.ts

  // Registration route is handled in auth.ts


  // Categories routes
  app.get("/api/categories", async (req, res) => {
    try {
      const categories = await storage.getCategories();
      res.json(categories);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch categories" });
    }
  });

  // Products routes
  app.get("/api/products", async (req, res) => {
    try {
      const {
        limit, offset, category, search, isNew, isPopular, isSale, minPrice, maxPrice
      } = req.query;

      const filterParams = {
        limit: limit ? parseInt(limit as string) : undefined,
        offset: offset ? parseInt(offset as string) : undefined,
        categoryId: category ? parseInt(category as string) : undefined,
        search: search as string,
        isNew: isNew === "true",
        isPopular: isPopular === "true",
        isSale: isSale === "true",
        minPrice: minPrice ? parseFloat(minPrice as string) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined
      };

      const products = await storage.getProducts(filterParams);

      if (!products || products.length === 0) {
        return res.json([]);
      }

      res.json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });

  // Featured products (popular products)
  app.get("/api/products/featured", async (req, res) => {
    try {
      const products = await storage.getProducts({
        isPopular: true,
        limit: 4
      });

      res.json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch featured products" });
    }
  });

  // New arrivals
  app.get("/api/products/new", async (req, res) => {
    try {
      const products = await storage.getProducts({
        isNew: true,
        limit: 4
      });

      res.json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch new products" });
    }
  });

  // On sale products
  app.get("/api/products/sale", async (req, res) => {
    try {
      const products = await storage.getProducts({
        isSale: true,
        limit: 4
      });

      res.json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch sale products" });
    }
  });

  app.get("/api/products/:id", async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      const product = await storage.getProduct(productId);

      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json(product);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch product" });
    }
  });

  // Cart routes
  app.get("/api/cart", async (req, res) => {
    console.log(`Received GET request to /api/cart`);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      console.log(`Fetching cart items for user ${req.user.id}`);
      const cartItems = await storage.getCartItemWithDetails(req.user.id);
      console.log(`Found ${cartItems.length} cart items for user ${req.user.id}`);
      res.json(cartItems);
    } catch (error) {
      console.error(`Error fetching cart items:`, error);
      res.status(500).json({ message: "Failed to fetch cart items" });
    }
  });

  app.post("/api/cart", async (req, res) => {
    console.log(`Received POST request to /api/cart`);
    console.log(`Request body:`, req.body);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.body.userId || req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      console.log(`Adding item to cart for user ${req.user.id}`);

      const validationResult = insertCartItemSchema.safeParse({
        ...req.body,
        userId: req.user.id
      });

      if (!validationResult.success) {
        console.log(`Validation failed:`, validationResult.error);
        return res.status(400).json({ message: "Invalid cart item data", errors: validationResult.error });
      }

      console.log(`Validated cart item data:`, validationResult.data);

      const product = await storage.getProduct(validationResult.data.productId);
      if (!product) {
        console.log(`Product ${validationResult.data.productId} not found, returning 404`);
        return res.status(404).json({ message: "Product not found" });
      }

      console.log(`Found product:`, product);

      const cartItem = await storage.addCartItem(validationResult.data);
      console.log(`Added cart item:`, cartItem);

      res.status(201).json(cartItem);
    } catch (error) {
      console.error(`Error adding item to cart:`, error);
      res.status(500).json({ message: "Failed to add item to cart" });
    }
  });

  app.put("/api/cart/:id", async (req, res) => {
    console.log(`Received PUT request for cart item ID: ${req.params.id}`);
    console.log(`Request body:`, req.body);
    console.log(`Authentication status: ${req.isAuthenticated()}`);

    if (!req.isAuthenticated()) {
      console.log('User not authenticated, returning 401');
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const itemId = parseInt(req.params.id);
      const { quantity } = req.body;

      console.log(`Updating cart item ${itemId} with quantity ${quantity} for user ${req.user.id}`);

      if (typeof quantity !== "number" || quantity < 1) {
        console.log(`Invalid quantity: ${quantity}, returning 400`);
        return res.status(400).json({ message: "Invalid quantity" });
      }

      const updatedItem = await storage.updateCartItem(itemId, quantity);
      console.log(`Update cart item result:`, updatedItem);

      if (!updatedItem) {
        console.log(`Cart item ${itemId} not found, returning 404`);
        return res.status(404).json({ message: "Cart item not found" });
      }

      console.log(`Successfully updated cart item ${itemId}, returning updated item`);
      res.json(updatedItem);
    } catch (error) {
      console.error(`Error updating cart item:`, error);
      res.status(500).json({ message: "Failed to update cart item" });
    }
  });

  app.delete("/api/cart/:id", async (req, res) => {
    console.log(`Received DELETE request for cart item ID: ${req.params.id}`);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      const itemId = parseInt(req.params.id);
      console.log(`Attempting to remove cart item ${itemId} for user ${req.user.id}`);

      // First, verify that the cart item belongs to the authenticated user
      const cartItems = await storage.getCartItems(req.user.id);
      console.log(`Found ${cartItems.length} cart items for user ${req.user.id}`);

      const itemToRemove = cartItems.find(item => item.id === itemId);
      if (!itemToRemove) {
        console.log(`Cart item ${itemId} not found for user ${req.user.id}, returning 404`);
        return res.status(404).json({ message: "Cart item not found or does not belong to the authenticated user" });
      }

      console.log(`Found cart item to remove:`, itemToRemove);

      const success = await storage.removeCartItem(itemId);
      console.log(`Cart item removal result: ${success}`);

      if (!success) {
        console.log(`Cart item ${itemId} not found, returning 404`);
        return res.status(404).json({ message: "Cart item not found" });
      }

      console.log(`Successfully removed cart item ${itemId}, returning 204`);
      res.status(204).end();
    } catch (error) {
      console.error(`Error removing cart item:`, error);
      res.status(500).json({ message: "Failed to remove item from cart" });
    }
  });

  app.delete("/api/cart", async (req, res) => {
    console.log(`Received DELETE request to clear cart`);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`User:`, req.user);

    if (!req.isAuthenticated()) {
      console.log('User not authenticated, returning 401');
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const userId = req.user.id;
      console.log(`Attempting to clear cart for user ${userId}`);

      await storage.clearCart(userId);
      console.log(`Successfully cleared cart for user ${userId}, returning 204`);

      res.status(204).end();
    } catch (error) {
      console.error(`Error clearing cart:`, error);
      res.status(500).json({ message: "Failed to clear cart" });
    }
  });

  // Orders routes
  app.get("/api/orders", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const orders = await storage.getUserOrders(req.user.id);
      res.json(orders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch orders" });
    }
  });

  app.post("/api/orders", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const { address, paymentMethod } = req.body;

      if (!address || !paymentMethod) {
        return res.status(400).json({ message: "Address and payment method are required" });
      }

      // Get cart items to calculate total and create order items
      const cartItems = await storage.getCartItemWithDetails(req.user.id);

      if (cartItems.length === 0) {
        return res.status(400).json({ message: "Cart is empty" });
      }

      // Calculate total
      const total = cartItems.reduce((sum, item) => {
        const price = item.product.discountPrice || item.product.price;
        return sum + (price * item.quantity);
      }, 0);

      // Create order
      const order = await storage.createOrder({
        userId: req.user.id,
        total,
        address,
        paymentMethod,
        status: "pending",
      });

      // Create order items
      for (const item of cartItems) {
        await storage.createOrderItem({
          orderId: order.id,
          productId: item.productId,
          quantity: item.quantity,
          price: item.product.discountPrice || item.product.price,
        });
      }

      // Clear cart
      await storage.clearCart(req.user.id);

      res.status(201).json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to create order" });
    }
  });

  app.get("/api/orders/:id", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const orderId = parseInt(req.params.id);
      const order = await storage.getOrder(orderId);

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      if (order.userId !== req.user.id) {
        return res.status(403).json({ message: "Not authorized to view this order" });
      }

      const orderItems = await storage.getOrderItems(orderId);

      res.json({ ...order, items: orderItems });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch order" });
    }
  });

  // Wishlist routes
  app.get("/api/wishlist", async (req, res) => {
    console.log(`Received GET request to /api/wishlist`);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      console.log(`Fetching wishlist items for user ${req.user.id}`);
      const wishlistItems = await storage.getWishlistItemWithDetails(req.user.id);
      console.log(`Found ${wishlistItems.length} wishlist items for user ${req.user.id}`);
      res.json(wishlistItems);
    } catch (error) {
      console.error(`Error fetching wishlist items:`, error);
      res.status(500).json({ message: "Failed to fetch wishlist items" });
    }
  });

  app.post("/api/wishlist", async (req, res) => {
    console.log(`Received POST request to /api/wishlist`);
    console.log(`Request body:`, req.body);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.body.userId || req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      console.log(`Adding item to wishlist for user ${req.user.id}`);

      const validationResult = insertWishlistItemSchema.safeParse({
        ...req.body,
        userId: req.user.id
      });

      if (!validationResult.success) {
        console.log(`Validation failed:`, validationResult.error);
        return res.status(400).json({ message: "Invalid wishlist item data", errors: validationResult.error });
      }

      console.log(`Validated wishlist item data:`, validationResult.data);

      const product = await storage.getProduct(validationResult.data.productId);
      if (!product) {
        console.log(`Product ${validationResult.data.productId} not found, returning 404`);
        return res.status(404).json({ message: "Product not found" });
      }

      console.log(`Found product:`, product);

      const wishlistItem = await storage.addWishlistItem(validationResult.data);
      console.log(`Added wishlist item:`, wishlistItem);

      res.status(201).json(wishlistItem);
    } catch (error) {
      console.error(`Error adding item to wishlist:`, error);
      res.status(500).json({ message: "Failed to add item to wishlist" });
    }
  });

  app.delete("/api/wishlist/:id", async (req, res) => {
    console.log(`Received DELETE request for wishlist item ID: ${req.params.id}`);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.body.userId || req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      const itemId = parseInt(req.params.id);
      console.log(`Attempting to remove wishlist item ${itemId} for user ${req.user.id}`);

      // First, verify that the wishlist item belongs to the authenticated user
      const wishlistItems = await storage.getWishlistItems(req.user.id);
      console.log(`Found ${wishlistItems.length} wishlist items for user ${req.user.id}`);

      const itemToRemove = wishlistItems.find(item => item.id === itemId);
      if (!itemToRemove) {
        console.log(`Wishlist item ${itemId} not found for user ${req.user.id}, returning 404`);
        return res.status(404).json({ message: "Wishlist item not found or does not belong to the authenticated user" });
      }

      console.log(`Found wishlist item to remove:`, itemToRemove);

      const success = await storage.removeWishlistItem(itemId);
      console.log(`Wishlist item removal result: ${success}`);

      if (!success) {
        console.log(`Wishlist item ${itemId} not found, returning 404`);
        return res.status(404).json({ message: "Wishlist item not found" });
      }

      console.log(`Successfully removed wishlist item ${itemId}, returning 204`);
      res.status(204).end();
    } catch (error) {
      console.error(`Error removing wishlist item:`, error);
      res.status(500).json({ message: "Failed to remove item from wishlist" });
    }
  });

  app.get("/api/wishlist/check/:productId", async (req, res) => {
    console.log(`Received GET request to check if product ${req.params.productId} is in wishlist`);
    console.log(`Authentication status: ${req.isAuthenticated()}`);
    console.log(`Session:`, req.session);
    console.log(`User:`, req.user);

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      // Try to get userId from the request body or query parameters as a fallback
      const userId = req.query.userId;
      console.log(`User not authenticated, trying to use userId from request: ${userId}`);

      if (!userId) {
        console.log('No userId found in request, returning 401');
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Set user object manually for this request
      req.user = { id: parseInt(userId.toString()) };
      console.log(`Using userId from request: ${req.user.id}`);
    }

    try {
      const productId = parseInt(req.params.productId);
      console.log(`Checking if product ${productId} is in wishlist for user ${req.user.id}`);

      const isInWishlist = await storage.isProductInWishlist(req.user.id, productId);
      console.log(`Product ${productId} is ${isInWishlist ? '' : 'not '}in wishlist for user ${req.user.id}`);

      res.json({ isInWishlist });
    } catch (error) {
      console.error(`Error checking wishlist status:`, error);
      res.status(500).json({ message: "Failed to check wishlist status" });
    }
  });

  // User profile routes
  app.put("/api/profile", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const { fullName, email, address, phone } = req.body;

      // Update user profile
      const updatedUser = await storage.updateUser(req.user.id, {
        fullName,
        email,
        address,
        phone,
      });

      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }

      // Remove password from the response
      const { password, ...userWithoutPassword } = updatedUser;

      res.json(userWithoutPassword);
    } catch (error) {
      res.status(500).json({ message: "Failed to update profile" });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}