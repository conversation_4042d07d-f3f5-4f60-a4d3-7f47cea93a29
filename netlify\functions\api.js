import express from 'express';
import serverless from 'serverless-http';
import dotenv from 'dotenv';
import { testConnection, getPgPool, getSupabaseClient } from './supabase-db.js';

// Load environment variables
dotenv.config();

// We're using the specialized connector from supabase-db.js now

// Create express app
const app = express();

// Middleware to parse JSON requests
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    platform: 'netlify'
  });
});

// Debug endpoint
app.get('/api/debug', async (req, res) => {
  let dbStatus = false;
  let dbError = null;
  
  // Try to connect to database using our specialized connector
  try {
    const result = await testConnection();
    dbStatus = result.success;
    
    if (!result.success) {
      dbError = result.error || 'Unknown connection error';
      console.error('Database connection failed:', dbError);
    } else {
      console.log('Successfully connected to database');
    }
  } catch (error) {
    dbError = error.message;
    console.error('Database connection check failed:', error);
  }
  
  const supabase = getSupabaseClient();
  const pool = getPgPool();
  
  res.status(200).json({
    environment: process.env.NODE_ENV || 'development',
    platform: 'netlify',
    database: {
      status: dbStatus ? 'connected' : 'disconnected',
      url: process.env.DATABASE_URL ? 'configured' : 'missing',
      host: process.env.POSTGRES_HOST || 'not configured',
      error: dbError,
      supabaseAvailable: !!supabase,
      pgPoolAvailable: !!pool
    },
    timestamp: new Date().toISOString()
  });
});

// Default handler for all other routes
app.all('/api/*', (req, res) => {
  res.status(200).json({
    message: 'API endpoint reached successfully',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Export the serverless handler
export const handler = serverless(app);
