import { useState, useEffect } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { ProductCard } from "@/components/ui/product-card";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useWishlist } from "@/hooks/use-wishlist"; // Added import
import {
  Heart,
  Share2,
  Truck,
  ShieldCheck,
  RefreshCw,
  ChevronRight,
  Minus,
  Plus,
  Check,
  Star
} from "lucide-react";
import { Loader2 } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { motion } from "framer-motion";

export default function ProductDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const { addToWishlist, removeFromWishlist, isInWishlist, wishlistItems } = useWishlist(); // Added wishlist hook
  const [quantity, setQuantity] = useState(1);

  // Fallback product data in case the API fails
  const fallbackProduct = {
    id: Number(id),
    name: "Sample Product",
    description: "This is a sample product for testing purposes.",
    price: 99.99,
    discountPrice: 79.99,
    rating: 4.5,
    reviewCount: 10,
    imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg",
    categoryId: 1,
    inStock: true,
    isNew: true,
    isPopular: true,
    isSale: false,
    createdAt: new Date()
  };

  // Fetch product details
  const { data: product, isLoading, error } = useQuery({
    queryKey: [`/api/products/${id}`],
    staleTime: 5 * 60 * 1000, // 5 minutes - individual products don't change frequently
    gcTime: 15 * 60 * 1000, // 15 minutes garbage collection
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    queryFn: async () => {
      console.log(`Fetching product with ID: ${id}`);
      try {
        const response = await fetch(`/api/products/${id}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch product: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log(`Successfully fetched product:`, data);

        // Validate image URL
        if (data && data.imageUrl) {
          console.log(`Product image URL: ${data.imageUrl}`);
          // Test if the image is accessible
          const imgTest = new Image();
          imgTest.onerror = () => {
            console.error(`Image URL is not accessible: ${data.imageUrl}`);
          };
          imgTest.onload = () => {
            console.log(`Image URL is accessible: ${data.imageUrl}`);
          };
          imgTest.src = data.imageUrl;
        } else {
          console.warn(`Product has no image URL or is invalid: ${data?.imageUrl}`);
        }

        return data;
      } catch (error) {
        console.error(`Error fetching product with ID ${id}:`, error);
        throw error;
      }
    }
  });

  // Fetch related products from same category
  const { data: relatedProducts } = useQuery({
    queryKey: ["/api/products", {
      category: product?.categoryId || fallbackProduct.categoryId,
      limit: 4
    }],
    enabled: true,
    staleTime: 3 * 60 * 1000, // 3 minutes cache for related products
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    refetchOnWindowFocus: false,
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000)
  });

  // Add to cart mutation
  const addToCartMutation = useMutation({
    mutationFn: async () => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to add items to your cart");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      // Make a direct fetch request to ensure userId is included
      console.log(`Adding product ${id} to cart for user ${user.id}`);

      const response = await fetch(`/api/cart?userId=${user.id}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: parseInt(String(user.id)),
          productId: Number(id),
          quantity
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to add to cart: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
      toast({
        title: "Added to cart",
        description: `${productData.name} has been added to your cart.`,
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add product to cart. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Handle quantity change
  const handleDecreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleIncreaseQuantity = () => {
    setQuantity(quantity + 1);
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (!user) {
      toast({
        title: "Login required",
        description: "Please login to add items to your cart",
        variant: "destructive",
      });
      navigate("/auth");
      return;
    }

    addToCartMutation.mutate();
  };

  // Handle add to wishlist
  const handleAddToWishlist = async () => {
    if (!user) {
      toast({
        title: "Login required",
        description: "Please login to add items to your wishlist.",
        variant: "destructive",
      });
      return;
    }

    try {
      const isProductInWishlist = isInWishlist(productData.id);
      const existingItem = wishlistItems.find(
        item => item.productId === productData.id || (item.product && item.product.id === productData.id)
      );

      if (isProductInWishlist && existingItem) {
        await removeFromWishlist(existingItem.id);
        toast({
          title: "Removed from wishlist",
          description: `${productData.name} has been removed from your wishlist.`,
        });
      } else {
        await addToWishlist(productData.id);
        toast({
          title: "Added to wishlist",
          description: `${productData.name} has been added to your wishlist.`,
        });
      }
    } catch (error) {
      console.error('Wishlist error:', error);
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
        variant: "destructive",
      });
    }
  };

  // New state and functions for review form
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [reviewName, setReviewName] = useState("");
  const [reviewText, setReviewText] = useState("");
  const [reviewRating, setReviewRating] = useState(5);

  // Submit review mutation
  const submitReviewMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("POST", `/api/products/${id}/reviews`, {
        name: reviewName,
        text: reviewText,
        rating: reviewRating
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/products/${id}`] });
      toast({
        title: "Review submitted",
        description: "Thank you for your feedback!",
      });
      setShowReviewForm(false);
      setReviewName("");
      setReviewText("");
      setReviewRating(5);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to submit review. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast({
        title: "Login required",
        description: "Please login to submit a review",
        variant: "destructive",
      });
      navigate("/auth");
      return;
    }

    if (!reviewName || !reviewText) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    submitReviewMutation.mutate();
  };

  // Use fallback product if there's an error or no product data
  const productData = product || fallbackProduct;

  // Show error toast but continue with fallback data
  useEffect(() => {
    if (error) {
      console.error("Using fallback product data due to error:", error);
    }
  }, [error]);

  // Preload product image when product data is available
  useEffect(() => {
    if (productData && productData.imageUrl) {
      const img = new Image();
      img.src = productData.imageUrl;
      img.onload = () => {
        console.log(`Successfully preloaded image: ${productData.imageUrl}`);
      };
      img.onerror = () => {
        console.error(`Failed to preload image: ${productData.imageUrl}`);
      };
    }
  }, [productData]);

  // Loading state
  if (isLoading) {
    return (
      <div>
        <main className="bg-background flex items-center justify-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-secondary" />
        </main>
      </div>
    );
  }

  // Filter out the current product from related products
  const filteredRelatedProducts = (relatedProducts as any[] || []).filter(p => p.id !== Number(id));

  // Calculate discount percentage
  const discountPercentage = productData.discountPrice
    ? Math.round(((productData.price - productData.discountPrice) / productData.price) * 100)
    : 0;

  return (
    <div>
      <main className="bg-background">
        {/* Breadcrumb */}
        <div className="bg-white border-y border-gray-200">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center text-sm">
              <a href="/" className="text-gray-500 hover:text-secondary">Home</a>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <a href="/products" className="text-gray-500 hover:text-secondary">Products</a>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <a
                href={`/products?category=${productData.categoryId}`}
                className="text-gray-500 hover:text-secondary"
              >
                {productData.categoryId === 1 ? "Recycled Electronics" :
                 productData.categoryId === 2 ? "Upcycled Fashion" :
                 productData.categoryId === 3 ? "Eco-friendly Home" :
                 productData.categoryId === 4 ? "Recycled Accessories" : "Other"}
              </a>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <span className="text-primary font-medium">{productData.name}</span>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
              {/* Product Images */}
              <div>
                <div className="rounded-lg overflow-hidden border border-gray-200 mb-4">
                  <img
                    src={productData.imageUrl}
                    alt={productData.name}
                    className="w-full h-auto object-cover"
                    onLoad={() => {
                      console.log(`Successfully loaded image for product ${productData.id} (${productData.name})`);
                    }}
                    onError={(e) => {
                      // If image fails to load, use category-specific fallback images
                      const target = e.target as HTMLImageElement;
                      console.log(`Image failed to load for product ${productData.id} (${productData.name}): ${productData.imageUrl}`);

                      // Try to fix the URL if it's malformed
                      let fixedUrl = productData.imageUrl;
                      if (fixedUrl && fixedUrl.includes('?')) {
                        fixedUrl = fixedUrl.split('?')[0];
                        console.log(`Trying with fixed URL: ${fixedUrl}`);
                        target.src = fixedUrl;
                        return;
                      }

                      // Use category-specific fallback images without query parameters
                      if (productData.categoryId === 1) {
                        // Recycled Electronics
                        target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
                      } else if (productData.categoryId === 2) {
                        // Upcycled Fashion
                        target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
                      } else if (productData.categoryId === 3) {
                        // Eco-friendly Home
                        target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
                      } else if (productData.categoryId === 4) {
                        // Recycled Accessories
                        target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
                      } else {
                        // Default fallback
                        target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
                      }
                      console.log(`Using fallback image for product ${productData.id} (${productData.name}) with category ${productData.categoryId}`);
                    }}
                  />
                </div>
                <div className="grid grid-cols-4 gap-2">
                  <div className="border border-secondary rounded-lg overflow-hidden">
                    <img
                      src={productData.imageUrl}
                      alt={productData.name}
                      className="w-full h-24 object-cover"
                      onError={(e) => {
                        // If image fails to load, set a fallback image based on category
                        const target = e.target as HTMLImageElement;

                        // Try to fix the URL if it's malformed
                        let fixedUrl = productData.imageUrl;
                        if (fixedUrl && fixedUrl.includes('?')) {
                          fixedUrl = fixedUrl.split('?')[0];
                          target.src = fixedUrl;
                          return;
                        }

                        if (productData.categoryId === 1) {
                          // Recycled Electronics
                          target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
                        } else if (productData.categoryId === 2) {
                          // Upcycled Fashion
                          target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
                        } else if (productData.categoryId === 3) {
                          // Eco-friendly Home
                          target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
                        } else if (productData.categoryId === 4) {
                          // Recycled Accessories
                          target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
                        } else {
                          // Default fallback
                          target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
                        }
                      }}
                    />
                  </div>
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={productData.imageUrl}
                      alt={productData.name}
                      className="w-full h-24 object-cover"
                      onError={(e) => {
                        // If image fails to load, set a fallback image based on category
                        const target = e.target as HTMLImageElement;

                        // Try to fix the URL if it's malformed
                        let fixedUrl = productData.imageUrl;
                        if (fixedUrl && fixedUrl.includes('?')) {
                          fixedUrl = fixedUrl.split('?')[0];
                          target.src = fixedUrl;
                          return;
                        }

                        if (productData.categoryId === 1) {
                          // Recycled Electronics
                          target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
                        } else if (productData.categoryId === 2) {
                          // Upcycled Fashion
                          target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
                        } else if (productData.categoryId === 3) {
                          // Eco-friendly Home
                          target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
                        } else if (productData.categoryId === 4) {
                          // Recycled Accessories
                          target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
                        } else {
                          // Default fallback
                          target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
                        }
                      }}
                    />
                  </div>
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={productData.imageUrl}
                      alt={productData.name}
                      className="w-full h-24 object-cover"
                      onError={(e) => {
                        // If image fails to load, set a fallback image based on category
                        const target = e.target as HTMLImageElement;

                        // Try to fix the URL if it's malformed
                        let fixedUrl = productData.imageUrl;
                        if (fixedUrl && fixedUrl.includes('?')) {
                          fixedUrl = fixedUrl.split('?')[0];
                          target.src = fixedUrl;
                          return;
                        }

                        if (productData.categoryId === 1) {
                          // Recycled Electronics
                          target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
                        } else if (productData.categoryId === 2) {
                          // Upcycled Fashion
                          target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
                        } else if (productData.categoryId === 3) {
                          // Eco-friendly Home
                          target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
                        } else if (productData.categoryId === 4) {
                          // Recycled Accessories
                          target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
                        } else {
                          // Default fallback
                          target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
                        }
                      }}
                    />
                  </div>
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={productData.imageUrl}
                      alt={productData.name}
                      className="w-full h-24 object-cover"
                      onError={(e) => {
                        // If image fails to load, set a fallback image based on category
                        const target = e.target as HTMLImageElement;

                        // Try to fix the URL if it's malformed
                        let fixedUrl = productData.imageUrl;
                        if (fixedUrl && fixedUrl.includes('?')) {
                          fixedUrl = fixedUrl.split('?')[0];
                          target.src = fixedUrl;
                          return;
                        }

                        if (productData.categoryId === 1) {
                          // Recycled Electronics
                          target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
                        } else if (productData.categoryId === 2) {
                          // Upcycled Fashion
                          target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
                        } else if (productData.categoryId === 3) {
                          // Eco-friendly Home
                          target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
                        } else if (productData.categoryId === 4) {
                          // Recycled Accessories
                          target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
                        } else {
                          // Default fallback
                          target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Product Details */}
              <div>
                <h1 className="text-2xl md:text-3xl font-bold font-poppins text-primary mb-2">
                  {productData.name}
                </h1>

                <div className="flex items-center mb-4">
                  <div className="flex text-accent">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill={i < Math.floor(productData.rating) ? "currentColor" : "none"}
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={i < Math.floor(productData.rating) ? "" : "text-gray-300"}
                      >
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                      </svg>
                    ))}
                  </div>
                  <span className="text-sm text-gray-500 ml-2">{productData.rating} ({productData.reviewCount} reviews)</span>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    {productData.discountPrice ? (
                      <>
                        <span className="text-2xl font-bold text-primary mr-2">
                          ${productData.discountPrice.toFixed(2)}
                        </span>
                        <span className="text-lg text-gray-500 line-through">
                          ${productData.price.toFixed(2)}
                        </span>
                        <span className="ml-2 bg-secondary text-white px-2 py-1 rounded text-xs font-medium">
                          {discountPercentage}% OFF
                        </span>
                      </>
                    ) : (
                      <span className="text-2xl font-bold text-primary">
                        ${productData.price.toFixed(2)}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-green-600 flex items-center">
                    <Check className="w-4 h-4 mr-1" />
                    {productData.inStock ? "In Stock" : "Out of Stock"}
                  </p>
                </div>

                <div className="border-t border-b border-gray-200 py-4 my-4">
                  <p className="text-gray-700">
                    {productData.description}
                  </p>
                </div>

                <div className="space-y-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Quantity
                    </label>
                    <div className="flex items-center">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 rounded-l-md rounded-r-none"
                        onClick={handleDecreaseQuantity}
                        disabled={quantity <= 1}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <div className="h-10 w-16 flex items-center justify-center border-y border-gray-200">
                        {quantity}
                      </div>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 rounded-r-md rounded-l-none"
                        onClick={handleIncreaseQuantity}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-4 mb-6">
                  <Button
                    className="flex-1 bg-secondary hover:bg-secondary/90"
                    onClick={handleAddToCart}
                    disabled={addToCartMutation.isPending}
                  >
                    {addToCartMutation.isPending ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <>Add to Cart</>
                    )}
                  </Button>
                  <Button variant="outline" className="flex-1" onClick={handleAddToWishlist}> {/* Added onClick handler */}
                    <Heart className="mr-2 h-4 w-4" /> Add to Wishlist
                  </Button>
                </div>

                <div className="flex items-center space-x-4">
                  <Button variant="ghost" size="sm" className="text-gray-500">
                    <Share2 className="mr-1 h-4 w-4" /> Share
                  </Button>
                </div>

                <div className="mt-6 space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Truck className="w-4 h-4 mr-2 text-gray-500" />
                    <span>Free shipping on orders over $50</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <ShieldCheck className="w-4 h-4 mr-2 text-gray-500" />
                    <span>2 year extended warranty</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <RefreshCw className="w-4 h-4 mr-2 text-gray-500" />
                    <span>30 days return policy</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Product Information Tabs */}
          <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
            <Tabs defaultValue="description">
              <TabsList className="w-full justify-start border-b mb-6">
                <TabsTrigger value="description" className="text-base">Description</TabsTrigger>
                <TabsTrigger value="specifications" className="text-base">Specifications</TabsTrigger>
                <TabsTrigger value="reviews" className="text-base">Reviews ({productData.reviewCount})</TabsTrigger>
              </TabsList>

              <TabsContent value="description" className="text-gray-700 space-y-4">
                <p>{productData.description}</p>
                <p>
                  This product is made with high-quality sustainable materials and designed for
                  long-lasting performance. Each item is carefully crafted with attention to detail
                  and environmental responsibility. Our products are designed to reduce waste and
                  promote a more sustainable lifestyle.
                </p>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Premium quality material</li>
                  <li>Comfortable and stylish</li>
                  <li>Durable and long-lasting</li>
                  <li>Easy to maintain</li>
                </ul>
              </TabsContent>

              <TabsContent value="specifications">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-lg mb-3">Product Specifications</h3>
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b">
                          <td className="py-2 font-medium text-gray-700">Brand</td>
                          <td className="py-2">EliteShop</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 font-medium text-gray-700">Model</td>
                          <td className="py-2">ES-{productData.id}00</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 font-medium text-gray-700">Material</td>
                          <td className="py-2">Premium Quality</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 font-medium text-gray-700">Color</td>
                          <td className="py-2">As shown</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 font-medium text-gray-700">Weight</td>
                          <td className="py-2">0.5 kg</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div>
                    <h3 className="font-medium text-lg mb-3">Package Contents</h3>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>1 x {productData.name}</li>
                      <li>1 x User Manual</li>
                      <li>1 x Warranty Card</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="reviews">
                <div className="space-y-6">
                  <motion.div
                    className="flex items-center mb-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="mr-4">
                      <div className="text-3xl font-bold text-primary">{productData.rating}</div>
                      <div className="flex text-accent">
                        {[...Array(5)].map((_, i) => (
                          <motion.svg
                            key={i}
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill={i < Math.floor(productData.rating) ? "currentColor" : "none"}
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className={i < Math.floor(productData.rating) ? "" : "text-gray-300"}
                            initial={{ scale: 0.5, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: i * 0.1 }}
                          >
                            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                          </motion.svg>
                        ))}
                      </div>
                      <div className="text-sm text-gray-500">Based on {productData.reviewCount} reviews</div>
                    </div>
                    <div className="flex-1">
                      <div className="space-y-2">
                        <motion.div
                          className="flex items-center"
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.7, delay: 0.1 }}
                        >
                          <span className="text-sm text-gray-500 w-12">5 star</span>
                          <div className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
                            <motion.div
                              className="h-2 bg-accent rounded-full"
                              style={{ width: '0%' }}
                              animate={{ width: '70%' }}
                              transition={{ duration: 0.8, delay: 0.2 }}
                            ></motion.div>
                          </div>
                          <span className="text-sm text-gray-500 w-8">70%</span>
                        </motion.div>
                        <motion.div
                          className="flex items-center"
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.7, delay: 0.2 }}
                        >
                          <span className="text-sm text-gray-500 w-12">4 star</span>
                          <div className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
                            <motion.div
                              className="h-2 bg-accent rounded-full"
                              style={{ width: '0%' }}
                              animate={{ width: '20%' }}
                              transition={{ duration: 0.8, delay: 0.3 }}
                            ></motion.div>
                          </div>
                          <span className="text-sm text-gray-500 w-8">20%</span>
                        </motion.div>
                        <motion.div
                          className="flex items-center"
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.7, delay: 0.3 }}
                        >
                          <span className="text-sm text-gray-500 w-12">3 star</span>
                          <div className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
                            <motion.div
                              className="h-2 bg-accent rounded-full"
                              style={{ width: '0%' }}
                              animate={{ width: '5%' }}
                              transition={{ duration: 0.8, delay: 0.4 }}
                            ></motion.div>
                          </div>
                          <span className="text-sm text-gray-500 w-8">5%</span>
                        </motion.div>
                        <motion.div
                          className="flex items-center"
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.7, delay: 0.4 }}
                        >
                          <span className="text-sm text-gray-500 w-12">2 star</span>
                          <div className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
                            <motion.div
                              className="h-2 bg-accent rounded-full"
                              style={{ width: '0%' }}
                              animate={{ width: '3%' }}
                              transition={{ duration: 0.8, delay: 0.5 }}
                            ></motion.div>
                          </div>
                          <span className="text-sm text-gray-500 w-8">3%</span>
                        </motion.div>
                        <motion.div
                          className="flex items-center"
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.7, delay: 0.5 }}
                        >
                          <span className="text-sm text-gray-500 w-12">1 star</span>
                          <div className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
                            <motion.div
                              className="h-2 bg-accent rounded-full"
                              style={{ width: '0%' }}
                              animate={{ width: '2%' }}
                              transition={{ duration: 0.8, delay: 0.6 }}
                            ></motion.div>
                          </div>
                          <span className="text-sm text-gray-500 w-8">2%</span>
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>

                  <div className="border-t pt-6">
                    <h3 className="font-medium text-lg mb-4">Customer Reviews</h3>
                    <div className="space-y-6">
                      {/* Review submission form */}
                      {showReviewForm ? (
                        <motion.div
                          className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200"
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <h4 className="font-medium mb-4">Write a Review</h4>
                          <form onSubmit={handleSubmitReview} className="space-y-4">
                            <div>
                              <Label htmlFor="reviewName">Your Name</Label>
                              <Input
                                id="reviewName"
                                value={reviewName}
                                onChange={(e) => setReviewName(e.target.value)}
                                required
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor="reviewRating">Rating</Label>
                              <div className="flex items-center mt-1 space-x-1">
                                {[1, 2, 3, 4, 5].map((rating) => (
                                  <button
                                    key={rating}
                                    type="button"
                                    onClick={() => setReviewRating(rating)}
                                    className="focus:outline-none"
                                  >
                                    <Star
                                      className={`w-6 h-6 ${
                                        rating <= reviewRating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                      } transition-colors duration-200`}
                                    />
                                  </button>
                                ))}
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="reviewText">Your Review</Label>
                              <Textarea
                                id="reviewText"
                                value={reviewText}
                                onChange={(e) => setReviewText(e.target.value)}
                                required
                                className="mt-1"
                                rows={4}
                              />
                            </div>
                            <div className="flex items-center justify-end space-x-2">
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => setShowReviewForm(false)}
                              >
                                Cancel
                              </Button>
                              <Button
                                type="submit"
                                disabled={submitReviewMutation.isPending}
                              >
                                {submitReviewMutation.isPending ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Submitting...
                                  </>
                                ) : (
                                  'Submit Review'
                                )}
                              </Button>
                            </div>
                          </form>
                        </motion.div>
                      ) : (
                        <div className="mb-6">
                          <Button
                            onClick={() => setShowReviewForm(true)}
                            className="w-full bg-green-600 hover:bg-green-700"
                          >
                            Write a Review
                          </Button>
                        </div>
                      )}

                      {/* Existing reviews with animation */}
                      <motion.div
                        className="border-b pb-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.1 }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">John Doe</h4>
                            <div className="flex text-accent mt-1">
                              {[...Array(5)].map((_, i) => (
                                <motion.svg
                                  key={i}
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="14"
                                  height="14"
                                  viewBox="0 0 24 24"
                                  fill={i < 5 ? "currentColor" : "none"}
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ delay: 0.2 + i * 0.05 }}
                                >
                                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                </motion.svg>
                              ))}
                            </div>
                          </div>
                          <span className="text-sm text-gray-500">2 days ago</span>
                        </div>
                        <p className="text-gray-700">Excellent product! Very happy with my purchase. The quality is amazing and it looks exactly as shown in the pictures.</p>
                      </motion.div>

                      <motion.div
                        className="border-b pb-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.2 }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">Jane Smith</h4>
                            <div className="flex text-accent mt-1">
                              {[...Array(5)].map((_, i) => (
                                <motion.svg
                                  key={i}
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="14"
                                  height="14"
                                  viewBox="0 0 24 24"
                                  fill={i < 4 ? "currentColor" : "none"}
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className={i < 4 ? "" : "text-gray-300"}
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ delay: 0.3 + i * 0.05 }}
                                >
                                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                </motion.svg>
                              ))}
                            </div>
                          </div>
                          <span className="text-sm text-gray-500">1 week ago</span>
                        </div>
                        <p className="text-gray-700">Good product for the price. Fast delivery and well packaged. Would recommend to others looking for something similar.</p>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.3 }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">Mike Johnson</h4>
                            <div className="flex text-accent mt-1">
                              {[...Array(5)].map((_, i) => (
                                <motion.svg
                                  key={i}
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="14"
                                  height="14"
                                  viewBox="0 0 24 24"
                                  fill={i < 5 ? "currentColor" : "none"}
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ delay: 0.4 + i * 0.05 }}
                                >
                                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                </motion.svg>
                              ))}
                            </div>
                          </div>
                          <span className="text-sm text-gray-500">2 weeks ago</span>
                        </div>
                        <p className="text-gray-700">Absolutely love this product! The quality exceeds my expectations. Shipping was fast and the customer service was excellent.</p>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Related Products */}
          {filteredRelatedProducts.length > 0 && (
            <div className="mt-12">
              <h2 className="text-2xl font-bold font-poppins text-primary mb-6">
                Related Products
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                {filteredRelatedProducts.slice(0, 4).map((relatedProduct: any) => (
                  <ProductCard key={relatedProduct.id} product={relatedProduct} />
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}