import { Store } from 'express-session';
import { Pool } from 'pg';

// Custom session store that uses our existing PostgreSQL pool
// but with better SSL certificate handling
export class SafeSessionStore extends Store {
  private pool: Pool;
  private tableName: string;
  private createTable: boolean;
  private pruneInterval: number | null;
  private pruneTimer: NodeJS.Timeout | null;

  constructor(options: any = {}) {
    super();
    
    this.pool = options.pool;
    this.tableName = options.tableName || 'session';
    this.createTable = options.createTableIfMissing !== false;
    this.pruneInterval = options.pruneSessionInterval || 60 * 15; // 15 minutes
    this.pruneTimer = null;
    
    if (this.createTable) {
      this.createSessionTable().catch(err => {
        console.error('Error creating session table:', err);
      });
    }
    
    if (this.pruneInterval) {
      this.startPruneTimer();
    }
  }
  
  private async createSessionTable() {
    try {
      const client = await this.pool.connect();
      try {
        await client.query(`
          CREATE TABLE IF NOT EXISTS "${this.tableName}" (
            "sid" varchar NOT NULL COLLATE "default",
            "sess" json NOT NULL,
            "expire" timestamp(6) NOT NULL,
            CONSTRAINT "${this.tableName}_pkey" PRIMARY KEY ("sid")
          )
        `);
        console.log(`Session table "${this.tableName}" initialized`);
      } finally {
        client.release();
      }
    } catch (err) {
      console.error('Could not create session table:', err);
      // Don't throw - we want to continue even if this fails
    }
  }
  
  private startPruneTimer() {
    this.pruneTimer = setInterval(() => {
      this.pruneSessions().catch(err => {
        console.error('Failed to prune sessions:', err);
      });
    }, this.pruneInterval! * 1000);
    
    // Don't keep the process alive just for pruning
    if (this.pruneTimer.unref) {
      this.pruneTimer.unref();
    }
  }
  
  private async pruneSessions() {
    try {
      const client = await this.pool.connect();
      try {
        await client.query(`DELETE FROM "${this.tableName}" WHERE "expire" < NOW()`);
      } finally {
        client.release();
      }
    } catch (err) {
      console.error('Could not prune sessions:', err);
    }
  }
  
  async get(sid: string, callback: (err: any, session?: any) => void) {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(
          `SELECT sess FROM "${this.tableName}" WHERE sid = $1 AND expire >= NOW()`,
          [sid]
        );
        
        if (result.rows.length === 0) {
          return callback(null);
        }
        
        callback(null, result.rows[0].sess);
      } finally {
        client.release();
      }
    } catch (err) {
      callback(err);
    }
  }
  
  async set(sid: string, session: any, callback?: (err?: any) => void) {
    try {
      // Calculate expiration
      const maxAge = session.cookie?.maxAge || 86400000; // Default 1 day
      const expire = new Date(Date.now() + maxAge);
      
      const client = await this.pool.connect();
      try {
        const query = `
          INSERT INTO "${this.tableName}" (sid, sess, expire)
          VALUES ($1, $2, $3)
          ON CONFLICT (sid) DO UPDATE
          SET sess = $2, expire = $3
        `;
        
        await client.query(query, [sid, session, expire]);
        
        if (callback) callback();
      } finally {
        client.release();
      }
    } catch (err) {
      if (callback) callback(err);
    }
  }
  
  async destroy(sid: string, callback?: (err?: any) => void) {
    try {
      const client = await this.pool.connect();
      try {
        await client.query(`DELETE FROM "${this.tableName}" WHERE sid = $1`, [sid]);
        if (callback) callback();
      } finally {
        client.release();
      }
    } catch (err) {
      if (callback) callback(err);
    }
  }
  
  async touch(sid: string, session: any, callback?: (err?: any) => void) {
    try {
      const maxAge = session.cookie?.maxAge || 86400000;
      const expire = new Date(Date.now() + maxAge);
      
      const client = await this.pool.connect();
      try {
        await client.query(
          `UPDATE "${this.tableName}" SET expire = $2 WHERE sid = $1`,
          [sid, expire]
        );
        
        if (callback) callback();
      } finally {
        client.release();
      }
    } catch (err) {
      if (callback) callback(err);
    }
  }
  
  async length(callback: (err: any, length?: number) => void) {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`SELECT COUNT(*) FROM "${this.tableName}"`);
        callback(null, parseInt(result.rows[0].count, 10));
      } finally {
        client.release();
      }
    } catch (err) {
      callback(err);
    }
  }
  
  async clear(callback?: (err?: any) => void) {
    try {
      const client = await this.pool.connect();
      try {
        await client.query(`DELETE FROM "${this.tableName}"`);
        if (callback) callback();
      } finally {
        client.release();
      }
    } catch (err) {
      if (callback) callback(err);
    }
  }
  
  async close() {
    if (this.pruneTimer) {
      clearInterval(this.pruneTimer);
      this.pruneTimer = null;
    }
  }
}
