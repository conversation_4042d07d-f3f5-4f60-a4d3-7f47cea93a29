import { queryClient, apiRequest } from "@/lib/queryClient";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { Trash, Loader2, Heart, ShoppingCart } from "lucide-react";
import { WishlistItem, Product } from "@shared/schema";
import { useEffect } from "react";

export default function WishlistPage() {
  const { user } = useAuth();
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Store userId in localStorage for future requests
  useEffect(() => {
    if (user && user.id) {
      localStorage.setItem('userId', user.id.toString());
    }
  }, [user]);

  // Fetch wishlist items
  const {
    data: wishlistItems = [] as Array<WishlistItem & { product: Product }>,
    isLoading: isLoadingWishlist,
    isError: isWishlistError,
    refetch: refetchWishlist
  } = useQuery<Array<WishlistItem & { product: Product }>>({
    queryKey: ["/api/wishlist", user ? { userId: user.id } : null],
    enabled: !!user,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching wishlist items:', error);
      toast({
        title: "Wishlist Error",
        description: "Failed to load wishlist items. Please try again.",
        variant: "destructive",
      });
    },
    // Ensure we always get an array even if the API fails
    select: (data) => {
      console.log('Wishlist data received:', data);
      if (!data || !Array.isArray(data)) {
        console.warn('Wishlist data is not an array, returning empty array');
        return [];
      }
      return data.filter(item => item && item.product); // Filter out invalid items
    }
  });

  // Force refetch wishlist items when the component mounts
  useEffect(() => {
    if (user) {
      console.log('Refetching wishlist items on mount');
      refetchWishlist();
    }
  }, [refetchWishlist, user]);

  // Delete wishlist item mutation
  const removeFromWishlistMutation = useMutation({
    mutationFn: async ({ wishlistItemId, productId }: { wishlistItemId: number, productId: number }) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to remove items from your wishlist");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      // Use the product ID to remove from wishlist
      console.log(`Removing product with ID: ${productId} from wishlist for user: ${user.id}`);

      const response = await fetch(`/api/wishlist/${productId}?userId=${user.id}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      // Log the response status
      console.log(`Wishlist item removal response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to remove from wishlist: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully removed product ${productId} from wishlist, status: ${response.status}`);
      return { wishlistItemId, productId };
    },
    onSuccess: () => {
      console.log('Successfully removed from wishlist');

      if (user) {
        // Force invalidate and refetch the wishlist queries
        queryClient.invalidateQueries({ queryKey: ["/api/wishlist"] });
        queryClient.invalidateQueries({ queryKey: ["/api/wishlist", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/wishlist"] });
        queryClient.refetchQueries({ queryKey: ["/api/wishlist", { userId: user.id }] });
      }

      toast({
        title: "Product removed from wishlist",
        description: "The product has been removed from your wishlist.",
      });
    },
    onError: (error) => {
      console.error('Error removing from wishlist:', error);
      toast({
        title: "Failed to remove product",
        description: "Could not remove product from the wishlist. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Add to cart mutation
  const addToCartMutation = useMutation({
    mutationFn: async (productId: number) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to add items to your cart");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      // Make a direct fetch request to ensure it works
      console.log(`Adding product ${productId} to cart for user ${user.id}`);

      const response = await fetch('/api/cart', {
        method: 'POST',
        credentials: 'include', // Important: include credentials for cookies
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: parseInt(String(productId)),
          quantity: 1
        }), // Don't send userId in body, it will be taken from the session
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to add to cart: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully added product ${productId} to cart`);
      return response.json();
    },
    onSuccess: (data) => {
      console.log('Successfully added to cart:', data);

      if (user) {
        // Force invalidate and refetch the cart queries
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }

      toast({
        title: "Added to cart",
        description: "Product added to your cart successfully.",
      });
    },
    onError: (error) => {
      console.error('Error adding to cart:', error);
      toast({
        title: "Failed to add to cart",
        description: "Could not add the product to your cart. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleRemoveFromWishlist = (wishlistItemId: number, productId: number) => {
    removeFromWishlistMutation.mutate({ wishlistItemId, productId });
  };

  const handleAddToCart = (productId: number) => {
    addToCartMutation.mutate(productId);
  };

  // Format price with currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (!user) {
    navigate("/auth");
    return null;
  }

  const renderContent = () => {
    if (isLoadingWishlist) {
      return (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading your wishlist...</span>
        </div>
      );
    }

    if (isWishlistError) {
      return (
        <div className="text-center py-8 bg-red-50 rounded-lg p-6">
          <div className="text-red-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-semibold">Failed to load wishlist items</p>
          </div>
          <p className="text-gray-700 mb-4">There was a problem loading your wishlist. Please try again.</p>
          <Button
            onClick={() => refetchWishlist()}
            variant="outline"
            className="mx-auto"
          >
            <Loader2 className={`h-4 w-4 mr-2 ${isLoadingWishlist ? 'animate-spin' : ''}`} />
            Try Again
          </Button>
        </div>
      );
    }

    if (wishlistItems.length === 0) {
      return (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Heart className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold mb-2">Your wishlist is empty</h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Start exploring sustainable products and save your favorites to come back to them later.
          </p>
          <Button onClick={() => navigate("/products")}>
            Explore Sustainable Products
          </Button>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {wishlistItems.map((item: WishlistItem & { product: Product }) => (
          <Card key={item.id} className="overflow-hidden flex flex-col">
            <div className="h-48 overflow-hidden relative">
              <img
                src={item.product.imageUrl}
                onError={(e) => {
                  // If image fails to load, try with different parameters or use fallback
                  const target = e.target as HTMLImageElement;
                  const originalSrc = item.product.imageUrl;

                  // First try: Add query parameters to the original URL
                  if (!originalSrc.includes('?')) {
                    const enhancedUrl = `${originalSrc}?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1`;
                    console.log(`Image load failed. Trying with enhanced URL: ${enhancedUrl}`);
                    target.src = enhancedUrl;

                    // Set a flag to prevent infinite loop of error handling
                    if (!(target as any).retryCount) {
                      (target as any).retryCount = 1;
                      return; // Exit and let the enhanced URL try to load
                    }
                  }

                  // If we get here, the enhanced URL also failed or was already tried
                  console.log(`Enhanced image URL also failed to load for product ${item.product.id}. Using category fallback.`);

                  // Use category-specific fallback images
                  if (item.product.categoryId === 1) {
                    // Recycled Electronics
                    target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1';
                  } else if (item.product.categoryId === 2) {
                    // Upcycled Fashion
                    target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1';
                  } else if (item.product.categoryId === 3) {
                    // Eco-friendly Home
                    target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1';
                  } else if (item.product.categoryId === 4) {
                    // Recycled Accessories
                    target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1';
                  } else {
                    // Default fallback
                    target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1';
                  }
                  console.log(`Using fallback image for product ${item.product.id} (${item.product.name}) with category ${item.product.categoryId}`);
                }}
                alt={item.product.name}
                className="w-full h-full object-cover transition-transform hover:scale-105"
              />
              <button
                onClick={() => handleRemoveFromWishlist(item.id, item.product.id)}
                className="absolute top-2 right-2 bg-white p-2 rounded-full hover:bg-gray-100"
                aria-label="Remove from wishlist"
              >
                <Trash className="h-5 w-5 text-red-500" />
              </button>
            </div>
            <CardContent className="flex-grow p-4">
              <h3 className="font-semibold text-lg mb-1 hover:text-primary cursor-pointer" onClick={() => navigate(`/products/${item.product.id}`)}>
                {item.product.name}
              </h3>
              <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                {item.product.description}
              </p>
              <div className="flex justify-between items-center">
                <div>
                  {item.product.discountPrice ? (
                    <div className="flex items-center">
                      <span className="text-lg font-semibold text-primary">
                        {formatCurrency(item.product.discountPrice)}
                      </span>
                      <span className="text-sm text-gray-500 line-through ml-2">
                        {formatCurrency(item.product.price)}
                      </span>
                    </div>
                  ) : (
                    <span className="text-lg font-semibold text-primary">
                      {formatCurrency(item.product.price)}
                    </span>
                  )}
                </div>
                {item.product.inStock ? (
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    In Stock
                  </span>
                ) : (
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                    Out of Stock
                  </span>
                )}
              </div>
            </CardContent>
            <CardFooter className="p-4 pt-0">
              <Button
                className="w-full gap-2"
                disabled={!item.product.inStock || addToCartMutation.isPending}
                onClick={() => handleAddToCart(item.product.id)}
              >
                {addToCartMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-4 w-4" />
                    Add to Cart
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="container py-8">
      <div className="flex items-center mb-8">
        <Heart className="w-6 h-6 text-primary mr-2" />
        <h1 className="text-2xl font-bold">My Wishlist</h1>
      </div>
      {renderContent()}
    </div>
  );
}