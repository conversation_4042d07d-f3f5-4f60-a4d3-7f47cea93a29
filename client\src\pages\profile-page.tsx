import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { formatDate } from "@/lib/utils";
import {
  User,
  ChevronRight,
  Package,
  CreditCard,
  Heart,
  LogOut,
  Map,
  Phone,
  Mail,
  Check,
  Loader2
} from "lucide-react";
import { Link, useLocation } from "wouter";

// Profile form validation schema
const profileSchema = z.object({
  fullName: z.string().min(3, "Full name is required"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().optional(),
  address: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function ProfilePage() {
  const { toast } = useToast();
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState("profile");
  const [location] = useLocation();

  // Check for tab parameter in URL
  useEffect(() => {
    // Parse the query string
    const params = new URLSearchParams(location.split('?')[1]);
    const tabParam = params.get('tab');

    // Set active tab if the parameter exists and is valid
    if (tabParam === 'orders') {
      setActiveTab('orders');
    }
  }, [location]);

  // Store userId in localStorage for future requests
  useEffect(() => {
    if (user && user.id) {
      localStorage.setItem('userId', user.id.toString());
    }
  }, [user]);

  // Get user orders
  const {
    data: orders = [],
    isLoading: ordersLoading,
    refetch: refetchOrders,
    error: ordersError
  } = useQuery({
    queryKey: ["/api/orders", user ? { userId: user.id } : null],
    queryFn: async ({ queryKey }) => {
      console.log('Fetching orders with query key:', queryKey);

      // Extract userId from query key
      const userId = user?.id;

      if (!userId) {
        console.error('No userId available for fetching orders');
        return [];
      }

      console.log(`Making direct fetch request for orders with userId: ${userId}`);

      // Make a direct fetch request to ensure it works
      const response = await fetch(`/api/orders?userId=${userId}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to fetch orders: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Orders data received:', data);

      return data || [];
    },
    enabled: !!user,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching orders:', error);
      toast({
        title: "Error",
        description: "Failed to load your orders. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Log orders data for debugging
  useEffect(() => {
    console.log('Current orders data:', orders);
    console.log('Orders loading state:', ordersLoading);
    console.log('Orders error state:', ordersError);
  }, [orders, ordersLoading, ordersError]);

  // Force refetch orders when the component mounts or when the active tab changes to orders
  useEffect(() => {
    if (user && activeTab === 'orders') {
      console.log('Refetching orders');
      refetchOrders();
    }
  }, [refetchOrders, user, activeTab]);

  // Get user cart items count for badge
  const {
    data: cartItems = [],
    error: cartError
  } = useQuery({
    queryKey: ["/api/cart", user ? { userId: user.id } : null],
    queryFn: async ({ queryKey }) => {
      console.log('Fetching cart items with query key:', queryKey);

      // Extract userId from query key
      const userId = user?.id;

      if (!userId) {
        console.error('No userId available for fetching cart items');
        return [];
      }

      console.log(`Making direct fetch request for cart items with userId: ${userId}`);

      // Make a direct fetch request to ensure it works
      const response = await fetch(`/api/cart?userId=${userId}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to fetch cart items: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Cart data received:', data);

      if (!data || !Array.isArray(data)) {
        console.warn('Cart data is not an array, returning empty array');
        return [];
      }

      return data.filter(item => item && item.product); // Filter out invalid items
    },
    enabled: !!user,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching cart items:', error);
    }
  });

  // Initialize form with user data
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      phone: user?.phone || "",
      address: user?.address || "",
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      console.log('Updating profile with data:', data);

      // Store userId in localStorage for future requests
      if (user && user.id) {
        localStorage.setItem('userId', user.id.toString());
      }

      // Make a direct fetch request to ensure it works
      const response = await fetch(`/api/profile?userId=${user?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to update profile: ${response.status} ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log('Profile updated successfully:', data);

      // Force invalidate and refetch the user data
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });
      queryClient.refetchQueries({ queryKey: ["/api/user"] });

      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error('Error updating profile:', error);

      toast({
        title: "Update failed",
        description: error instanceof Error
          ? `Error: ${error.message}`
          : "There was a problem updating your profile. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Handle profile form submission
  const onSubmit = (data: ProfileFormValues) => {
    updateProfileMutation.mutate(data);
  };

  // Handle logout
  const handleLogout = () => {
    logout({
      onSuccess: () => {
        // Force redirect to auth page after logout
        window.location.href = '/auth';
      }
    });
  };

  const cartItemCount = cartItems?.length || 0;

  // Get status badge color based on order status
  const getStatusColor = (status: string | undefined | null) => {
    if (!status) {
      return 'bg-gray-100 text-gray-700';
    }

    try {
      switch (status.toLowerCase()) {
        case 'completed':
          return 'bg-green-100 text-green-700';
        case 'shipped':
          return 'bg-blue-100 text-blue-700';
        case 'processing':
          return 'bg-yellow-100 text-yellow-700';
        case 'pending':
        default:
          return 'bg-gray-100 text-gray-700';
      }
    } catch (error) {
      console.error('Error getting status color:', error);
      return 'bg-gray-100 text-gray-700';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
              <CardDescription>
                Please log in to view your profile.
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href="/auth">Go to Login</Link>
              </Button>
            </CardFooter>
          </Card>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 bg-background">
        {/* Breadcrumb */}
        <div className="bg-white border-y border-gray-200">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center text-sm">
              <Link href="/" className="text-gray-500 hover:text-secondary">Home</Link>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <span className="text-primary font-medium">My Account</span>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Sidebar */}
            <div className="md:w-1/4">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden sticky top-4">
                <div className="p-6 text-center border-b">
                  <Avatar className="w-20 h-20 mx-auto mb-3">
                    <AvatarImage src="" alt={user?.username || 'User'} />
                    <AvatarFallback className="bg-secondary text-white text-xl">
                      {user?.username && typeof user.username === 'string' ? user.username.charAt(0).toUpperCase() : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <h2 className="font-bold text-xl">{user?.fullName || user?.username || 'User'}</h2>
                  <p className="text-gray-500 text-sm mt-1">{user?.email || 'No email provided'}</p>
                </div>

                <div className="p-3">
                  <nav className="space-y-1">
                    <button
                      onClick={() => setActiveTab("profile")}
                      className={`w-full flex items-center px-3 py-2 rounded-md transition ${
                        activeTab === "profile"
                          ? "bg-secondary text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      <User className="h-5 w-5 mr-3" />
                      <span>My Profile</span>
                    </button>

                    <button
                      onClick={() => setActiveTab("orders")}
                      className={`w-full flex items-center px-3 py-2 rounded-md transition ${
                        activeTab === "orders"
                          ? "bg-secondary text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      <Package className="h-5 w-5 mr-3" />
                      <span>My Orders</span>
                    </button>

                    <Link
                      href="/cart"
                      className="w-full flex items-center px-3 py-2 rounded-md transition text-gray-700 hover:bg-gray-100"
                    >
                      <CreditCard className="h-5 w-5 mr-3" />
                      <span>My Cart</span>
                      {cartItemCount > 0 && (
                        <span className="ml-auto bg-secondary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {cartItemCount}
                        </span>
                      )}
                    </Link>

                    <Link
                      href="/wishlist"
                      className="w-full flex items-center px-3 py-2 rounded-md transition text-gray-700 hover:bg-gray-100"
                    >
                      <Heart className="h-5 w-5 mr-3" />
                      <span>Wishlist</span>
                    </Link>

                    <Separator className="my-2" />

                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center px-3 py-2 rounded-md transition text-red-600 hover:bg-red-50"
                    >
                      <LogOut className="h-5 w-5 mr-3" />
                      <span>Logout</span>
                    </button>
                  </nav>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="md:w-3/4">
              {/* Profile Tab */}
              {activeTab === "profile" && (
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <div className="p-6 border-b">
                    <h1 className="text-xl font-bold">Account Information</h1>
                  </div>

                  <div className="p-6">
                    <Tabs defaultValue="details">
                      <TabsList className="w-full mb-6">
                        <TabsTrigger value="details" className="flex-1">Personal Details</TabsTrigger>
                        <TabsTrigger value="address" className="flex-1">Address</TabsTrigger>
                      </TabsList>

                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)}>
                          <TabsContent value="details" className="mt-0">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <FormField
                                control={form.control}
                                name="fullName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Full Name</FormLabel>
                                    <FormControl>
                                      <Input placeholder="John Doe" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Email Address</FormLabel>
                                    <FormControl>
                                      <Input type="email" placeholder="<EMAIL>" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="phone"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Phone Number</FormLabel>
                                    <FormControl>
                                      <Input placeholder="(*************" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <div>
                                <FormLabel>Username</FormLabel>
                                <Input value={user?.username || 'User'} disabled className="bg-gray-50" />
                                <p className="text-sm text-gray-500 mt-1">Username cannot be changed</p>
                              </div>
                            </div>
                          </TabsContent>

                          <TabsContent value="address" className="mt-0">
                            <div className="grid grid-cols-1 gap-6">
                              <FormField
                                control={form.control}
                                name="address"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Address</FormLabel>
                                    <FormControl>
                                      <Input placeholder="123 Main St, City, State, Zip" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </TabsContent>

                          <div className="mt-6">
                            <Button
                              type="submit"
                              disabled={updateProfileMutation.isPending}
                            >
                              {updateProfileMutation.isPending ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Saving...
                                </>
                              ) : (
                                <>Save Changes</>
                              )}
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </Tabs>
                  </div>
                </div>
              )}

              {/* Orders Tab */}
              {activeTab === "orders" && (
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <div className="p-6 border-b">
                    <h1 className="text-xl font-bold">My Orders</h1>
                  </div>

                  <div className="p-6">
                    {ordersLoading ? (
                      <div className="flex justify-center py-12">
                        <Loader2 className="h-8 w-8 animate-spin text-secondary" />
                      </div>
                    ) : orders && orders.length > 0 ? (
                      <div className="space-y-6">
                        {orders.map((order) => (
                          <div
                            key={order.id}
                            className="border rounded-lg overflow-hidden"
                          >
                            <div className="bg-gray-50 p-4 flex flex-wrap items-center justify-between">
                              <div>
                                <p className="text-sm text-gray-500">Order ID</p>
                                <p className="font-medium">#{order?.id || 'N/A'}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Date</p>
                                <p className="font-medium">{formatDate(order?.createdAt)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Total</p>
                                <p className="font-medium">
                                  {order?.total !== undefined && !isNaN(Number(order.total))
                                    ? `$${Number(order.total).toFixed(2)}`
                                    : '$0.00'}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Status</p>
                                <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order?.status)}`}>
                                  {order?.status && typeof order.status === 'string'
                                    ? order.status.charAt(0).toUpperCase() + order.status.slice(1)
                                    : 'Pending'}
                                </span>
                              </div>
                            </div>
                            <div className="p-4">
                              <div className="flex items-start gap-3 mb-4">
                                <Map className="w-5 h-5 text-gray-500 mt-1" />
                                <div>
                                  <h3 className="font-medium text-sm">Shipping Address</h3>
                                  <p className="text-gray-600 text-sm">{order?.address || 'No address provided'}</p>
                                </div>
                              </div>
                              <div className="flex items-start gap-3">
                                <CreditCard className="w-5 h-5 text-gray-500 mt-1" />
                                <div>
                                  <h3 className="font-medium text-sm">Payment Method</h3>
                                  <p className="text-gray-600 text-sm">
                                    {order?.paymentMethod && typeof order.paymentMethod === 'string'
                                      ? order.paymentMethod.charAt(0).toUpperCase() + order.paymentMethod.slice(1)
                                      : 'Credit Card'}
                                  </p>
                                </div>
                              </div>
                              <div className="mt-4">
                                <Link href={`/orders/${order.id}`}>
                                  <Button variant="outline" size="sm">
                                    View Order Details
                                  </Button>
                                </Link>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Package className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                        <h3 className="text-xl font-medium text-gray-700 mb-1">No Orders Yet</h3>
                        <p className="text-gray-500 mb-6">You haven't placed any orders yet.</p>
                        <Button asChild>
                          <Link href="/products">Start Shopping</Link>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
