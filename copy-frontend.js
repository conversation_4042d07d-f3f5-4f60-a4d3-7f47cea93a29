// Script to copy the frontend build to the deployment directory
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination directories
const sourceDir = path.join(__dirname, 'dist');
const destDir = path.join(__dirname, '.vercel/output/static');

// Function to copy a directory recursively
function copyDirectory(source, destination) {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
    console.log(`Created directory: ${destination}`);
  }

  // Read the contents of the source directory
  const files = fs.readdirSync(source);

  // Copy each file/directory
  for (const file of files) {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);

    // Check if it's a directory or file
    const stats = fs.statSync(sourcePath);
    if (stats.isDirectory()) {
      // Recursively copy directory
      copyDirectory(sourcePath, destPath);
    } else {
      // Copy file
      fs.copyFileSync(sourcePath, destPath);
      console.log(`Copied file: ${sourcePath} -> ${destPath}`);
    }
  }
}

// Main function
function main() {
  console.log('Copying frontend build to deployment directory...');
  
  // Check if source directory exists
  if (!fs.existsSync(sourceDir)) {
    console.error(`Source directory not found: ${sourceDir}`);
    console.error('Please build the frontend first using: npm run build');
    process.exit(1);
  }

  // Copy the frontend build
  try {
    copyDirectory(sourceDir, destDir);
    console.log('Frontend build copied successfully!');
  } catch (error) {
    console.error('Error copying frontend build:', error);
    process.exit(1);
  }
}

// Run the main function
main();
