@echo off
echo Starting server on port 3000 with database connection...
echo.

REM Set environment variables
set PORT=3000
set VERCEL=true
set NODE_TLS_REJECT_UNAUTHORIZED=0
set PGSSLMODE=no-verify
set PG_SSL_REJECT_UNAUTHORIZED=0
set NODE_ENV=development

REM Run the server
echo Running: node --import tsx server/index.ts
node --import tsx server/index.ts

REM If tsx fails, try with node directly
if %ERRORLEVEL% NEQ 0 (
  echo.
  echo tsx command failed, trying with node...
  node server/index.js
)

pause
