import { Ch<PERSON>ronR<PERSON>, Wrench, Clock, MapPin, Phone, CalendarDays, Star, ArrowRight, BatteryCharging } from "lucide-react";
import { ContentOnlyLayout } from "@/components/layout/content-only-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";

interface RepairService {
  id: number;
  name: string;
  description: string;
  estimatedTimeframe: string;
  startingPrice: string;
  icon: JSX.Element;
  benefits: string[];
}

const repairServices: RepairService[] = [
  {
    id: 1,
    name: "Electronics Repair",
    description: "Professional repair services for smartphones, tablets, laptops, and other electronic devices. We fix screens, batteries, charging ports, and more.",
    estimatedTimeframe: "1-3 days depending on parts availability",
    startingPrice: "₹500",
    icon: <BatteryCharging className="h-10 w-10 text-[#8bbe1b]" />,
    benefits: [
      "Extends device lifespan by 2-3 years on average",
      "Uses genuine or high-quality compatible parts",
      "90-day warranty on all repairs",
      "Free diagnostic assessment",
      "Data protection guaranteed"
    ]
  },
  {
    id: 2,
    name: "Appliance Repair",
    description: "Expert repair for refrigerators, washing machines, dishwashers, microwaves, and other household appliances. We diagnose and fix mechanical and electrical issues.",
    estimatedTimeframe: "1-5 days depending on complexity",
    startingPrice: "₹800",
    icon: <Wrench className="h-10 w-10 text-[#8bbe1b]" />,
    benefits: [
      "Saves up to 70% compared to replacement costs",
      "Reduces appliance waste in landfills",
      "In-home service available for large appliances",
      "6-month warranty on parts and labor",
      "Preventative maintenance tips provided"
    ]
  },
  {
    id: 3,
    name: "Furniture Restoration",
    description: "Breathe new life into your furniture with our restoration services. We repair, refinish, reupholster, and restore wooden and upholstered furniture.",
    estimatedTimeframe: "5-14 days depending on service",
    startingPrice: "₹1,200",
    icon: <Wrench className="h-10 w-10 text-[#8bbe1b]" />,
    benefits: [
      "Preserves heirloom and quality pieces",
      "Eco-friendly materials and processes",
      "Custom color matching available",
      "Free pickup and delivery for large items",
      "Before and after documentation provided"
    ]
  },
  {
    id: 4,
    name: "Clothing & Textile Repair",
    description: "Professional mending, alterations, and repairs for clothing and textiles. We fix tears, replace zippers, patch holes, and perform custom alterations.",
    estimatedTimeframe: "1-7 days depending on workload",
    startingPrice: "₹200",
    icon: <Wrench className="h-10 w-10 text-[#8bbe1b]" />,
    benefits: [
      "Extends garment life and reduces textile waste",
      "Expert tailoring and invisible mending",
      "Reinforcement to prevent future damage",
      "Eco-friendly thread and material options",
      "Rush service available for urgent needs"
    ]
  }
];

interface RepairWorkshop {
  id: number;
  title: string;
  date: string;
  time: string;
  location: string;
  instructor: string;
  price: string;
  spots: number;
}

const upcomingWorkshops: RepairWorkshop[] = [
  {
    id: 1,
    title: "DIY Smartphone Screen Replacement",
    date: "June 15, 2025",
    time: "10:00 AM - 12:00 PM",
    location: "W2W Main Store, Faridabad",
    instructor: "Rahul Verma",
    price: "₹1,500 (includes repair kit)",
    spots: 8
  },
  {
    id: 2,
    title: "Basic Appliance Troubleshooting",
    date: "June 22, 2025",
    time: "2:00 PM - 4:30 PM",
    location: "W2W Main Store, Faridabad",
    instructor: "Priya Sharma",
    price: "₹800",
    spots: 12
  },
  {
    id: 3,
    title: "Furniture Upcycling Workshop",
    date: "July 5, 2025",
    time: "11:00 AM - 3:00 PM",
    location: "W2W Furniture Center, Faridabad",
    instructor: "Amit Kapoor",
    price: "₹1,800 (materials included)",
    spots: 6
  },
  {
    id: 4,
    title: "Clothing Repair Basics",
    date: "July 12, 2025",
    time: "1:00 PM - 3:00 PM",
    location: "W2W Main Store, Faridabad",
    instructor: "Neha Gupta",
    price: "₹700 (includes starter kit)",
    spots: 10
  }
];

const RepairServicesPage = () => {
  return (
    <ContentOnlyLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-[#8bbe1b] transition-colors">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <span>Repair Services</span>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
              Repair Services
            </h1>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Why replace when you can repair? Our professional repair services extend the life of your belongings, 
              reduce waste, and save you money. Discover how we can help restore your items to working condition.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Card className="border-amber-100 shadow-sm bg-amber-50">
              <CardHeader className="pb-2">
                <div className="bg-[#8bbe1b]/20 p-3 rounded-full w-fit">
                  <Wrench className="h-6 w-6 text-[#8bbe1b]" />
                </div>
                <CardTitle className="mt-3">Expert Technicians</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Our certified repair specialists have years of experience and ongoing training to provide high-quality repairs.
                </p>
              </CardContent>
            </Card>

            <Card className="border-amber-100 shadow-sm bg-amber-50">
              <CardHeader className="pb-2">
                <div className="bg-[#8bbe1b]/20 p-3 rounded-full w-fit">
                  <Clock className="h-6 w-6 text-[#8bbe1b]" />
                </div>
                <CardTitle className="mt-3">Quick Turnaround</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Most repairs are completed within 1-5 days, with express service available for urgent needs.
                </p>
              </CardContent>
            </Card>

            <Card className="border-amber-100 shadow-sm bg-amber-50">
              <CardHeader className="pb-2">
                <div className="bg-[#8bbe1b]/20 p-3 rounded-full w-fit">
                  <Star className="h-6 w-6 text-[#8bbe1b]" />
                </div>
                <CardTitle className="mt-3">Quality Guarantee</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  All repairs come with a warranty. If the same issue recurs within the warranty period, we'll fix it at no cost.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-bold mb-8 text-gray-800 text-center">Our Repair Services</h2>
            
            <Tabs defaultValue="1" className="w-full">
              <TabsList className="w-full grid grid-cols-2 md:grid-cols-4 mb-8">
                {repairServices.map((service) => (
                  <TabsTrigger key={service.id} value={service.id.toString()} className="text-sm">
                    {service.name}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {repairServices.map((service) => (
                <TabsContent key={service.id} value={service.id.toString()}>
                  <Card className="border-amber-100 overflow-hidden">
                    <div className="bg-[#8bbe1b]/10 px-6 py-4">
                      <div className="flex items-center gap-4">
                        {service.icon}
                        <div>
                          <h3 className="text-xl font-bold text-gray-800">{service.name}</h3>
                          <p className="text-gray-600">{service.description}</p>
                        </div>
                      </div>
                    </div>
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                          <div className="flex items-center gap-2 mb-4">
                            <Clock className="h-5 w-5 text-[#8bbe1b]" />
                            <div>
                              <h4 className="text-sm font-semibold text-gray-700">Estimated Timeframe:</h4>
                              <p className="text-sm text-gray-600">{service.estimatedTimeframe}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="text-[#8bbe1b] font-bold">₹</div>
                            <div>
                              <h4 className="text-sm font-semibold text-gray-700">Starting Price:</h4>
                              <p className="text-sm text-gray-600">{service.startingPrice}</p>
                            </div>
                          </div>
                          <div className="mt-6">
                            <Button className="bg-[#8bbe1b] hover:bg-[#7aa518] text-white">
                              Schedule a Repair
                            </Button>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-gray-700 mb-3">Benefits:</h4>
                          <ul className="space-y-2">
                            {service.benefits.map((benefit, index) => (
                              <li key={index} className="flex items-start gap-2 text-sm">
                                <ArrowRight className="h-4 w-4 text-[#8bbe1b] mt-1 flex-shrink-0" />
                                <span className="text-gray-600">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          </div>

          <div className="bg-amber-50 border border-amber-100 rounded-lg p-6 mb-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Repair Process</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="flex flex-col items-center text-center">
                <div className="bg-[#8bbe1b] rounded-full h-12 w-12 flex items-center justify-center mb-4">
                  <span className="text-white text-xl font-bold">1</span>
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Diagnostic Assessment</h3>
                <p className="text-sm text-gray-600">
                  We thoroughly examine your item to identify all issues and provide an accurate quote.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-[#8bbe1b] rounded-full h-12 w-12 flex items-center justify-center mb-4">
                  <span className="text-white text-xl font-bold">2</span>
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Repair Quote</h3>
                <p className="text-sm text-gray-600">
                  You'll receive a detailed price estimate and timeframe for the necessary repairs.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-[#8bbe1b] rounded-full h-12 w-12 flex items-center justify-center mb-4">
                  <span className="text-white text-xl font-bold">3</span>
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Expert Repair</h3>
                <p className="text-sm text-gray-600">
                  Our skilled technicians perform the repairs using quality parts and materials.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-[#8bbe1b] rounded-full h-12 w-12 flex items-center justify-center mb-4">
                  <span className="text-white text-xl font-bold">4</span>
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Quality Testing</h3>
                <p className="text-sm text-gray-600">
                  We thoroughly test all repairs before returning your item, ensuring everything works perfectly.
                </p>
              </div>
            </div>
          </div>

          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">DIY Repair Workshops</h2>
              <Button variant="outline" className="border-[#8bbe1b] text-[#8bbe1b]">
                View All Workshops
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {upcomingWorkshops.slice(0, 2).map((workshop) => (
                <Card key={workshop.id} className="border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex justify-between items-start">
                      <span>{workshop.title}</span>
                      <Badge className="bg-[#8bbe1b]">
                        {workshop.spots} spots left
                      </Badge>
                    </CardTitle>
                    <CardDescription>Learn valuable repair skills from our experts</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-5 w-5 text-[#8bbe1b]" />
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">{workshop.date}</span> • {workshop.time}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-[#8bbe1b]" />
                        <div className="text-sm text-gray-600">{workshop.location}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-5 w-5 text-[#8bbe1b]" />
                        <div className="text-sm text-gray-600">Instructor: {workshop.instructor}</div>
                      </div>
                      <div className="pt-2">
                        <p className="text-sm font-semibold text-gray-800">{workshop.price}</p>
                      </div>
                      <Button className="w-full mt-2 bg-[#8bbe1b] hover:bg-[#7aa518] text-white">
                        Reserve a Spot
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="bg-[#8bbe1b]/10 rounded-lg p-6 mb-8">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="md:w-2/3">
                <h3 className="text-xl font-bold mb-3 text-gray-800">Can't bring your item in?</h3>
                <p className="text-gray-600 mb-4">
                  We offer pickup and delivery services for larger items that are difficult to transport. 
                  For select items, we also provide on-site repair services at your home or office.
                </p>
                <Button className="bg-[#8bbe1b] hover:bg-[#7aa518] text-white">
                  Schedule Pickup
                </Button>
              </div>
              <div className="md:w-1/3 bg-white p-4 rounded-lg border border-amber-100">
                <h4 className="font-semibold text-gray-800 mb-2">Contact Repair Services</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-[#8bbe1b]" />
                    <span className="text-gray-600">+91 9519564623</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-[#8bbe1b]" />
                    <span className="text-gray-600">All W2W locations</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-[#8bbe1b]" />
                    <span className="text-gray-600">Mon-Sat: 10AM-7PM</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h3 className="text-xl font-bold mb-4 text-gray-800">Explore Related Sustainability Resources</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/recycling-guide" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Recycling Guide</h4>
                <p className="text-sm text-gray-600">Learn how to recycle properly</p>
              </Link>
              <Link href="/dropoff-locations" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Drop-off Locations</h4>
                <p className="text-sm text-gray-600">Find nearby recycling centers</p>
              </Link>
              <Link href="/trade-in" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Trade-in Program</h4>
                <p className="text-sm text-gray-600">Exchange your old items</p>
              </Link>
              <Link href="/sustainability" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Sustainability Tips</h4>
                <p className="text-sm text-gray-600">Live more eco-friendly</p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ContentOnlyLayout>
  );
};

export default RepairServicesPage;