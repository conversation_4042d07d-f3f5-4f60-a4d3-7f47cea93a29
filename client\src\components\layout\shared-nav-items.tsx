import { Link } from "wouter";

export const NAV_ITEMS = [
  { path: "/new-arrivals", label: "Recently Landed" },
  { path: "/on-sale", label: "On Sale" },
  { path: "/popular-items", label: "Popular" }
];

interface SharedNavItemsProps {
  onItemClick?: () => void;
  className?: string;
}

export function SharedNavItems({ onItemClick, className = "" }: SharedNavItemsProps) {
  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      {NAV_ITEMS.map((item) => (
        <Link
          key={item.path}
          href={item.path}
          onClick={onItemClick}
          className="text-sm font-medium hover:text-[#6a9816] px-2 py-1"
        >
          {item.label}
        </Link>
      ))}
    </div>
  );
}