import { createContext, ReactN<PERSON>, useContext, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { insertUserSchema, User as SelectUser, InsertUser } from "@shared/schema";
import { getQueryFn, apiRequest, queryClient } from "../lib/queryClient";
import { useToast } from "@/hooks/use-toast";

type LoginData = {
  email: string;
  password: string;
};

type AuthContextType = {
  user: SelectUser | null;
  isLoading: boolean;
  error: Error | null;
  login: (options: {
    variables: LoginData;
    onSuccess?: (data: SelectUser) => void;
  }) => void;
  logout: (options?: {
    onSuccess?: () => void;
  }) => void;
  register: (options: {
    variables: InsertUser;
    onSuccess?: (data: SelectUser) => void;
  }) => void;
};

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get stored user data
  const storedUserId = localStorage.getItem('userId');
  const storedUserData = localStorage.getItem('userData');

  // Initialize user from localStorage if available
  useEffect(() => {
    const initializeUserFromStorage = async () => {
      try {
        if (storedUserId && storedUserData) {
          const userData = JSON.parse(storedUserData);
          queryClient.setQueryData(["/api/user", { userId: storedUserId }], userData);
        }
      } catch (error) {
        console.error('Error initializing user from storage:', error);
      }
    };
    initializeUserFromStorage();
  }, []);

  const {
    data: user,
    error,
    isLoading,
  } = useQuery<SelectUser | null, Error>({
    queryKey: ["/api/user", storedUserId ? { userId: storedUserId } : null],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: !!storedUserId,
    staleTime: 10 * 60 * 1000, // 10 minutes - user data doesn't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes garbage collection
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    onError: (error) => {
      if (error.message !== "Not authenticated") {
        toast({
          title: "Error",
          description: "There was a problem loading your profile. Please try again.",
          variant: "destructive",
        });
      }
    },
  });

  const { mutate: loginMutation } = useMutation({
    mutationFn: async (credentials: LoginData) => {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: credentials.email.trim(),
          password: credentials.password
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Server error' }));
        throw new Error(errorData.error || `Login failed (${response.status})`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data.user) {
        localStorage.setItem('userData', JSON.stringify(data.user));
        if (data.user.id) {
          localStorage.setItem('userId', data.user.id.toString());
        }
        if (data.session?.access_token) {
          localStorage.setItem('token', data.session.access_token);
        }
        queryClient.setQueryData(["/api/user"], data.user);
      }
    },
    onError: (error: Error) => {
      console.error('Login failed:', error);
      toast({
        title: 'Login Failed',
        description: error.message || 'Please check your credentials and try again',
        variant: 'destructive',
      });
    },
  });

  // Wrapper function for login that handles the options parameter
  const login = (options: {
    variables: LoginData;
    onSuccess?: (data: SelectUser) => void;
  }) => {
    return loginMutation(options.variables, {
      onSuccess: (data) => {
        if (options.onSuccess && data.user) {
          options.onSuccess(data.user);
        }
      }
    });
  };

  const { mutate: registerMutation } = useMutation({
    mutationFn: async (credentials: InsertUser) => {
      console.log('Registration credentials:', credentials); // Debug log

      // Validate required fields before sending request
      if (!credentials.username || !credentials.email || !credentials.password || !credentials.fullName) {
        console.error('Missing required fields:', credentials);
        throw new Error('All fields are required for registration');
      }

      try {
        const response = await fetch('/api/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: credentials.username,
            email: credentials.email,
            password: credentials.password,
            fullName: credentials.fullName
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Registration failed with status ${response.status}`);
        }

        const userData = await response.json();

        // Store user data in localStorage
        if (userData.user) {
          localStorage.setItem('userData', JSON.stringify(userData.user));
          if (userData.user.id) {
            localStorage.setItem('userId', userData.user.id.toString());
          }
        }

        return userData.user;
      } catch (error: any) {
        console.error('Registration request error:', error);
        throw error;
      }
    },
    onSuccess: (user: SelectUser) => {
      queryClient.setQueryData(["/api/user"], user);
      queryClient.setQueryData(["/api/cart"], []);
      queryClient.setQueryData(["/api/wishlist"], []);
      toast({
        title: "Registration successful",
        description: "Welcome to EliteShop!",
      });
    },
    onError: (error: Error) => {
      console.error('Registration error:', error); // Debug log
      toast({
        title: "Registration failed",
        description: error.message || "Failed to create account. Please try again.",
        variant: "destructive"
      });
    },
  });

  const { mutate: logoutMutation } = useMutation({
    mutationFn: async () => {
      const userId = user?.id;
      if (userId) {
        await apiRequest("POST", "/api/logout", { userId }, { maxRetries: 3 });
      } else {
        await apiRequest("POST", "/api/logout", {}, { maxRetries: 3 });
      }
    },
    onSuccess: () => {
      queryClient.setQueryData(["/api/user"], null);
      queryClient.setQueryData(["/api/cart"], []);
      queryClient.setQueryData(["/api/wishlist"], []);
      queryClient.invalidateQueries();

      sessionStorage.clear();
      localStorage.clear();

      window.location.href = '/auth';
    },
    onError: (error: Error) => {
      console.error("Logout error:", error);
      queryClient.setQueryData(["/api/user"], null);
      queryClient.setQueryData(["/api/cart"], []);
      queryClient.setQueryData(["/api/wishlist"], []);

      sessionStorage.clear();
      localStorage.clear();

      window.location.href = '/auth';
    },
  });

  // Wrapper function for logout that handles the options parameter
  const logout = (options?: {
    onSuccess?: () => void;
  }) => {
    return logoutMutation(undefined, {
      onSuccess: () => {
        if (options?.onSuccess) {
          options.onSuccess();
        }
      }
    });
  };

  const safeUser = user ? {
    ...user,
    username: user.username || 'User',
    email: user.email || '',
    fullName: user.fullName || '',
    address: user.address || '',
    phone: user.phone || '',
    isLoggedIn: user.isLoggedIn || false,
  } : null;

  // Wrapper function for register that handles the options parameter
  const register = (options: {
    variables: InsertUser;
    onSuccess?: (data: SelectUser) => void;
  }) => {
    return registerMutation(options.variables, {
      onSuccess: (data) => {
        if (options.onSuccess) {
          options.onSuccess(data);
        }
      }
    });
  };

  return (
    <AuthContext.Provider
      value={{
        user: safeUser,
        isLoading,
        error,
        login,
        logout,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}


