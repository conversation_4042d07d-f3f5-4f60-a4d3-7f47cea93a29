// Development server launcher that disables SSL certificate validation
// DO NOT use this in production!

// Explicitly disable SSL certificate validation for PostgreSQL connections
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
process.env.PGSSLMODE = 'no-verify';
process.env.PG_TLS_REJECT_UNAUTHORIZED = '0';

// Load development environment variables
import { config } from 'dotenv';
config({ path: '.env.development' });

// Set environment to development
process.env.NODE_ENV = 'development';

// Run the development server using the child_process exec method
// This allows us to run the TypeScript file directly
import { spawn } from 'child_process';

console.log('🚀 Starting development server with SSL certificate validation disabled...');

// Use spawn to run in a new process and pipe output to the current console
const childProcess = spawn('npx', ['tsx', 'server/index.ts'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_TLS_REJECT_UNAUTHORIZED: '0',
    PGSSLMODE: 'no-verify',
    PG_TLS_REJECT_UNAUTHORIZED: '0'
  }
});

// Handle process errors
childProcess.on('error', (error) => {
  console.error('Error starting server:', error);
});

// Handle process exit
process.on('SIGINT', () => {
  childProcess.kill('SIGINT');
  process.exit(0);
});
