// Environment variables for Netlify functions
export const env = {
  DATABASE_URL: process.env.DATABASE_URL || "postgres://postgres.nanrlqojfklgdpsmwoxm:<EMAIL>:6543/postgres?sslmode=require",
  POSTGRES_URL: process.env.POSTGRES_URL || "postgres://postgres.nanrlqojfklgdpsmwoxm:<EMAIL>:6543/postgres?sslmode=require",
  POSTGRES_HOST: process.env.POSTGRES_HOST || "db.nanrlqojfklgdpsmwoxm.supabase.co",
  POSTGRES_USER: process.env.POSTGRES_USER || "postgres",
  POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD || "drcFplupDCllkvKs",
  POSTGRES_DATABASE: process.env.POSTGRES_DATABASE || "postgres",
  NODE_ENV: process.env.NODE_ENV || "production",
  NETLIFY: "true"
};
