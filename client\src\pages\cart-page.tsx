import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import {
  Minus,
  Plus,
  Trash2,
  ShoppingCart,
  RefreshCw,
  ChevronRight,
  ArrowLeft
} from "lucide-react";
import { Loader2 } from "lucide-react";

export default function CartPage() {
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const [couponCode, setCouponCode] = useState("");
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [itemToRemove, setItemToRemove] = useState<number | null>(null);
  const [isClearCartDialogOpen, setIsClearCartDialogOpen] = useState(false);

  // Redirect to login if not authenticated
  if (!user) {
    navigate("/auth");
    return null;
  }

  // Store userId in localStorage for future requests
  useEffect(() => {
    if (user && user.id) {
      localStorage.setItem('userId', user.id.toString());
    }
  }, [user]);

  // Fetch cart items
  const {
    data: cartItems = [],
    isLoading,
    error,
    refetch: refetchCart
  } = useQuery({
    queryKey: ["/api/cart", user ? { userId: user.id } : null],
    queryFn: async ({ queryKey }) => {
      console.log('Fetching cart items with query key:', queryKey);

      // Extract userId from query key
      const userId = user?.id;

      if (!userId) {
        console.error('No userId available for fetching cart items');
        return [];
      }

      console.log(`Making direct fetch request for cart items with userId: ${userId}`);

      // Make a direct fetch request to ensure it works
      const response = await fetch(`/api/cart?userId=${userId}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to fetch cart items: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Cart data received:', data);

      if (!data || !Array.isArray(data)) {
        console.warn('Cart data is not an array, returning empty array');
        return [];
      }

      return data.filter(item => item && item.product); // Filter out invalid items
    },
    enabled: !!user,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching cart items:', error);
      toast({
        title: "Cart Error",
        description: "Failed to load cart items. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Force refetch cart items when the component mounts
  useEffect(() => {
    if (user) {
      console.log('Refetching cart items on mount');
      refetchCart();
    }
  }, [refetchCart, user]);

  // Update cart item quantity
  const updateQuantityMutation = useMutation({
    mutationFn: async ({ id, quantity }: { id: number; quantity: number }) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to update your cart");
      }

      console.log(`Updating cart item ${id} quantity to ${quantity} for user ${user.id}`);

      const response = await fetch(`/api/cart/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quantity,
          userId: parseInt(String(user.id))
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to update cart: ${response.status} ${response.statusText}`);
      }
    },
    onSuccess: () => {
      if (user) {
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }
    },
    onError: (error) => {
      console.error('Error updating cart quantity:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update item quantity. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Remove item from cart
  const removeItemMutation = useMutation({
    mutationFn: async (id: number) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to remove items from your cart");
      }

      console.log(`Removing cart item ${id} for user ${user.id}`);

      // Use direct fetch with credentials to ensure cookies are sent
      const response = await fetch(`/api/cart/${id}?userId=${user.id}`, {
        method: 'DELETE',
        credentials: 'include', // Important: include credentials for cookies
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to remove from cart: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully removed cart item ${id}, status: ${response.status}`);
      return true;
    },
    onSuccess: () => {
      console.log('Successfully removed item from cart');

      if (user) {
        // Force invalidate and refetch the cart queries
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }

      toast({
        title: "Item removed",
        description: "The item has been removed from your cart.",
      });
    },
    onError: (error) => {
      console.error('Error removing cart item:', error);
      toast({
        title: "Remove Failed",
        description: "Failed to remove item from cart. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Clear cart
  const clearCartMutation = useMutation({
    mutationFn: async () => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to clear your cart");
      }

      console.log(`Clearing cart for user ${user.id}`);

      // Use direct fetch with credentials to ensure cookies are sent
      const response = await fetch(`/api/cart?userId=${user.id}`, {
        method: 'DELETE',
        credentials: 'include', // Important: include credentials for cookies
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to clear cart: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully cleared cart, status: ${response.status}`);
      return true;
    },
    onSuccess: () => {
      console.log('Successfully cleared cart');

      if (user) {
        // Force invalidate and refetch the cart queries
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }

      toast({
        title: "Cart cleared",
        description: "All items have been removed from your cart.",
      });
    },
    onError: (error) => {
      console.error('Error clearing cart:', error);
      toast({
        title: "Clear Failed",
        description: "Failed to clear your cart. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Handle quantity change
  const handleDecrease = (id: number, currentQuantity: number) => {
    if (currentQuantity > 1) {
      updateQuantityMutation.mutate({ id, quantity: currentQuantity - 1 });
    }
  };

  const handleIncrease = (id: number, currentQuantity: number) => {
    updateQuantityMutation.mutate({ id, quantity: currentQuantity + 1 });
  };

  // Handle remove item
  const handleRemoveClick = (id: number) => {
    setItemToRemove(id);
    setIsRemoveDialogOpen(true);
  };

  const confirmRemove = () => {
    if (itemToRemove !== null) {
      removeItemMutation.mutate(itemToRemove);
      setIsRemoveDialogOpen(false);
      setItemToRemove(null);
    }
  };

  // Handle clear cart
  const handleClearCart = () => {
    setIsClearCartDialogOpen(true);
  };

  const confirmClearCart = () => {
    clearCartMutation.mutate();
    setIsClearCartDialogOpen(false);
  };

  // Calculate cart totals
  const subtotal = cartItems?.reduce((total, item) => {
    const price = item.product.discountPrice || item.product.price;
    return total + (price * item.quantity);
  }, 0) || 0;

  // Shipping is free if subtotal >= 50
  const shipping = subtotal >= 50 ? 0 : 9.99;
  const total = subtotal + shipping;

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-secondary" />
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-sm p-8 max-w-md text-center">
            <div className="text-red-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h1 className="text-xl font-bold text-primary mb-2">Error Loading Cart</h1>
            </div>
            <p className="text-gray-500 mb-4">There was a problem loading your cart. Please try again.</p>
            <div className="flex space-x-4 justify-center">
              <Button
                onClick={() => refetchCart()}
                variant="default"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry
                  </>
                )}
              </Button>
              <Button
                onClick={() => navigate("/products")}
                variant="outline"
              >
                Continue Shopping
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!cartItems || cartItems.length === 0) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-sm p-8 max-w-md text-center">
            <div className="flex justify-center mb-4">
              <ShoppingCart className="h-16 w-16 text-gray-300" />
            </div>
            <h1 className="text-2xl font-bold text-primary mb-2">Your Cart is Empty</h1>
            <p className="text-gray-500 mb-6">Looks like you haven't added any products to your cart yet.</p>
            <Button asChild>
              <Link href="/products">Start Shopping</Link>
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 bg-background">
        {/* Breadcrumb */}
        <div className="bg-white border-y border-gray-200">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center text-sm">
              <Link href="/" className="text-gray-500 hover:text-secondary">Home</Link>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <span className="text-primary font-medium">Shopping Cart</span>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl md:text-3xl font-bold font-poppins text-primary mb-6">
            Your Shopping Cart
          </h1>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Cart Items */}
            <div className="lg:w-2/3">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-medium">
                      Cart Items ({cartItems.length})
                    </h2>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearCart}
                      disabled={clearCartMutation.isPending}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Clear Cart
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {cartItems.map((item) => (
                      <div key={item.id} className="flex flex-col sm:flex-row items-start sm:items-center py-4 border-b border-gray-200">
                        <div className="w-20 h-20 rounded-md overflow-hidden">
                          <img
                            src={item.product.imageUrl}
                            alt={item.product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1 sm:ml-4 mt-2 sm:mt-0">
                          <Link href={`/products/${item.product.id}`}>
                            <h3 className="font-medium text-primary hover:text-secondary">{item.product.name}</h3>
                          </Link>
                          <p className="text-sm text-gray-500">
                            Category: {item.product.categoryId === 1 ? "Men's Fashion" :
                                       item.product.categoryId === 2 ? "Women's Fashion" :
                                       item.product.categoryId === 3 ? "Electronics" :
                                       item.product.categoryId === 4 ? "Home & Decor" : "Other"}
                          </p>
                          {item.product.discountPrice && (
                            <p className="text-sm text-gray-500">
                              <span className="line-through">${item.product.price.toFixed(2)}</span>
                              {" "}
                              <span className="text-secondary font-medium">${item.product.discountPrice.toFixed(2)}</span>
                            </p>
                          )}
                          {!item.product.discountPrice && (
                            <p className="text-sm text-gray-600">${item.product.price.toFixed(2)}</p>
                          )}
                        </div>
                        <div className="flex items-center mt-2 sm:mt-0">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 rounded-full"
                            onClick={() => handleDecrease(item.id, item.quantity)}
                            disabled={item.quantity <= 1 || updateQuantityMutation.isPending}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="mx-3 text-sm">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 rounded-full"
                            onClick={() => handleIncrease(item.id, item.quantity)}
                            disabled={updateQuantityMutation.isPending}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="flex items-center mt-2 sm:mt-0 sm:ml-4">
                          <span className="font-medium text-primary text-lg">
                            ${((item.product.discountPrice || item.product.price) * item.quantity).toFixed(2)}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="ml-2 text-gray-500 hover:text-red-500"
                            onClick={() => handleRemoveClick(item.id)}
                            disabled={removeItemMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6">
                    <Button variant="outline" asChild className="flex items-center">
                      <Link href="/products">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Continue Shopping
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:w-1/3">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-6">
                  <h2 className="text-lg font-medium mb-4">Order Summary</h2>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping</span>
                      <span className="font-medium">{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</span>
                    </div>
                    {shipping > 0 && (
                      <div className="text-sm text-gray-500">
                        Add ${(50 - subtotal).toFixed(2)} more to get free shipping
                      </div>
                    )}
                  </div>

                  <Separator className="my-4" />

                  <div className="flex justify-between mb-6">
                    <span className="text-lg font-medium">Total</span>
                    <span className="text-xl font-bold text-primary">${total.toFixed(2)}</span>
                  </div>

                  <div className="mb-6">
                    <div className="flex space-x-2 mb-3">
                      <Input
                        placeholder="Enter coupon code"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                      />
                      <Button variant="outline">Apply</Button>
                    </div>
                    <div className="text-sm text-gray-500">
                      Enter your coupon code if you have one
                    </div>
                  </div>

                  <Button asChild className="w-full bg-secondary hover:bg-secondary/90">
                    <Link href="/checkout">
                      Proceed to Checkout
                    </Link>
                  </Button>

                  <div className="mt-6">
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                      <span>Secure checkout</span>
                      <span>100% Protected</span>
                    </div>
                    <div className="flex space-x-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                        <rect width="40" height="24" rx="4" fill="#1A1F71"/>
                        <path d="M16.9 7H23.1L20 17H13.8L16.9 7Z" fill="#FFFFFF"/>
                        <path d="M26.2 7C25.2 7 24.3 7.6 24 8.5L20 17H24.5L25 15.5H29L29.3 17H33.5L31 7H26.2ZM26 13L27 10L28 13H26Z" fill="#FFFFFF"/>
                        <path d="M11.8 7L8.6 14L8.2 12.5L8.2 12.5L7 8C6.9 7.4 6.4 7 5.8 7H0.2L0.1 7.2C1.1 7.4 2.3 7.9 3 8.2L6.5 17H11L16.5 7H11.8Z" fill="#FFFFFF"/>
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                        <rect width="40" height="24" rx="4" fill="#252525"/>
                        <path d="M25.5 7C23.6 7 22 7.7 22 9.9C22 11.9 24.8 12.2 24.8 13.4C24.8 13.9 24.2 14.3 23.2 14.3C21.8 14.3 21 13.8 21 13.8L20.5 16.3C20.5 16.3 21.5 17 23.2 17C25.5 17 27.5 15.9 27.5 13.6C27.5 11.4 24.7 11 24.7 10C24.7 9.6 25.1 9.2 26.1 9.2C27.1 9.2 28 9.7 28 9.7L28.5 7.3C28.5 7.3 27.7 7 25.5 7Z" fill="#FFFFFF"/>
                        <path d="M34.7 7.1H32.2C31.5 7.1 31 7.3 30.7 8L27 17H29.7L30.2 15.5H33.5L33.8 17H36.2L34.7 7.1ZM30.7 13.5L32 9.5L32.8 13.5H30.7Z" fill="#FFFFFF"/>
                        <path d="M20.4 7.1L18.2 17H20.8L23 7.1H20.4Z" fill="#FFFFFF"/>
                        <path d="M16.9 7.1L14.2 13.9L13.4 8.4C13.3 7.6 12.7 7.1 12 7.1H7.2L7.1 7.3C8.1 7.5 9.1 7.9 9.9 8.3L12.2 17H15L19.7 7.1H16.9Z" fill="#FFFFFF"/>
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                        <rect width="40" height="24" rx="4" fill="#003087"/>
                        <path d="M17.1 9.8H14.9C14.8 9.8 14.6 9.9 14.6 10L13.5 16.4C13.5 16.5 13.6 16.6 13.7 16.6H14.8C14.9 16.6 15.1 16.5 15.1 16.4L15.4 14.7C15.4 14.6 15.5 14.5 15.7 14.5H16.4C18 14.5 19 13.6 19.3 12.1C19.4 11.5 19.3 10.9 19 10.5C18.6 10.1 18 9.8 17.1 9.8ZM17.4 12.1C17.2 13.0 16.5 13.0 15.8 13.0H15.5L15.8 11.4C15.8 11.3 15.9 11.3 16 11.3H16.1C16.6 11.3 17 11.3 17.2 11.6C17.4 11.7 17.4 11.9 17.4 12.1Z" fill="#FFFFFF"/>
                        <path d="M24.8 12H23.6C23.5 12 23.4 12.1 23.4 12.1L23.3 12.3L23.2 12.1C23 11.7 22.4 11.6 21.8 11.6C20.5 11.6 19.4 12.5 19.1 13.9C19 14.5 19.1 15.1 19.4 15.6C19.7 16 20.1 16.2 20.7 16.2C21.7 16.2 22.3 15.6 22.3 15.6L22.2 15.8C22.2 15.9 22.3 16 22.4 16H23.5C23.6 16 23.8 15.9 23.8 15.8L24.5 12.2C24.5 12.1 24.4 12 24.8 12ZM22.6 13.9C22.5 14.6 21.9 15.1 21.2 15.1C20.9 15.1 20.6 15 20.5 14.8C20.4 14.6 20.3 14.3 20.4 14C20.5 13.3 21.1 12.8 21.8 12.8C22.1 12.8 22.3 12.9 22.5 13.1C22.6 13.3 22.6 13.6 22.6 13.9Z" fill="#FFFFFF"/>
                        <path d="M30.2 12H29C28.9 12 28.8 12 28.7 12.1L26.6 13.6L25.9 12.2C25.8 12.1 25.7 12 25.6 12H24.4C24.3 12 24.2 12.1 24.2 12.2L25.5 15.9L24.3 16.8C24.2 16.9 24.2 17 24.3 17.1C24.3 17.1 24.4 17.2 24.5 17.2H25.7C25.8 17.2 25.9 17.1 26 17.1L30.3 12.3C30.3 12.2 30.3 12.1 30.2 12Z" fill="#FFFFFF"/>
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                        <rect width="40" height="24" rx="4" fill="#FFB600"/>
                        <path d="M20 16C22.2091 16 24 14.2091 24 12C24 9.79086 22.2091 8 20 8C17.7909 8 16 9.79086 16 12C16 14.2091 17.7909 16 20 16Z" fill="#FF5F00"/>
                        <path d="M19.2 12C19.2 10.8 19.8 9.6 20.7 8.9C19.9 8.3 18.8 8 17.7 8C15.1 8 13 9.8 13 12C13 14.2 15.1 16 17.7 16C18.8 16 19.9 15.7 20.7 15.1C19.8 14.4 19.2 13.2 19.2 12Z" fill="#EB001B"/>
                        <path d="M27 12C27 14.2 24.9 16 22.3 16C21.2 16 20.1 15.7 19.3 15.1C20.3 14.4 20.8 13.2 20.8 12C20.8 10.8 20.2 9.6 19.3 8.9C20.1 8.3 21.2 8 22.3 8C24.9 8 27 9.8 27 12Z" fill="#F79E1B"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Remove Item Confirmation Dialog */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Item</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this item from your cart?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmRemove}>Remove</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Clear Cart Confirmation Dialog */}
      <AlertDialog open={isClearCartDialogOpen} onOpenChange={setIsClearCartDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Clear Cart</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove all items from your cart?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmClearCart}>Clear</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
