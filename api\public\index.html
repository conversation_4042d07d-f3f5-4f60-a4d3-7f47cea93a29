<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ecommerce API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .endpoints {
            margin-top: 20px;
        }
        .endpoint {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .method {
            font-weight: bold;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>Ecommerce API</h1>
        <p class="success">✅ API is running</p>
        <p>This is the API for the Ecommerce application.</p>
    </div>
    
    <div class="card">
        <h2>Available Endpoints</h2>
        <div class="endpoints">
            <div class="endpoint">
                <span class="method">GET</span> /api/health
                <p>Check the health of the API and database connection</p>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/categories
                <p>Get all product categories</p>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/products
                <p>Get all products</p>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/products/:id
                <p>Get a specific product by ID</p>
            </div>
        </div>
    </div>
</body>
</html>
