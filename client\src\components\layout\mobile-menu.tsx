import { Link, useLocation, useSearch } from "wouter";
import {
  X, User, ShoppingCart, ChevronRight, LogIn, UserPlus,
  Heart, Package, Filter, SlidersHorizontal, Search
} from "lucide-react";
import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  cartItemCount: number;
  user: any;
}

export function MobileMenu({ isOpen, onClose, cartItemCount, user }: MobileMenuProps) {
  const [location, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const { logout } = useAuth();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      onClose();
    }
  };

  const isHomePage = location === "/";
  const isProductPage = location.includes("/products");

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50">
      <div className="fixed right-0 top-0 h-full w-[85%] max-w-sm bg-white shadow-2xl flex flex-col animate-in slide-in-from-right">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-black/10 px-6 py-5">
          <h2 className="text-xl font-semibold text-black">Menu</h2>
          <button
            onClick={onClose}
            className="rounded-full p-2 hover:bg-black/5 transition-colors"
          >
            <X className="h-6 w-6 text-black" />
          </button>
        </div>

        {/* Search Bar */}
        <div className="px-6 py-4 border-b border-black/10">
          <form onSubmit={handleSearch} className="relative">
            <input
              type="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search products..."
              className="w-full px-4 py-2 pl-10 bg-black/5 rounded-lg focus:outline-none focus:ring-2 focus:ring-black/20"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-black/40" />
            <button
              type="submit"
              className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-black/10 rounded-full"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </form>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Filter Toggle - Only show on products page */}
          {isProductPage && (
            <div className="p-6 border-b border-black/10">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center justify-between w-full py-3 px-4 bg-black/5 rounded-lg hover:bg-black/10 transition-colors"
              >
                <span className="flex items-center">
                  <Filter className="h-5 w-5 mr-2" />
                  <span className="font-medium">Filters</span>
                </span>
                <SlidersHorizontal className="h-5 w-5" />
              </button>

              {showFilters && (
                <div className="mt-4 space-y-4">
                  {/* Add your filter components here */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Categories</label>
                    <select className="w-full p-2 border border-black/10 rounded-md">
                      <option>All Categories</option>
                      <option>Eco-Friendly</option>
                      <option>Sustainable</option>
                      <option>Recycled</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Price Range</label>
                    <div className="flex gap-2">
                      <input
                        type="number"
                        placeholder="Min"
                        className="w-1/2 p-2 border border-black/10 rounded-md"
                      />
                      <input
                        type="number"
                        placeholder="Max"
                        className="w-1/2 p-2 border border-black/10 rounded-md"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Main Navigation */}
          <nav className="p-6 border-b border-black/10">
            {[
              { href: "/", label: "Home", icon: null },
              { href: "/products", label: "Shop", icon: null },
              { href: "/sustainability", label: "Eco-Tips", icon: null },
            ].map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
                onClick={onClose}
              >
                <span className="text-black font-medium">{item.label}</span>
                <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
              </Link>
            ))}
          </nav>

          {/* Quick Access */}
          <div className="p-6 border-b border-black/10">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-black/60 mb-4">
              Quick Access
            </h3>
            {[
              { href: "/new-arrivals", label: "Recently Landed", icon: Package },
              { href: "/on-sale", label: "On Sale", icon: null },
              { href: "/popular-items", label: "Popular", icon: null },
            ].map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
                onClick={onClose}
              >
                <span className="flex items-center">
                  {item.icon && <item.icon className="h-5 w-5 mr-3" />}
                  <span className="text-black font-medium">{item.label}</span>
                </span>
                <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
              </Link>
            ))}
          </div>

          {/* User Section */}
          <div className="p-6 border-b border-black/10">
            {user ? (
              <>
                <Link
                  href="/profile"
                  className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
                  onClick={onClose}
                >
                  <span className="flex items-center">
                    <User className="h-5 w-5 mr-3 text-black" />
                    <span className="font-medium text-black">My Profile</span>
                  </span>
                  <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
                </Link>
                <Link
                  href="/wishlist"
                  className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
                  onClick={onClose}
                >
                  <span className="flex items-center">
                    <Heart className="h-5 w-5 mr-3 text-black" />
                    <span className="font-medium text-black">Wishlist</span>
                  </span>
                  <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
                </Link>
              </>
            ) : (
              <>
                <Link
                  href="/auth"
                  className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
                  onClick={onClose}
                >
                  <span className="flex items-center">
                    <LogIn className="h-5 w-5 mr-3 text-black" />
                    <span className="font-medium text-black">Login</span>
                  </span>
                  <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
                </Link>
                <Link
                  href="/auth?tab=register"
                  className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
                  onClick={onClose}
                >
                  <span className="flex items-center">
                    <UserPlus className="h-5 w-5 mr-3 text-black" />
                    <span className="font-medium text-black">Register</span>
                  </span>
                  <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
                </Link>
              </>
            )}
          </div>

          {/* Cart Section */}
          <div className="p-6">
            <Link
              href={user ? "/cart" : "/auth"}
              className="flex items-center justify-between py-3.5 group hover:bg-black/5 rounded-lg px-3 transition-colors"
              onClick={onClose}
            >
              <span className="flex items-center">
                <ShoppingCart className="h-5 w-5 mr-3 text-black" />
                <span className="font-medium text-black">Cart</span>
              </span>
              <div className="flex items-center">
                {cartItemCount > 0 && (
                  <span className="bg-black text-white px-2 py-1 rounded-full text-xs mr-2">
                    {cartItemCount}
                  </span>
                )}
                <ChevronRight className="h-5 w-5 text-black/40 group-hover:text-black transition-colors" />
              </div>
            </Link>
          </div>
        </div>

        {/* Bottom Section - Only show when logged in */}
        {user && (
          <div className="mt-auto p-6 border-t border-black/10">
            <button
              onClick={() => {
                onClose();
                logout();
              }}
              className="w-full py-3 px-4 bg-black text-white rounded-lg hover:bg-black/90 transition-colors"
            >
              Sign Out
            </button>
          </div>
        )}
      </div>
    </div>
  );
}






