@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 228 29% 97%;
    --foreground: 212 20% 20%;
    
    --card: 0 0% 100%;
    --card-foreground: 212 20% 20%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 212 20% 20%;
    
    --primary: 212 20% 20%;
    --primary-foreground: 0 0% 98%;
    
    --secondary: 205 92% 44%;
    --secondary-foreground: 0 0% 98%;
    
    --accent: 166 100% 36%;
    --accent-foreground: 0 0% 98%;
    
    --muted: 220 14% 96%;
    --muted-foreground: 220 8% 46%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 205 92% 44%;
    
    --radius: 0.5rem;
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-sans;
  }
  
  /* Custom styles for nav links */
  .nav-link {
    position: relative;
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: hsl(var(--secondary));
    transition: width 0.3s;
  }
  
  .nav-link:hover::after {
    width: 100%;
  }
  
  /* Product card hover effects */
  .product-card {
    transition: transform 0.3s, box-shadow 0.3s;
  }
  
  .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
}

@layer utilities {
  .animate-in {
    animation: animate-in 0.3s ease-out;
  }
  
  .slide-in-from-right {
    animation: slide-in-from-right 0.3s ease-out;
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

