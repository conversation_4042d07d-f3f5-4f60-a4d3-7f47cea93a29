import { useEffect } from "react";
import { <PERSON> } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetClose } from "@/components/ui/sheet";
import { X } from "lucide-react";
import { CartItem } from "./cart-item";
import { Button } from "@/components/ui/button";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";

type CartModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export function CartModal({ isOpen, onClose }: CartModalProps) {
  // Import useAuth to get the user
  const { user } = useAuth();

  // Fetch cart items
  const {
    data: cartItems = [],
    isLoading,
    error,
    refetch: refetchCart
  } = useQuery({
    queryKey: ["/api/cart", user ? { userId: user.id } : null],
    enabled: isOpen && !!user,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    onError: (error) => {
      console.error('Error fetching cart items in modal:', error);
    },
    // Ensure we always get an array even if the API fails
    select: (data) => {
      if (!data || !Array.isArray(data)) {
        console.warn('Cart data is not an array, returning empty array');
        return [];
      }
      return data.filter(item => item && item.product); // Filter out invalid items
    }
  });

  // Clear cart mutation
  const clearCartMutation = useMutation({
    mutationFn: async () => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to clear your cart");
      }

      console.log('Clearing cart for user:', user.id);
      await apiRequest("DELETE", `/api/cart?userId=${user.id}`, undefined, { maxRetries: 3 });
    },
    onSuccess: () => {
      if (user) {
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }
    },
    onError: (error) => {
      console.error('Error clearing cart in modal:', error);
    }
  });

  // Calculate subtotal and total
  const subtotal = cartItems?.reduce((total, item) => {
    const price = item.product.discountPrice || item.product.price;
    return total + (price * item.quantity);
  }, 0) || 0;

  // Shipping is free if subtotal >= 50
  const shipping = subtotal >= 50 ? 0 : 9.99;
  const total = subtotal + shipping;

  if (error) {
    return (
      <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <SheetContent side="right" className="w-full max-w-md p-0 flex flex-col">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <SheetTitle>Your Cart</SheetTitle>
            <SheetClose asChild>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
              </Button>
            </SheetClose>
          </div>
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center">
              <div className="text-red-500 mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="font-semibold">Error loading cart</p>
              </div>
              <p className="text-gray-600 mb-4">There was a problem loading your cart items.</p>
              <Button
                variant="default"
                onClick={() => refetchCart()}
                disabled={isLoading}
                className="mb-2 w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <Loader2 className="mr-2 h-4 w-4" />
                    Try again
                  </>
                )}
              </Button>
              <SheetClose asChild>
                <Button variant="outline" className="w-full" onClick={onClose}>
                  Close
                </Button>
              </SheetClose>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent side="right" className="w-full max-w-md p-0 flex flex-col">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <SheetTitle>Your Cart {!isLoading && cartItems && `(${cartItems.length})`}</SheetTitle>
          <SheetClose asChild>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </SheetClose>
        </div>

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-secondary" />
          </div>
        ) : cartItems && cartItems.length > 0 ? (
          <>
            <div className="flex-1 overflow-y-auto p-4">
              {cartItems.map((item) => (
                <CartItem key={item.id} item={item} />
              ))}
            </div>

            <div className="p-4 border-t border-gray-200 bg-background">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium text-primary">${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Shipping:</span>
                <span className="font-medium text-primary">{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</span>
              </div>
              <div className="flex justify-between items-center mb-4">
                <span className="font-medium text-primary">Total:</span>
                <span className="font-bold text-lg text-primary">${total.toFixed(2)}</span>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <SheetClose asChild>
                  <Button asChild variant="outline">
                    <Link href="/cart" onClick={onClose}>View Cart</Link>
                  </Button>
                </SheetClose>
                <SheetClose asChild>
                  <Button asChild>
                    <Link href="/checkout" onClick={onClose}>Checkout</Link>
                  </Button>
                </SheetClose>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center">
              <p className="text-gray-500 mb-4">Your cart is empty</p>
              <SheetClose asChild>
                <Button asChild>
                  <Link href="/products" onClick={onClose}>Start Shopping</Link>
                </Button>
              </SheetClose>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
