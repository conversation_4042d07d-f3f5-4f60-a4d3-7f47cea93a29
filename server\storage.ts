// Import schema from local compatibility layer instead of @shared/schema.js
import {
  User, InsertUser, Product, InsertProduct,
  Category, InsertCategory, CartItem, InsertCartItem,
  Order, InsertOrder, OrderItem, InsertOrderItem,
  WishlistItem, InsertWishlistItem
} from "./schema.js";
import session from "express-session";
import { Store } from 'express-session';
import createMemoryStore from "memorystore";

// Storage interface
export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, data: Partial<User>): Promise<User | undefined>;

  // Product methods
  getProduct(id: number): Promise<Product | undefined>;
  getProducts(options?: {
    limit?: number;
    offset?: number;
    categoryId?: number;
    search?: string;
    isNew?: boolean;
    isPopular?: boolean;
    isSale?: boolean;
    minPrice?: number;
    maxPrice?: number;
  }): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;

  // Category methods
  getCategory(id: number): Promise<Category | undefined>;
  getCategories(): Promise<Category[]>;
  createCategory(category: InsertCategory): Promise<Category>;

  // Cart methods
  getCartItems(userId: number): Promise<CartItem[]>;
  getCartItemWithDetails(userId: number): Promise<(CartItem & { product: Product })[]>;
  addCartItem(item: InsertCartItem): Promise<CartItem>;
  updateCartItem(id: number, quantity: number): Promise<CartItem | undefined>;
  removeCartItem(id: number): Promise<boolean>;
  clearCart(userId: number): Promise<boolean>;

  // Order methods
  getOrder(id: number): Promise<Order | undefined>;
  getUserOrders(userId: number): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrderStatus(id: number, status: string): Promise<boolean>;

  // Order item methods
  getOrderItems(orderId: number): Promise<OrderItem[]>;
  createOrderItem(item: InsertOrderItem): Promise<OrderItem>;

  // Wishlist methods
  getWishlistItems(userId: number): Promise<WishlistItem[]>;
  getWishlistItemWithDetails(userId: number): Promise<(WishlistItem & { product: Product })[]>;
  addWishlistItem(item: InsertWishlistItem): Promise<WishlistItem>;
  removeWishlistItem(id: number): Promise<boolean>;
  isProductInWishlist(userId: number, productId: number): Promise<boolean>;

  // Initialize database (seed if needed)
  initialize?(): Promise<void>;

  // Session store
  sessionStore: any;
}

// In-memory storage implementation
export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private products: Map<number, Product>;
  private categories: Map<number, Category>;
  private cartItems: Map<number, CartItem>;
  private orders: Map<number, Order>;
  private orderItems: Map<number, OrderItem>;
  private wishlistItems: Map<number, WishlistItem>;

  sessionStore: Store;

  private userIdCounter: number;
  private productIdCounter: number;
  private categoryIdCounter: number;
  private cartItemIdCounter: number;
  private orderIdCounter: number;
  private orderItemIdCounter: number;
  private wishlistItemIdCounter: number;

  constructor() {
    this.users = new Map();
    this.products = new Map();
    this.categories = new Map();
    this.cartItems = new Map();
    this.orders = new Map();
    this.orderItems = new Map();
    this.wishlistItems = new Map();

    this.userIdCounter = 1;
    this.productIdCounter = 1;
    this.categoryIdCounter = 1;
    this.cartItemIdCounter = 1;
    this.orderIdCounter = 1;
    this.orderItemIdCounter = 1;
    this.wishlistItemIdCounter = 1;

    const MemoryStore = createMemoryStore(session);
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // 24 hours
    });

    // Initialize with some sample data
    this.initializeData();
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(userData: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const createdAt = new Date();
    const user: User = { ...userData, id, createdAt };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;

    const updatedUser = { ...user, ...data };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  // Product methods
  async getProduct(id: number): Promise<Product | undefined> {
    return this.products.get(id);
  }

  async getProducts(options: {
    limit?: number;
    offset?: number;
    categoryId?: number;
    search?: string;
    isNew?: boolean;
    isPopular?: boolean;
    isSale?: boolean;
    minPrice?: number;
    maxPrice?: number;
  } = {}): Promise<Product[]> {
    let result = Array.from(this.products.values());

    // Apply filters
    if (options.categoryId !== undefined) {
      result = result.filter(product => product.categoryId === options.categoryId);
    }

    // Apply price range filter
    if (options.minPrice !== undefined || options.maxPrice !== undefined) {
      result = result.filter(product => {
        const price = product.discountPrice || product.price;
        const meetsMinPrice = options.minPrice === undefined || price >= options.minPrice;
        const meetsMaxPrice = options.maxPrice === undefined || price <= options.maxPrice;
        return meetsMinPrice && meetsMaxPrice;
      });
    }

    if (options.search) {
      const searchLower = options.search.toLowerCase();
      result = result.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filters
    if (options.isNew === true) {
      result = result.filter(product => product.isNew === true);
    }
    if (options.isPopular === true) {
      result = result.filter(product => product.isPopular === true);
    }
    if (options.isSale === true) {
      result = result.filter(product => product.isSale === true);
    }

    // Sort products by newest first (based on createdAt)
    result.sort((a, b) => {
      const dateA = a.createdAt || new Date(0);
      const dateB = b.createdAt || new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    // Apply pagination
    if (options.offset !== undefined) {
      result = result.slice(options.offset);
    }

    if (options.limit !== undefined) {
      result = result.slice(0, options.limit);
    }

    return result;
  }

  async createProduct(productData: InsertProduct): Promise<Product> {
    const id = this.productIdCounter++;
    const createdAt = new Date();
    const product: Product = { ...productData, id, createdAt };
    this.products.set(id, product);
    return product;
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    return this.categories.get(id);
  }

  async getCategories(): Promise<Category[]> {
    return Array.from(this.categories.values());
  }

  async createCategory(categoryData: InsertCategory): Promise<Category> {
    const id = this.categoryIdCounter++;
    const category: Category = { ...categoryData, id };
    this.categories.set(id, category);
    return category;
  }

  // Cart methods
  async getCartItems(userId: number): Promise<CartItem[]> {
    return Array.from(this.cartItems.values()).filter(item => item.userId === userId);
  }

  async getCartItemWithDetails(userId: number): Promise<(CartItem & { product: Product })[]> {
    const items = await this.getCartItems(userId);
    return items.map(item => {
      const product = this.products.get(item.productId);
      if (!product) {
        throw new Error(`Product with ID ${item.productId} not found`);
      }
      return { ...item, product };
    });
  }

  async addCartItem(itemData: InsertCartItem): Promise<CartItem> {
    // Check if the item already exists in the cart
    const existingItem = Array.from(this.cartItems.values()).find(
      item => item.userId === itemData.userId && item.productId === itemData.productId
    );

    if (existingItem) {
      // Update quantity instead of adding a new item
      return this.updateCartItem(existingItem.id, existingItem.quantity + itemData.quantity) as Promise<CartItem>;
    }

    const id = this.cartItemIdCounter++;
    const createdAt = new Date();
    const cartItem: CartItem = { ...itemData, id, createdAt };
    this.cartItems.set(id, cartItem);
    return cartItem;
  }

  async updateCartItem(id: number, quantity: number): Promise<CartItem | undefined> {
    const item = this.cartItems.get(id);
    if (!item) return undefined;

    const updatedItem = { ...item, quantity };
    this.cartItems.set(id, updatedItem);
    return updatedItem;
  }

  async removeCartItem(id: number): Promise<boolean> {
    return this.cartItems.delete(id);
  }

  async clearCart(userId: number): Promise<boolean> {
    const itemsToRemove = Array.from(this.cartItems.values())
      .filter(item => item.userId === userId)
      .map(item => item.id);

    itemsToRemove.forEach(id => this.cartItems.delete(id));
    return true;
  }

  // Order methods
  async getOrder(id: number): Promise<Order | undefined> {
    return this.orders.get(id);
  }

  async getUserOrders(userId: number): Promise<Order[]> {
    return Array.from(this.orders.values())
      .filter(order => order.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async createOrder(orderData: InsertOrder): Promise<Order> {
    const id = this.orderIdCounter++;
    const createdAt = new Date();
    const order: Order = { ...orderData, id, createdAt };
    this.orders.set(id, order);
    return order;
  }

  async updateOrderStatus(id: number, status: string): Promise<boolean> {
    const order = this.orders.get(id);
    if (!order) return false;

    this.orders.set(id, { ...order, status });
    return true;
  }

  // Order item methods
  async getOrderItems(orderId: number): Promise<OrderItem[]> {
    return Array.from(this.orderItems.values())
      .filter(item => item.orderId === orderId);
  }

  async createOrderItem(itemData: InsertOrderItem): Promise<OrderItem> {
    const id = this.orderItemIdCounter++;
    const createdAt = new Date();
    const orderItem: OrderItem = { ...itemData, id, createdAt };
    this.orderItems.set(id, orderItem);
    return orderItem;
  }

  // Wishlist methods
  async getWishlistItems(userId: number): Promise<WishlistItem[]> {
    return Array.from(this.wishlistItems.values()).filter(item => item.userId === userId);
  }

  async getWishlistItemWithDetails(userId: number): Promise<(WishlistItem & { product: Product })[]> {
    const items = await this.getWishlistItems(userId);
    return items.map(item => {
      const product = this.products.get(item.productId);
      if (!product) {
        throw new Error(`Product with ID ${item.productId} not found`);
      }
      return { ...item, product };
    });
  }

  async addWishlistItem(itemData: InsertWishlistItem): Promise<WishlistItem> {
    // Check if the item already exists in the wishlist
    const existingItem = Array.from(this.wishlistItems.values()).find(
      item => item.userId === itemData.userId && item.productId === itemData.productId
    );

    if (existingItem) {
      // Item already exists in wishlist, just return it
      return existingItem;
    }

    const id = this.wishlistItemIdCounter++;
    const createdAt = new Date();
    const wishlistItem: WishlistItem = { ...itemData, id, createdAt };
    this.wishlistItems.set(id, wishlistItem);
    return wishlistItem;
  }

  async removeWishlistItem(id: number): Promise<boolean> {
    return this.wishlistItems.delete(id);
  }

  async isProductInWishlist(userId: number, productId: number): Promise<boolean> {
    return Array.from(this.wishlistItems.values()).some(
      item => item.userId === userId && item.productId === productId
    );
  }

  // Initialize with some sample data
  private initializeData() {
    // We'll skip initializing users here as we can register them
    // through the API for proper password hashing

    // Add categories
    const recycledElectronicsCategory = this.createCategory({
      name: "Recycled Electronics",
      imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    });
    const upcycledFashionCategory = this.createCategory({
      name: "Upcycled Fashion",
      imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    });
    const ecoHomeCategory = this.createCategory({
      name: "Eco-friendly Home",
      imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    });
    const recycledAccessoriesCategory = this.createCategory({
      name: "Recycled Accessories",
      imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    });

    // Add products - each with ONLY ONE tag (New, Popular, or Sale)

    // NEW ARRIVALS - Products tagged as 'New' only
    this.createProduct({
      name: "Refurbished Smartphone",
      description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
      price: 329.99,
      discountPrice: null,
      rating: 4.5,
      reviewCount: 34,
      imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 1, // Recycled Electronics
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: false
    });

    this.createProduct({
      name: "Recycled Plastic Watch",
      description: "Stylish watch made from ocean plastic. Water-resistant and eco-conscious design.",
      price: 79.99,
      discountPrice: null,
      rating: 4.1,
      reviewCount: 27,
      imageUrl: "https://images.pexels.com/photos/3908800/pexels-photo-3908800.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 4, // Recycled Accessories
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: false
    });

    this.createProduct({
      name: "Sustainable Bamboo Toothbrush",
      description: "Eco-friendly bamboo toothbrush with biodegradable handle. Reduces plastic waste.",
      price: 6.99,
      discountPrice: null,
      rating: 4.6,
      reviewCount: 42,
      imageUrl: "https://images.pexels.com/photos/3737593/pexels-photo-3737593.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: false
    });

    this.createProduct({
      name: "Cork Yoga Mat",
      description: "Natural cork yoga mat that's eco-friendly, non-slip and antimicrobial. Perfect for your practice.",
      price: 49.99,
      discountPrice: null,
      rating: 4.7,
      reviewCount: 18,
      imageUrl: "https://images.pexels.com/photos/4056535/pexels-photo-4056535.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: false
    });

    // POPULAR ITEMS - Products tagged as 'Popular' only
    this.createProduct({
      name: "Upcycled Denim Backpack",
      description: "Handcrafted backpack made from recycled denim jeans. Each piece is unique and eco-friendly.",
      price: 59.99,
      discountPrice: null,
      rating: 4.7,
      reviewCount: 56,
      imageUrl: "https://images.pexels.com/photos/4068314/pexels-photo-4068314.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 2, // Upcycled Fashion
      inStock: true,
      isNew: false,
      isPopular: true,
      isSale: false
    });

    this.createProduct({
      name: "Reconditioned Wireless Headphones",
      description: "Factory reconditioned headphones with noise cancellation. Looks and works like new at a fraction of the cost.",
      price: 129.99,
      discountPrice: null,
      rating: 4.3,
      reviewCount: 42,
      imageUrl: "https://images.pexels.com/photos/3394665/pexels-photo-3394665.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 1, // Recycled Electronics
      inStock: true,
      isNew: false,
      isPopular: true,
      isSale: false
    });

    this.createProduct({
      name: "Eco-Friendly Plant Pot",
      description: "Beautiful plant pot made from recycled materials. Perfect for indoor plants and sustainable home decor.",
      price: 19.99,
      discountPrice: null,
      rating: 4.6,
      reviewCount: 28,
      imageUrl: "https://images.pexels.com/photos/1084188/pexels-photo-1084188.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: false,
      isPopular: true,
      isSale: false
    });

    this.createProduct({
      name: "Upcycled T-Shirt Collection",
      description: "Fashion-forward t-shirts made from recycled cotton and plastic bottles. Eco-friendly and super soft.",
      price: 34.99,
      discountPrice: null,
      rating: 4.8,
      reviewCount: 38,
      imageUrl: "https://images.pexels.com/photos/4210862/pexels-photo-4210862.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 2, // Upcycled Fashion
      inStock: true,
      isNew: false,
      isPopular: true,
      isSale: false
    });

    // ON SALE ITEMS - Products tagged as 'Sale' only
    this.createProduct({
      name: "Recycled Paper Gift Wrap",
      description: "Eco-friendly gift wrapping paper made from 100% recycled materials. Comes in various patterns.",
      price: 5.99,
      discountPrice: 8.99,
      rating: 4.3,
      reviewCount: 16,
      imageUrl: "https://images.pexels.com/photos/6044227/pexels-photo-6044227.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: true
    });

    this.createProduct({
      name: "Bamboo Kitchen Utensils",
      description: "Sustainable bamboo utensil set - durable, lightweight and biodegradable.",
      price: 24.99,
      discountPrice: 32.99,
      rating: 4.2,
      reviewCount: 89,
      imageUrl: "https://images.pexels.com/photos/5907815/pexels-photo-5907815.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: true
    });

    this.createProduct({
      name: "Recycled Glass Vase",
      description: "Beautiful handblown vase made from 100% recycled glass. Each piece is unique and artisanal.",
      price: 29.99,
      discountPrice: 39.99,
      rating: 4.5,
      reviewCount: 31,
      imageUrl: "https://images.pexels.com/photos/1391679/pexels-photo-1391679.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: true
    });

    this.createProduct({
      name: "Refurbished Tablet",
      description: "Certified refurbished tablet with new battery. Comes with 6-month warranty and reduced carbon footprint.",
      price: 199.99,
      discountPrice: 249.99,
      rating: 4.0,
      reviewCount: 18,
      imageUrl: "https://images.pexels.com/photos/1334597/pexels-photo-1334597.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 1, // Recycled Electronics
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: true
    });

    // REGULAR PRODUCTS - No special tag
    this.createProduct({
      name: "Recycled Glass Lamp",
      description: "Artisanal lamp crafted from recycled glass bottles. Each piece is unique with a beautiful illumination pattern.",
      price: 89.99,
      discountPrice: null,
      rating: 4.4,
      reviewCount: 19,
      imageUrl: "https://images.pexels.com/photos/3679601/pexels-photo-3679601.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: false
    });

    this.createProduct({
      name: "Solar Power Bank",
      description: "Portable solar charger with dual USB ports. Charge your devices using clean energy.",
      price: 39.99,
      discountPrice: null,
      rating: 4.2,
      reviewCount: 15,
      imageUrl: "https://images.pexels.com/photos/5417676/pexels-photo-5417676.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 1, // Recycled Electronics
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: false
    });

    // More regular products with no special tag
    this.createProduct({
      name: "Bottle Cap Accessories",
      description: "Colorful, handmade jewelry from recycled bottle caps. Lightweight, eco-friendly statement pieces.",
      price: 24.99,
      discountPrice: null,
      rating: 4.6,
      reviewCount: 25,
      imageUrl: "https://images.pexels.com/photos/1020016/pexels-photo-1020016.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 4, // Recycled Accessories
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: false
    });

    // This product is ONLY new (not popular, not on sale)
    this.createProduct({
      name: "Repurposed Wood Furniture",
      description: "Unique coffee table made from repurposed shipping pallets. Sustainable home decor with character.",
      price: 149.99,
      discountPrice: null,
      rating: 4.7,
      reviewCount: 23,
      imageUrl: "https://images.pexels.com/photos/3773573/pexels-photo-3773573.png?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 3, // Eco-friendly Home
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: false
    });

    this.createProduct({
      name: "Recycled Metal Water Bottle",
      description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
      price: 29.99,
      discountPrice: null,
      rating: 4.9,
      reviewCount: 120,
      imageUrl: "https://images.pexels.com/photos/1342529/pexels-photo-1342529.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 4, // Recycled Accessories
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: false
    });

    // Add Upcycled Fashion products
    this.createProduct({
      name: "Upcycled Denim Jacket",
      description: "Vintage denim jacket reimagined with artistic patches and sustainable materials.",
      price: 89.99,
      discountPrice: null,
      rating: 4.7,
      reviewCount: 45,
      imageUrl: "https://images.pexels.com/photos/7691355/pexels-photo-7691355.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 2, // Upcycled Fashion
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: false
    });

    this.createProduct({
      name: "Upcycled T-Shirt Dress",
      description: "Stylish and comfortable t-shirt dress made from upcycled cotton.",
      price: 49.99,
      discountPrice: null,
      rating: 4.6,
      reviewCount: 32,
      imageUrl: "https://images.pexels.com/photos/10730512/pexels-photo-10730512.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      categoryId: 2, //Upcycled Fashion
      inStock: true,
      isNew: false,
      isPopular: true,
      isSale: false
    });
  }
}

// Use MemStorage for development purposes
// Import DatabaseStorage
import { DatabaseStorage, dbStorage } from "./database-storage";

// You can switch between MemStorage and DatabaseStorage here
// export const storage = new MemStorage();
export const storage = dbStorage;