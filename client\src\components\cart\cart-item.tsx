import { useMutation } from "@tanstack/react-query";
import { Minus, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { CartItem as CartItemType } from "@shared/schema";
import { Product } from "@shared/schema";
import { useAuth } from "@/hooks/use-auth";

type CartItemProps = {
  item: CartItemType & { product: Product };
};

export function CartItem({ item }: CartItemProps) {
  // Get user from auth context
  const { user } = useAuth();

  // Update cart item quantity
  const updateQuantityMutation = useMutation({
    mutationFn: async ({ id, quantity }: { id: number; quantity: number }) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to update your cart");
      }

      console.log(`Updating cart item ${id} quantity to ${quantity} for user ${user.id}`);

      // Use direct fetch with credentials to ensure cookies are sent
      const response = await fetch(`/api/cart/${id}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ quantity })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to update cart item: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully updated cart item ${id} to quantity ${quantity}`);
      return await response.json();
    },
    onSuccess: () => {
      console.log('Successfully updated cart item quantity');

      if (user) {
        // Force invalidate and refetch the cart queries
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }
    },
    onError: (error) => {
      console.error('Error updating cart item quantity:', error);
    }
  });

  // Remove item from cart
  const removeItemMutation = useMutation({
    mutationFn: async (id: number) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to remove items from your cart");
      }

      console.log(`Removing cart item ${id} for user ${user.id}`);

      // Use direct fetch with credentials to ensure cookies are sent
      const response = await fetch(`/api/cart/${id}?userId=${user.id}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to remove from cart: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully removed cart item ${id}, status: ${response.status}`);
      return true;
    },
    onSuccess: () => {
      console.log('Successfully removed item from cart');

      if (user) {
        // Force invalidate and refetch the cart queries
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }
    },
    onError: (error) => {
      console.error('Error removing cart item:', error);
    }
  });

  const handleDecrease = () => {
    if (item.quantity > 1) {
      updateQuantityMutation.mutate({ id: item.id, quantity: item.quantity - 1 });
    }
  };

  const handleIncrease = () => {
    updateQuantityMutation.mutate({ id: item.id, quantity: item.quantity + 1 });
  };

  const handleRemove = () => {
    removeItemMutation.mutate(item.id);
  };

  // Get product price (use discount price if available)
  const price = item.product.discountPrice || item.product.price;
  const totalPrice = price * item.quantity;

  return (
    <div className="flex items-center py-4 border-b border-gray-200">
      <div className="w-20 h-20 rounded-md overflow-hidden">
        <img
          src={item.product.imageUrl}
          alt={item.product.name}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="flex-1 ml-4">
        <h3 className="font-medium text-primary">{item.product.name}</h3>
        {item.product.discountPrice && (
          <p className="text-sm text-gray-500">
            <span className="line-through">${item.product.price.toFixed(2)}</span>
            {" "}
            <span className="text-secondary font-medium">${item.product.discountPrice.toFixed(2)}</span>
          </p>
        )}
        {!item.product.discountPrice && (
          <p className="text-sm text-gray-600">${item.product.price.toFixed(2)}</p>
        )}
        <div className="flex items-center mt-2">
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 rounded-full"
            onClick={handleDecrease}
            disabled={item.quantity <= 1 || updateQuantityMutation.isPending}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <span className="mx-2 text-sm">{item.quantity}</span>
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 rounded-full"
            onClick={handleIncrease}
            disabled={updateQuantityMutation.isPending}
          >
            <Plus className="h-3 w-3" />
          </Button>
          <span className="font-medium text-primary ml-auto">${totalPrice.toFixed(2)}</span>
        </div>
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="ml-2 text-gray-500 hover:text-red-500"
        onClick={handleRemove}
        disabled={removeItemMutation.isPending}
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}
