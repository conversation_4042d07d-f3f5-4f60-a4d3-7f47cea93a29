[build]
  command = "npm run build"
  publish = "dist"
  functions = "netlify/functions"

[dev]
  command = "npm run dev"
  port = 3000

# Process all API requests through the main API function
[[redirects]]
  from = "/api/products*"
  to = "/.netlify/functions/products"
  status = 200

[[redirects]]
  from = "/api/login"
  to = "/.netlify/functions/auth"
  status = 200
  force = true

[[redirects]]
  from = "/api/auth/*"
  to = "/.netlify/functions/auth"
  status = 200
  force = true

[[redirects]]
  from = "/api/cart*"
  to = "/.netlify/functions/cart"
  status = 200

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api/:splat"
  status = 200

# Serve static assets
[[redirects]]
  from = "/assets/*"
  to = "/assets/:splat"
  status = 200

# Single page application setup
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Function configuration
[functions]
  node_bundler = "esbuild"
  included_files = ["dist/**", "server/**", "shared/**"]
  external_node_modules = ["pg-native"]
  
# Build plugin
[[plugins]]
  package = "/netlify-build-plugin.js"

# Environment variables
[build.environment]
  NODE_ENV = "production"
  NETLIFY = "true"
  PGSSLMODE = "no-verify"
  POSTGRESQL_SSL_CERT_MODE = "no-verify"
  NODE_TLS_REJECT_UNAUTHORIZED = "0"

