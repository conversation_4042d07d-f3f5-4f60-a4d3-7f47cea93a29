import { useState, useEffect, useRef } from "react";
import { Link, useLocation } from "wouter";
import { MobileMenu } from "./mobile-menu";
import { CartModal } from "../cart/cart-modal";
import { ShoppingCart, Heart, User, ChevronDown, RecycleIcon, SearchIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import ScrollReveal from 'scrollreveal';
import { HomeNav } from "./home-nav";

export function Header() {
  const [location, setLocation] = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCartModalOpen, setIsCartModalOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [isCompact, setIsCompact] = useState(false);
  const [searchExpanded, setSearchExpanded] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [homeNavMerged, setHomeNavMerged] = useState(false);
  const dropdownRef = useRef(null);
  const headerRef = useRef(null);
  const searchInputRef = useRef(null);

  const { user, logout } = useAuth();

  const { data: cartItems } = useQuery({
    queryKey: ["/api/cart"],
    enabled: !!user,
  });

  const cartItemCount = cartItems?.length || 0;

  // Detect pathname to determine if we're on home page
  const isHomePage = location === "/";

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Initialize ScrollReveal for animations with reduced delay
  useEffect(() => {
    const sr = ScrollReveal({
      origin: 'top',
      distance: '20px',
      duration: 800,
      reset: false,
      delay: 100
    });

    // Reveal header elements immediately with no interval
    sr.reveal('.header-animate', {
      interval: 0,
      delay: 0
    });

    return () => sr.destroy();
  }, []);

  // Enhanced scroll event listener for sticky header with transformations
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (!ticking) {
        window.requestAnimationFrame(() => {
          // Initial sticky state (at 50px)
          const isScrolled = currentScrollY > 50;
          setIsSticky(isScrolled);

          // Compact mode and nav merge threshold at 150px
          const threshold = 150;
          const shouldBeCompact = currentScrollY > threshold;
          const shouldMergeNav = currentScrollY > threshold;

          // Only update states if they're different
          if (isCompact !== shouldBeCompact) {
            setIsCompact(shouldBeCompact);
          }

          // Always keep search expanded
          setSearchExpanded(true);

          // Add debounce for nav merge to prevent flickering
          if (homeNavMerged !== shouldMergeNav) {
            setHomeNavMerged(shouldMergeNav);
          }

          lastScrollY = currentScrollY;
          ticking = false;
        });

        ticking = true;
      }
    };

    // Throttled scroll handler
    let scrollTimeout: NodeJS.Timeout;
    const throttledScrollHandler = () => {
      if (!scrollTimeout) {
        scrollTimeout = setTimeout(() => {
          handleScroll();
          scrollTimeout = null;
        }, 16); // Approximately 60fps
      }
    };

    // Initial state
    handleScroll();

    window.addEventListener('scroll', throttledScrollHandler, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScrollHandler);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [isCompact, homeNavMerged]);

  // Get header height and set it as a CSS variable for proper spacing
  useEffect(() => {
    if (headerRef.current) {
      const height = headerRef.current.offsetHeight;
      document.documentElement.style.setProperty('--header-height', `${height}px`);
    }
  }, [isSticky]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Redirect to products page with search query
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleNavigate = (path) => {
    setDropdownOpen(false);
    setLocation(path);
  };

  const handleLogout = () => {
    setDropdownOpen(false);
    logout();
  };

  const IconButton = ({ icon: Icon, label, count, onClick, href }) => (
    <div className="relative">
      {href ? (
        <Link
          href={href}
          className="flex items-center gap-1 p-1 transition-colors hover:text-[#6a9816]"
          aria-label={label}
        >
          <Icon className="w-5 h-5" />
          {count > 0 && (
            <span className="absolute -top-1 -right-1 rounded-full w-4 h-4 flex items-center justify-center text-xs text-white bg-[#8bbe1b]">
              {count}
            </span>
          )}
        </Link>
      ) : (
        <button
          onClick={onClick}
          className="flex items-center gap-1 p-1 transition-colors hover:text-[#6a9816]"
          aria-label={label}
        >
          <Icon className="w-5 h-5" />
          {count > 0 && (
            <span className="absolute -top-1 -right-1 rounded-full w-4 h-4 flex items-center justify-center text-xs text-white bg-[#8bbe1b]">
              {count}
            </span>
          )}
        </button>
      )}
    </div>
  );

  // Function to expand search when clicking on the icon in compact mode
  const toggleSearch = () => {
    if (!searchExpanded) {
      setSearchExpanded(true);
      // Focus the input after a brief delay to allow for animation
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 300);
    }
  };

  return (
    <header
      ref={headerRef}
      className={`bg-white transition-all duration-500 border-b border-black ${
        isSticky ? 'sticky top-0 z-50 shadow-lg animate-fadeInDown' : ''
      } ${isCompact ? 'py-2' : 'py-0'}`}
      id="main-header"
    >
      {/* White background with animated shadow on scroll */}
      <div className={`container px-4 ${isCompact ? 'py-1' : 'py-3'} relative z-10 transition-all duration-500`}>
        <div className="flex items-center justify-between">
          {/* Logo with dynamic size based on scroll state */}
          <Link href="/" className="flex items-center gap-2 transition-all duration-500">
            <div className={`bg-[#8bbe1b] rounded-full ${isCompact ? 'w-8 h-8' : 'w-10 h-10'} flex items-center justify-center shadow-lg transition-all duration-500`}>
              <RecycleIcon className={`text-white ${isCompact ? 'w-5 h-5' : 'w-6 h-6'} transition-all duration-500`} />
            </div>
            <div className={`hidden sm:block transition-all duration-500 ${isCompact && !isSticky ? 'opacity-0 scale-0' : 'opacity-100 scale-100'}`}>
              <div className="flex items-center">
                <span className={`font-bold font-poppins text-[#8bbe1b] transition-all duration-500 ${isCompact ? 'text-2xl' : 'text-3xl'}`}>W</span>
                <span className={`font-bold font-poppins text-black mx-0.5 transition-all duration-500 ${isCompact ? 'text-2xl' : 'text-3xl'}`}>2</span>
                <span className={`font-bold font-poppins text-[#8bbe1b] transition-all duration-500 ${isCompact ? 'text-2xl' : 'text-3xl'}`}>W</span>
              </div>
              <p className={`text-xs font-semibold text-black -mt-1 tracking-wide text-center transition-all duration-500 ${isCompact ? 'opacity-0 h-0' : 'opacity-100'}`}>
                WASTE TO WEALTH
              </p>
            </div>
          </Link>

          {/* Transforming Search Bar - Now Middle */}
          {searchExpanded ? (
            <form
              onSubmit={handleSearch}
              className={`hidden md:flex flex-1 max-w-md mx-4 relative group transition-all duration-500 transform-gpu ${
                isCompact ? 'scale-90 max-w-xs' : ''
              }`}
            >
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={isCompact ? "Search..." : "Search for sustainable products..."}
                className="w-full bg-white border border-[#8bbe1b] rounded-full py-2 pl-4 pr-10 focus:ring-2 focus:ring-[#8bbe1b] focus:outline-none transition-all duration-300 shadow-md group-hover:shadow-lg"
              />
              <button
                type="submit"
                className="absolute right-1 top-1/2 -translate-y-1/2 bg-[#8bbe1b] text-white p-1.5 rounded-full hover:bg-[#6a9816] transition-all duration-300"
                aria-label="Search products"
              >
                <SearchIcon className="w-4 h-4" />
              </button>
            </form>
          ) : (
            <button
              onClick={toggleSearch}
              className="hidden md:flex items-center justify-center p-2 bg-[#8bbe1b] text-white rounded-full hover:bg-[#6a9816] transition-all duration-300"
              aria-label="Open search"
            >
              <SearchIcon className="w-5 h-5" />
            </button>
          )}

          {/* ALL Navigation Items - RIGHT aligned */}
          <div className="hidden md:flex items-center gap-3 ml-auto">
            {/* Navigation Links */}
            <div className="flex items-center">
              <Link href="/" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1">Home</Link>
              <Link href="/products" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1">Shop</Link>
              <Link href="/sustainability" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1">Eco-Tips</Link>

              {/* Show home navigation links only when scrolled on homepage */}
              {isHomePage && homeNavMerged && (
                <>
                  <Link href="/new-arrivals" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1 animate-fadeInDown">Recently Landed</Link>
                  <Link href="/on-sale" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1 animate-fadeInDown">On Sale</Link>
                  <Link href="/popular-items" className="text-sm font-medium hover:text-[#6a9816] px-2 py-1 animate-fadeInDown">Popular</Link>
                </>
              )}
            </div>

            {/* Separator between navigation and icons */}
            <div className="h-6 border-r border-gray-300 mx-1"></div>

            {/* User status indicator - Removed to store in database only */}

            {/* Account, Wishlist and Cart Icons - Now after navigation links with more right alignment */}
            <div className="flex items-center gap-1 ml-auto">
              {user ? (
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="flex items-center gap-1 p-2 transition-colors text-gray-800 hover:text-[#8bbe1b]"
                    aria-label="User menu"
                  >
                    <User className="w-5 h-5" />
                    <ChevronDown className="w-4 h-4" />
                  </button>
                  {dropdownOpen && (
                    <div
                      className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md border z-50"
                    >
                      <Link
                        href="/profile"
                        className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 text-gray-800 hover:text-[#8bbe1b]"
                        onClick={() => setDropdownOpen(false)}
                      >
                        Profile
                      </Link>
                      <Link
                        href="/profile?tab=orders"
                        className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 text-gray-800 hover:text-[#8bbe1b]"
                        onClick={() => {
                          setDropdownOpen(false);
                        }}
                      >
                        Orders
                      </Link>
                      <Link
                        href="/user-status"
                        className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 text-gray-800 hover:text-[#8bbe1b]"
                        onClick={() => setDropdownOpen(false)}
                      >
                        Login Status
                      </Link>
                      <button
                        onClick={() => {
                          setDropdownOpen(false);
                          logout({
                            onSuccess: () => {
                              // Force redirect to auth page after logout
                              window.location.href = '/auth';
                            }
                          });
                        }}
                        className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 text-gray-800 hover:text-[#8bbe1b]"
                      >
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  href="/auth"
                  className="flex items-center gap-1 p-2 transition-colors text-gray-800 hover:text-[#8bbe1b]"
                  aria-label="Login"
                >
                  <User className="w-5 h-5" />
                </Link>
              )}
              <Link
                href={user ? "/wishlist" : "/auth"}
                className="flex items-center gap-1 p-2 transition-colors text-gray-800 hover:text-[#8bbe1b]"
                aria-label="Wishlist"
              >
                <Heart className="w-5 h-5" />
              </Link>
              <button
                onClick={() => (user ? setIsCartModalOpen(true) : setLocation("/auth"))}
                className="flex items-center gap-1 p-2 transition-colors text-gray-800 hover:text-[#8bbe1b] relative"
                aria-label="Cart"
              >
                <ShoppingCart className="w-5 h-5" />
                {cartItemCount > 0 && (
                  <span className="absolute -top-1 -right-1 rounded-full w-5 h-5 flex items-center justify-center text-xs text-white bg-[#8bbe1b] font-bold">
                    {cartItemCount}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden p-2 text-gray-800 hover:bg-gray-100"
            onClick={() => setIsMobileMenuOpen(true)}
            aria-label="Open menu"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 24 24"
              stroke="currentColor"
              fill="none"
              strokeWidth="2"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </Button>
        </div>
      </div>

      {/* Mobile search - updated to redirect to products page */}
      <div className="md:hidden px-4 pb-3 relative z-10">
        <form onSubmit={handleSearch} className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search products..."
            className="w-full bg-white border border-[#8bbe1b] rounded-full py-2 pl-4 pr-10 focus:ring-2 focus:ring-[#8bbe1b] focus:outline-none shadow-md"
          />
          <button
            type="submit"
            className="absolute right-1 top-1/2 -translate-y-1/2 bg-[#8bbe1b] text-white p-1.5 rounded-full hover:bg-[#6a9816] transition-colors"
          >
            <SearchIcon className="w-4 h-4" />
          </button>
        </form>
      </div>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        cartItemCount={cartItemCount}
        user={user}
      />

      <CartModal
        isOpen={isCartModalOpen}
        onClose={() => setIsCartModalOpen(false)}
      />

      {/* Add CSS animation for the fade-in effect */}
      <style>{`
        @keyframes fadeInDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fadeInDown {
          animation: fadeInDown 0.3s ease-in-out;
        }
      `}</style>
    </header>
  );
}
