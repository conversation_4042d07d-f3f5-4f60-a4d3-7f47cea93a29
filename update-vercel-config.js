// This script updates the vercel.json file to use the correct command
// It's a simple solution to fix the tsx loader issue

import fs from 'fs';

const VERCEL_CONFIG_PATH = 'vercel.json';

try {
  // Read the current vercel.json file
  const vercelConfig = JSON.parse(fs.readFileSync(VERCEL_CONFIG_PATH, 'utf8'));
  
  // Update the devCommand to use the correct flag
  vercelConfig.devCommand = "node --import tsx server/index.ts";
  
  // Write the updated config back to the file
  fs.writeFileSync(VERCEL_CONFIG_PATH, JSON.stringify(vercelConfig, null, 2));
  
  console.log('Successfully updated vercel.json with the correct devCommand');
  console.log('New devCommand:', vercelConfig.devCommand);
} catch (error) {
  console.error('Error updating vercel.json:', error);
}
