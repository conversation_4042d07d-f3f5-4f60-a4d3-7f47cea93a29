// Direct database connection handler for Vercel
import { drizzle } from "drizzle-orm/postgres-js";
import { sql } from "drizzle-orm";
import postgres from "postgres";
import * as schema from "./schema.js";

// Get connection string from environment variables
// Try multiple environment variables to ensure we find a valid connection string
let connectionString = process.env.POSTGRES_URL ||
                      process.env.DATABASE_URL ||
                      process.env.POSTGRES_URL_NON_POOLING ||
                      process.env.DATABASE_URL_UNPOOLED;

// Fallback to hardcoded connection string if none is found in environment variables
if (!connectionString) {
  console.warn('No database connection string found in environment variables. Using fallback.');
  connectionString = 'postgres://neondb_owner:<EMAIL>/neondb?sslmode=require';
}

// Log database connection attempt for debugging
console.log(`Attempting to connect to database in Vercel environment`);
console.log(`Database URL configured: ${connectionString ? '✓ Present' : '✗ Missing'}`);
console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`SSL Mode: ${process.env.PGSSLMODE || 'default'}`);
console.log(`SSL Reject Unauthorized: ${process.env.NODE_TLS_REJECT_UNAUTHORIZED || 'default'}`);

// Serverless-friendly connection pooling config
let globalClient;

const getClient = () => {
  // Use existing connection if available (handles Vercel serverless function reuse)
  if (globalClient) return globalClient;

  // Configure connection for serverless environment
  const clientConfig = {
    // For serverless, use lower connection counts
    max: 1,
    idle_timeout: 10,
    connect_timeout: 10,
    prepare: false,
    // Always use rejectUnauthorized: false for Vercel environment
    ssl: { rejectUnauthorized: false },
    // Add connection error handler
    onnotice: (notice) => {
      console.log('Database notice:', notice);
    },
    debug: true, // Enable debug logging
    connection: {
      // Set application_name to help identify connections in database logs
      application_name: 'vercel-serverless'
    }
  };

  // For Vercel environment, explicitly set SSL options
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  process.env.PGSSLMODE = 'no-verify';
  process.env.PG_SSL_REJECT_UNAUTHORIZED = '0';

  try {
    console.log('Connecting to PostgreSQL database from Vercel...');
    globalClient = postgres(connectionString, clientConfig);
    console.log('Successfully connected to PostgreSQL database from Vercel');
    return globalClient;
  } catch (error) {
    console.error('Failed to connect to PostgreSQL database from Vercel:', error);
    throw error;
  }
};

// Singleton database client - creates connection only when needed
export const db = drizzle(getClient(), { schema });

// Check database connection
export async function pingDatabase() {
  try {
    // First try a simple query that doesn't depend on schema
    const result = await db.execute(sql`SELECT 1 as test`);
    console.log('Database ping successful from Vercel (raw query)');

    // Then try a query that uses the schema
    try {
      const users = await db.select().from(schema.users).limit(1);
      console.log('Database ping successful from Vercel (users table)');
      return true;
    } catch (schemaError) {
      console.error('Database schema query failed:', schemaError);
      // If the schema query fails but the raw query succeeded, the database is connected
      // but there might be an issue with the schema or tables
      return true;
    }
  } catch (error) {
    console.error('Database ping failed from Vercel:', error);
    throw error;
  }
}
