// Script to update the image URL for the Upcycled Denim Backpack product
import postgres from 'postgres';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env.vercel
const envPath = path.join(__dirname, '.env.vercel');
if (fs.existsSync(envPath)) {
  console.log('Loading environment variables from .env.vercel');
  dotenv.config({ path: envPath });
} else {
  console.log('No .env.vercel file found, using default environment variables');
  dotenv.config();
}

// Get connection string from environment variables
const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;

if (!connectionString) {
  console.error("Neither POSTGRES_URL nor DATABASE_URL is defined in environment variables");
  process.exit(1);
}

// Create a postgres client
const sql = postgres(connectionString, {
  ssl: { rejectUnauthorized: false },
  max: 3,
  idle_timeout: 20,
  connect_timeout: 10,
  prepare: false
});

async function updateImage() {
  try {
    console.log('Updating image URL for Upcycled Denim Backpack...');

    // Update the product image URL
    const result = await sql`
      UPDATE products
      SET image_url = ${'https://images.pexels.com/photos/1314058/pexels-photo-1314058.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'}
      WHERE name = ${'Upcycled Denim Backpack'}
      RETURNING *
    `;

    if (result.length > 0) {
      console.log('Successfully updated product:', result[0]);
    } else {
      console.log('No product found with name "Upcycled Denim Backpack"');
    }
  } catch (error) {
    console.error('Error updating product:', error);
  } finally {
    await sql.end();
  }
}

// Run the update function
updateImage();
