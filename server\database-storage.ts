
import {
  User, InsertUser, Product, InsertProduct,
  Category, InsertCategory, CartItem, InsertCartItem,
  Order, InsertOrder, OrderItem, InsertOrderItem,
  WishlistItem, InsertWishlistItem,
  users, products, categories, cartItems, orders, orderItems, wishlistItems
} from "./schema.js";
import { IStorage } from "./storage.js";
import { db } from "./db.js";
import { eq, and, desc, gte, lte, like } from "drizzle-orm";
import session from "express-session";
import connectPg from "connect-pg-simple";
import pkg from 'pg';
const { Pool } = pkg;

// Create PostgreSQL session store
const PostgresSessionStore = connectPg(session);
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL,
});

// Database storage implementation
export class DatabaseStorage implements IStorage {
  // Session store
  sessionStore: any;

  constructor() {
    this.sessionStore = new PostgresSessionStore({
      pool,
      createTableIfMissing: true,
    });
  }

  async initialize() {
    console.log("Initializing database storage...");

    try {
      // Add sample data if the tables are empty
      const categoriesCount = await db.select().from(categories);
      if (categoriesCount.length === 0) {
        console.log("No categories found. Adding sample categories and products...");

        // Add categories - using only the specified categories
        await db.insert(categories).values([
          {
            name: "Recycled Electronics",
            imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          },
          {
            name: "Upcycled Fashion",
            imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          },
          {
            name: "Eco-friendly Home",
            imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          },
          {
            name: "Recycled Accessories",
            imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          }
        ]);

        // Add sample products - using only the specified products
        await db.insert(products).values([
          {
            name: "Refurbished Smartphone",
            description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
            price: 329.99,
            discountPrice: 299.99,
            rating: 4.5,
            reviewCount: 34,
            imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1&fit=crop",
            categoryId: 1, // Recycled Electronics
            inStock: true,
            isNew: true,
            isPopular: true,
            isSale: true
          },
          {
            name: "Recycled Plastic Watch",
            description: "Stylish watch made from ocean plastic. Water-resistant and eco-conscious design.",
            price: 79.99,
            discountPrice: 69.99,
            rating: 4.1,
            reviewCount: 27,
            imageUrl: "https://images.pexels.com/photos/3908800/pexels-photo-3908800.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1&fit=crop",
            categoryId: 4, // Recycled Accessories
            inStock: true,
            isNew: true,
            isPopular: true,
            isSale: true
          },
          {
            name: "Sustainable Bamboo Toothbrush",
            description: "Eco-friendly bamboo toothbrush with biodegradable handle. Reduces plastic waste.",
            price: 6.99,
            discountPrice: 4.99,
            rating: 4.6,
            reviewCount: 42,
            imageUrl: "https://images.pexels.com/photos/3737593/pexels-photo-3737593.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1&fit=crop",
            categoryId: 3, // Eco-friendly Home
            inStock: true,
            isNew: true,
            isPopular: true,
            isSale: true
          },
          {
            name: "Upcycled Denim Backpack",
            description: "Handcrafted backpack made from recycled denim jeans. Each piece is unique.",
            price: 59.99,
            discountPrice: 49.99,
            rating: 4.8,
            reviewCount: 56,
            imageUrl: "https://images.pexels.com/photos/1314058/pexels-photo-1314058.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
            categoryId: 2, // Upcycled Fashion
            inStock: true,
            isNew: true,
            isPopular: true,
            isSale: true
          },
          {
            name: "Vintage Console",
            description: "Refurbished gaming console from the 90s. Includes classic games and accessories.",
            price: 149.99,
            discountPrice: 129.99,
            rating: 4.7,
            reviewCount: 32,
            imageUrl: "https://images.pexels.com/photos/3945657/pexels-photo-3945657.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1&fit=crop",
            categoryId: 1, // Recycled Electronics
            inStock: true,
            isNew: true,
            isPopular: true,
            isSale: true
          }
        ]);

        console.log("Sample categories and products added successfully");
      } else {
        console.log(`Found ${categoriesCount.length} existing categories, skipping initialization`);
      }

      console.log("Database initialization complete");
    } catch (error) {
      console.error("Error initializing database:", error);
      throw error;
    }
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async createUser(userData: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(userData).returning();
    return user;
  }

  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const [updatedUser] = await db
      .update(users)
      .set(data)
      .where(eq(users.id, id))
      .returning();
    return updatedUser;
  }

  // Product methods
  async getProduct(id: number): Promise<Product | undefined> {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product;
  }

  // Method alias for compatibility with existing code
  async getProductById(id: number): Promise<Product | undefined> {
    return this.getProduct(id);
  }

  // Special product methods
  async getFeaturedProducts(limit: number = 8): Promise<Product[]> {
    return await db.select()
      .from(products)
      .where(eq(products.isPopular, true))
      .limit(limit);
  }

  async getNewArrivals(limit: number = 8): Promise<Product[]> {
    return await db.select()
      .from(products)
      .where(eq(products.isNew, true))
      .limit(limit);
  }

  async getPopularProducts(limit: number = 8): Promise<Product[]> {
    return await db.select()
      .from(products)
      .where(eq(products.isPopular, true))
      .limit(limit);
  }

  async getOnSaleProducts(limit: number = 8): Promise<Product[]> {
    return await db.select()
      .from(products)
      .where(eq(products.isSale, true))
      .limit(limit);
  }

  async getProducts(options: {
    limit?: number;
    offset?: number;
    categoryId?: number;
    search?: string;
    isNew?: boolean;
    isPopular?: boolean;
    isSale?: boolean;
    minPrice?: number;
    maxPrice?: number;
  } = {}): Promise<Product[]> {
    let query = db.select().from(products);

    // Apply filters
    const whereConditions = [];

    if (options.categoryId !== undefined) {
      whereConditions.push(eq(products.categoryId, options.categoryId));
    }

    if (options.isNew === true) {
      whereConditions.push(eq(products.isNew, true));
    }

    if (options.isPopular === true) {
      whereConditions.push(eq(products.isPopular, true));
    }

    if (options.isSale === true) {
      whereConditions.push(eq(products.isSale, true));
    }

    if (options.minPrice !== undefined) {
      whereConditions.push(gte(products.price, options.minPrice));
    }

    if (options.maxPrice !== undefined) {
      whereConditions.push(lte(products.price, options.maxPrice));
    }

    if (options.search) {
      const searchPattern = `%${options.search}%`;
      whereConditions.push(like(products.name, searchPattern));
    }

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Order by newest first
    query = query.orderBy(desc(products.createdAt));

    // Apply pagination
    if (options.offset !== undefined) {
      query = query.offset(options.offset);
    }

    if (options.limit !== undefined) {
      query = query.limit(options.limit);
    }

    return await query;
  }

  async createProduct(productData: InsertProduct): Promise<Product> {
    const [product] = await db.insert(products).values(productData).returning();
    return product;
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    const [category] = await db.select().from(categories).where(eq(categories.id, id));
    return category;
  }

  async getCategoryById(id: number): Promise<Category | undefined> {
    return this.getCategory(id);
  }

  async getCategories(): Promise<Category[]> {
    return await db.select().from(categories);
  }

  async createCategory(categoryData: InsertCategory): Promise<Category> {
    const [category] = await db.insert(categories).values(categoryData).returning();
    return category;
  }

  // Cart methods
  async getCartItems(userId: number): Promise<CartItem[]> {
    return await db.select().from(cartItems).where(eq(cartItems.userId, userId));
  }

  async getCartItemWithDetails(userId: number): Promise<(CartItem & { product: Product })[]> {
    const items = await this.getCartItems(userId);
    const result = [];

    for (const item of items) {
      const product = await this.getProduct(item.productId);
      if (product) {
        result.push({ ...item, product });
      }
    }

    return result;
  }

  async addCartItem(itemData: InsertCartItem): Promise<CartItem> {
    // Check if the item already exists
    const existingItems = await db
      .select()
      .from(cartItems)
      .where(
        and(
          eq(cartItems.userId, itemData.userId),
          eq(cartItems.productId, itemData.productId)
        )
      );

    if (existingItems.length > 0) {
      // Update quantity instead
      const existingItem = existingItems[0];
      return await this.updateCartItem(
        existingItem.id,
        existingItem.quantity + (itemData.quantity || 1)
      ) as CartItem;
    }

    // Create new item
    const [cartItem] = await db.insert(cartItems).values(itemData).returning();
    return cartItem;
  }

  async updateCartItem(id: number, quantity: number): Promise<CartItem | undefined> {
    const [updatedItem] = await db
      .update(cartItems)
      .set({ quantity })
      .where(eq(cartItems.id, id))
      .returning();
    return updatedItem;
  }

  async removeCartItem(id: number): Promise<boolean> {
    try {
      console.log(`Attempting to remove cart item with ID: ${id}`);
      const result = await db.delete(cartItems).where(eq(cartItems.id, id)).returning();
      console.log(`Cart item removal result:`, result);
      return result.length > 0;
    } catch (error) {
      console.error(`Error removing cart item with ID ${id}:`, error);
      throw error;
    }
  }

  async clearCart(userId: number): Promise<boolean> {
    try {
      console.log(`Attempting to clear cart for user ID: ${userId}`);
      const result = await db.delete(cartItems).where(eq(cartItems.userId, userId)).returning();
      console.log(`Cart cleared for user ${userId}, removed ${result.length} items`);
      return true;
    } catch (error) {
      console.error(`Error clearing cart for user ID ${userId}:`, error);
      throw error;
    }
  }

  // Order methods
  async getOrder(id: number): Promise<Order | undefined> {
    const [order] = await db.select().from(orders).where(eq(orders.id, id));
    return order;
  }

  async getUserOrders(userId: number): Promise<Order[]> {
    return await db
      .select()
      .from(orders)
      .where(eq(orders.userId, userId))
      .orderBy(desc(orders.createdAt));
  }

  async createOrder(orderData: InsertOrder): Promise<Order> {
    const [order] = await db.insert(orders).values(orderData).returning();
    return order;
  }

  async updateOrderStatus(id: number, status: string): Promise<boolean> {
    const result = await db
      .update(orders)
      .set({ status })
      .where(eq(orders.id, id))
      .returning();
    return result.length > 0;
  }

  // Order item methods
  async getOrderItems(orderId: number): Promise<OrderItem[]> {
    return await db
      .select()
      .from(orderItems)
      .where(eq(orderItems.orderId, orderId));
  }

  async createOrderItem(itemData: InsertOrderItem): Promise<OrderItem> {
    const [orderItem] = await db.insert(orderItems).values(itemData).returning();
    return orderItem;
  }

  // Wishlist methods
  async getWishlistItems(userId: number): Promise<WishlistItem[]> {
    return await db
      .select()
      .from(wishlistItems)
      .where(eq(wishlistItems.userId, userId));
  }

  async getWishlistItemWithDetails(userId: number): Promise<(WishlistItem & { product: Product })[]> {
    const items = await this.getWishlistItems(userId);
    const result = [];

    for (const item of items) {
      const product = await this.getProduct(item.productId);
      if (product) {
        result.push({ ...item, product });
      }
    }

    return result;
  }

  async addWishlistItem(itemData: InsertWishlistItem): Promise<WishlistItem> {
    // Check if the item already exists
    const existingItems = await db
      .select()
      .from(wishlistItems)
      .where(
        and(
          eq(wishlistItems.userId, itemData.userId),
          eq(wishlistItems.productId, itemData.productId)
        )
      );

    if (existingItems.length > 0) {
      // Item already exists, just return it
      return existingItems[0];
    }

    // Create new item
    const [wishlistItem] = await db.insert(wishlistItems).values(itemData).returning();
    return wishlistItem;
  }

  async removeWishlistItem(id: number): Promise<boolean> {
    try {
      console.log(`Attempting to remove wishlist item with ID: ${id}`);
      const result = await db.delete(wishlistItems).where(eq(wishlistItems.id, id)).returning();
      console.log(`Wishlist item removal result:`, result);
      return result.length > 0;
    } catch (error) {
      console.error(`Error removing wishlist item with ID ${id}:`, error);
      throw error;
    }
  }

  async isProductInWishlist(userId: number, productId: number): Promise<boolean> {
    const items = await db
      .select()
      .from(wishlistItems)
      .where(
        and(
          eq(wishlistItems.userId, userId),
          eq(wishlistItems.productId, productId)
        )
      );
    return items.length > 0;
  }
}

// Export instance
export const dbStorage = new DatabaseStorage();