import { ChevronRight, Footprints, Info, Leaf, Recycle, SquareCode } from "lucide-react";
import { ContentOnlyLayout } from "@/components/layout/content-only-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON> } from "wouter";

interface RecyclingMaterial {
  id: number;
  name: string;
  icon: JSX.Element;
  recyclable: boolean;
  preparation: string;
  notes: string;
}

const recyclingMaterials: RecyclingMaterial[] = [
  {
    id: 1,
    name: "Paper & Cardboard",
    icon: <SquareCode className="w-10 h-10 text-[#8bbe1b]" />,
    recyclable: true,
    preparation: "Remove tape, staples, and plastic. Flatten boxes to save space.",
    notes: "Clean and dry items only. Avoid greasy or food-stained paper.",
  },
  {
    id: 2,
    name: "Plastic Bottles & Containers",
    icon: <Recycle className="w-10 h-10 text-[#8bbe1b]" />,
    recyclable: true,
    preparation: "Rinse containers, remove caps, and flatten to save space.",
    notes: "Check the recycling code (1-7) on the bottom. Not all plastics are accepted everywhere.",
  },
  {
    id: 3,
    name: "Glass Bottles & Jars",
    icon: <Info className="w-10 h-10 text-[#8bbe1b]" />,
    recyclable: true,
    preparation: "Rinse thoroughly. Remove caps and lids (they are often made of different materials).",
    notes: "Broken glass should not be placed in recycling bins. Window glass and drinking glasses are not recyclable.",
  },
  {
    id: 4,
    name: "Metal Cans & Aluminum",
    icon: <Recycle className="w-10 h-10 text-[#8bbe1b]" />,
    recyclable: true,
    preparation: "Rinse containers. Labels can remain on. Crush cans to save space.",
    notes: "Aluminum foil can be recycled if clean and balled up to be at least 2 inches in diameter.",
  },
  {
    id: 5,
    name: "Electronics",
    icon: <Recycle className="w-10 h-10 text-[#8bbe1b]" />,
    recyclable: true,
    preparation: "Back up and erase personal data. Remove batteries if possible.",
    notes: "Many retailers offer electronics recycling programs. Never place in regular recycling bins.",
  },
  {
    id: 6,
    name: "Textiles & Clothing",
    icon: <Footprints className="w-10 h-10 text-[#8bbe1b]" />,
    recyclable: true,
    preparation: "Clean and dry items only. Separate usable and unusable items.",
    notes: "Consider donation for gently used items. Even worn or torn items can be recycled into industrial rags or insulation.",
  },
];

const RecyclingGuidePage = () => {
  return (
    <ContentOnlyLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-[#8bbe1b] transition-colors">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <span>Recycling Guide</span>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-12 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
              Your Guide to Responsible Recycling
            </h1>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Proper recycling is essential for reducing waste and conserving resources. This guide will help you understand how to prepare different materials for recycling, which items are accepted, and how to maximize your environmental impact.
            </p>
          </div>

          <div className="bg-amber-50 border border-amber-100 rounded-lg p-6 mb-12">
            <div className="flex items-center gap-4 mb-4">
              <div className="bg-[#8bbe1b]/20 p-3 rounded-full">
                <Leaf className="w-6 h-6 text-[#8bbe1b]" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">Why Recycle?</h2>
            </div>
            <ul className="list-disc list-inside space-y-2 text-gray-600 pl-4">
              <li>Conserves natural resources like timber, water, and minerals</li>
              <li>Reduces the amount of waste sent to landfills</li>
              <li>Prevents pollution by reducing the need to collect new raw materials</li>
              <li>Saves energy in manufacturing processes</li>
              <li>Reduces greenhouse gas emissions that contribute to climate change</li>
              <li>Creates jobs in the recycling and manufacturing industries</li>
            </ul>
          </div>

          <h2 className="text-2xl font-bold mb-6 text-gray-800">Recycling Guide by Material</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
            {recyclingMaterials.map((material) => (
              <Card key={material.id} className="border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {material.icon}
                      <CardTitle className="text-xl">{material.name}</CardTitle>
                    </div>
                    <span className={`text-sm font-semibold px-3 py-1 rounded-full ${
                      material.recyclable 
                        ? "bg-green-100 text-green-700" 
                        : "bg-red-100 text-red-700"
                    }`}>
                      {material.recyclable ? "Recyclable" : "Not Recyclable"}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-sm font-semibold text-gray-700">How to Prepare:</h4>
                      <p className="text-gray-600">{material.preparation}</p>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-semibold text-gray-700">Important Notes:</h4>
                      <p className="text-gray-600">{material.notes}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="bg-[#8bbe1b]/10 rounded-lg p-6 mb-8">
            <h3 className="text-xl font-bold mb-4 text-gray-800">Recycling Tips</h3>
            <ul className="space-y-4">
              <li className="flex gap-3">
                <div className="bg-[#8bbe1b] rounded-full p-1 h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-sm font-bold">1</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Clean Before Recycling</h4>
                  <p className="text-gray-600">Rinse food containers to remove residue. Contaminated items can ruin an entire batch of recyclables.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="bg-[#8bbe1b] rounded-full p-1 h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-sm font-bold">2</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Know Your Local Guidelines</h4>
                  <p className="text-gray-600">Recycling rules vary by location. Check with your local recycling program to know exactly what's accepted.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="bg-[#8bbe1b] rounded-full p-1 h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-sm font-bold">3</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Don't Bag Recyclables</h4>
                  <p className="text-gray-600">Most recycling facilities require loose items. Plastic bags can jam sorting machinery.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="bg-[#8bbe1b] rounded-full p-1 h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-sm font-bold">4</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">When in Doubt, Find Out</h4>
                  <p className="text-gray-600">If you're unsure about an item, look it up instead of wishful recycling. Including non-recyclables can contaminate the whole batch.</p>
                </div>
              </li>
            </ul>
          </div>

          <div className="text-center">
            <h3 className="text-xl font-bold mb-4 text-gray-800">Explore Related Sustainability Resources</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/dropoff-locations" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Drop-off Locations</h4>
                <p className="text-sm text-gray-600">Find nearby recycling centers</p>
              </Link>
              <Link href="/trade-in" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Trade-in Program</h4>
                <p className="text-sm text-gray-600">Exchange your old items</p>
              </Link>
              <Link href="/repair-services" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Repair Services</h4>
                <p className="text-sm text-gray-600">Fix instead of replace</p>
              </Link>
              <Link href="/sustainability" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Sustainability Tips</h4>
                <p className="text-sm text-gray-600">Live more eco-friendly</p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ContentOnlyLayout>
  );
};

export default RecyclingGuidePage;