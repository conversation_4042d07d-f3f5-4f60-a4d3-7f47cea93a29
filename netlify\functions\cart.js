import express from 'express';
import serverless from 'serverless-http';
import dotenv from 'dotenv';
import { getSupabaseClient, testDatabaseConnection } from './db-connection.js';

// Load environment variables
dotenv.config();

// Create express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// Middleware to get user from token
const getUserFromToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization required' });
    }
    
    const token = authHeader.split(' ')[1];
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }
    
    req.user = user;
    next();
  } catch (error) {
    console.error('Error authenticating user:', error);
    return res.status(500).json({ error: 'Authentication error' });
  }
};

// Get cart items for user
app.get('/api/cart', getUserFromToken, async (req, res) => {
  try {
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Get all cart items for this user
    const { data: cartItems, error } = await supabase
      .from('cart_items')
      .select('*, products(*)')
      .eq('user_id', req.user.id);
      
    if (error) {
      console.error('Error fetching cart:', error);
      return res.status(500).json({ error: error.message });
    }
    
    return res.status(200).json(cartItems);
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Add item to cart
app.post('/api/cart', getUserFromToken, async (req, res) => {
  try {
    const { product_id, quantity } = req.body;
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Check if product exists
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('id, price')
      .eq('id', product_id)
      .single();
      
    if (productError || !product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    // Check if item already in cart
    const { data: existingItem, error: existingError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('user_id', req.user.id)
      .eq('product_id', product_id)
      .single();
      
    if (existingItem) {
      // Update quantity if item already in cart
      const newQuantity = existingItem.quantity + quantity;
      
      const { error: updateError } = await supabase
        .from('cart_items')
        .update({ quantity: newQuantity })
        .eq('id', existingItem.id);
        
      if (updateError) {
        console.error('Error updating cart item:', updateError);
        return res.status(500).json({ error: updateError.message });
      }
      
      return res.status(200).json({ message: 'Cart updated', item_id: existingItem.id });
    } else {
      // Add new item to cart
      const { data: newItem, error: insertError } = await supabase
        .from('cart_items')
        .insert([
          {
            user_id: req.user.id,
            product_id,
            quantity,
            price: product.price
          }
        ])
        .select()
        .single();
        
      if (insertError) {
        console.error('Error adding to cart:', insertError);
        return res.status(500).json({ error: insertError.message });
      }
      
      return res.status(201).json({ message: 'Added to cart', item: newItem });
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Update cart item quantity
app.put('/api/cart/:itemId', getUserFromToken, async (req, res) => {
  try {
    const { itemId } = req.params;
    const { quantity } = req.body;
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Verify the item belongs to this user
    const { data: cartItem, error: fetchError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('id', itemId)
      .eq('user_id', req.user.id)
      .single();
      
    if (fetchError || !cartItem) {
      return res.status(404).json({ error: 'Cart item not found' });
    }
    
    // Delete item if quantity is 0, otherwise update
    if (quantity <= 0) {
      const { error: deleteError } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', itemId);
        
      if (deleteError) {
        console.error('Error removing item from cart:', deleteError);
        return res.status(500).json({ error: deleteError.message });
      }
      
      return res.status(200).json({ message: 'Item removed from cart' });
    } else {
      const { error: updateError } = await supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', itemId);
        
      if (updateError) {
        console.error('Error updating cart item:', updateError);
        return res.status(500).json({ error: updateError.message });
      }
      
      return res.status(200).json({ message: 'Cart updated' });
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Remove item from cart
app.delete('/api/cart/:itemId', getUserFromToken, async (req, res) => {
  try {
    const { itemId } = req.params;
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Verify the item belongs to this user
    const { data: cartItem, error: fetchError } = await supabase
      .from('cart_items')
      .select('*')
      .eq('id', itemId)
      .eq('user_id', req.user.id)
      .single();
      
    if (fetchError || !cartItem) {
      return res.status(404).json({ error: 'Cart item not found' });
    }
    
    const { error: deleteError } = await supabase
      .from('cart_items')
      .delete()
      .eq('id', itemId);
      
    if (deleteError) {
      console.error('Error removing item from cart:', deleteError);
      return res.status(500).json({ error: deleteError.message });
    }
    
    return res.status(200).json({ message: 'Item removed from cart' });
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Export the serverless handler
export const handler = serverless(app);
