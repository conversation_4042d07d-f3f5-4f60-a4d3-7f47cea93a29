// Debug information endpoint for Vercel deployment
export default function handler(req, res) {
  // Collect environment information
  const environment = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    VERCEL: process.env.VERCEL ? true : false,
    VERCEL_ENV: process.env.VERCEL_ENV,
    VERCEL_URL: process.env.VERCEL_URL,
    VERCEL_REGION: process.env.VERCEL_REGION
  };

  // Collect database configuration
  const database = {
    DATABASE_URL: process.env.DATABASE_URL ? 'configured' : 'missing',
    POSTGRES_URL: process.env.POSTGRES_URL ? 'configured' : 'missing',
    POSTGRES_HOST: process.env.POSTGRES_HOST || 'not configured',
    POSTGRES_USER: process.env.POSTGRES_USER ? 'configured' : 'missing',
    POSTGRES_DATABASE: process.env.POSTGRES_DATABASE || process.env.PGDATABASE || 'not configured'
  };

  // Collect SSL configuration
  const ssl = {
    NODE_TLS_REJECT_UNAUTHORIZED: process.env.NODE_TLS_REJECT_UNAUTHORIZED,
    PGSSLMODE: process.env.PGSSLMODE,
    PG_SSL_REJECT_UNAUTHORIZED: process.env.PG_SSL_REJECT_UNAUTHORIZED,
    DISABLE_SSL_VALIDATION: process.env.DISABLE_SSL_VALIDATION
  };

  // Collect request information
  const request = {
    method: req.method,
    url: req.url,
    headers: req.headers,
    host: req.headers.host,
    origin: req.headers.origin,
    referer: req.headers.referer
  };

  // Return all collected information
  res.status(200).json({
    timestamp: new Date().toISOString(),
    environment,
    database,
    ssl,
    request,
    message: 'This endpoint provides debug information for troubleshooting deployment issues'
  });
}
