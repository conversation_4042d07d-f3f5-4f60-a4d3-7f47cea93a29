// Compatibility layer for @shared/schema.js
// This file re-exports the schema from the shared directory using a relative path
// This is needed for Vercel deployment where the @shared alias is not recognized

// Use relative path to import the schema
import * as schema from '../shared/schema.js';

// Re-export everything from the schema
export * from '../shared/schema.js';

// Also export the schema as default
export default schema;
