
import { db } from './db';
// Import schema from local compatibility layer instead of @shared/schema
import { categories, products } from './schema.js';
import { sql } from 'drizzle-orm';

export async function initializeDatabase() {
  console.log("Initializing database storage...");

  try {
    // Drop existing tables and recreate
    await db.delete(products).where(sql`true`);
    await db.delete(categories).where(sql`true`);

    console.log("Adding categories...");
    // Add categories with 4K images
    await db.insert(categories).values([
      {
        name: "Recycled Electronics",
        imageUrl: "https://images.unsplash.com/photo-1605792657660-596af9009e82?w=3840&q=100"
      },
      {
        name: "Upcycled Fashion",
        imageUrl: "https://images.unsplash.com/photo-1544441893-675973e31985?w=3840&q=100"
      },
      {
        name: "Eco-friendly Home",
        imageUrl: "https://images.unsplash.com/photo-1616486338812-3caddf4fb915?w=3840&q=100"
      },
      {
        name: "Recycled Accessories",
        imageUrl: "https://images.unsplash.com/photo-1630059897392-cf891829848c?w=3840&q=100"
      }
    ]);

    console.log("Adding products...");
    await db.insert(products).values([
      {
        name: "Cork Yoga Mat",
        description: "Natural cork yoga mat that's eco-friendly, non-slip and antimicrobial",
        price: 49.99,
        discountPrice: null,
        rating: 4.7,
        reviewCount: 18,
        imageUrl: "https://images.unsplash.com/photo-1601925260368-ae2f83cf8b7f?w=3840&q=100",
        categoryId: 3,
        inStock: true,
        isNew: true,
        isPopular: false,
        isSale: false,
        createdAt: new Date()
      },
      {
        name: "Recycled Denim Jacket",
        description: "Vintage denim jacket reimagined with sustainable materials",
        price: 129.99,
        discountPrice: 99.99,
        rating: 4.7,
        reviewCount: 45,
        imageUrl: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=3840&q=100",
        categoryId: 2,
        inStock: true,
        isNew: false,
        isPopular: false,
        isSale: true,
        createdAt: new Date()
      },
      {
        name: "Upcycled Leather Tote",
        description: "Handcrafted tote bag made from reclaimed leather",
        price: 189.99,
        discountPrice: null,
        rating: 4.8,
        reviewCount: 67,
        imageUrl: "https://images.unsplash.com/photo-1590874103328-eac38a683ce7?w=3840&q=100",
        categoryId: 2,
        inStock: true,
        isNew: false,
        isPopular: true,
        isSale: false,
        createdAt: new Date()
      },
      {
        name: "Bamboo Kitchen Set",
        description: "Complete kitchen utensil set made from sustainable bamboo",
        price: 79.99,
        discountPrice: 59.99,
        rating: 4.9,
        reviewCount: 234,
        imageUrl: "https://images.unsplash.com/photo-1610701596007-11502861dcfa?w=3840&q=100",
        categoryId: 3,
        inStock: true,
        isNew: false,
        isPopular: true,
        isSale: false,
        createdAt: new Date()
      },
      {
        name: "Ocean Plastic Watch",
        description: "Stylish timepiece crafted from recycled ocean plastics",
        price: 159.99,
        discountPrice: null,
        rating: 4.8,
        reviewCount: 128,
        imageUrl: "https://images.unsplash.com/photo-1542496658-e33a6d0d50f6?w=3840&q=100",
        categoryId: 4,
        inStock: true,
        isNew: true,
        isPopular: false,
        isSale: false,
        createdAt: new Date()
      }
    ]);

    console.log("Database initialization complete");
  } catch (error) {
    console.error("Error initializing database:", error);
    throw error;
  }
}
