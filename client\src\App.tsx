import { Switch, Route, useLocation } from "wouter";
import { useA<PERSON>, AuthProvider } from "@/hooks/use-auth";
import { WishlistProvider } from "@/hooks/use-wishlist";
import { Loader2 } from "lucide-react";
import HomePage from "@/pages/home-page";
import AuthPage from "@/pages/auth-page";
import LoginPage from "@/pages/login-page";
import NotFound from "@/pages/not-found";
import ProductPage from "@/pages/product-page";
import ProductDetailPage from "@/pages/product-detail-page";
import CartPage from "@/pages/cart-page";
import CheckoutPage from "@/pages/checkout-page";
import ProfilePage from "@/pages/profile-page";
import WishlistPage from "@/pages/wishlist-page";
import OrderDetailsPage from "@/pages/order-details-page";
import NewArrivalsPage from "@/pages/new-arrivals-page";
import PopularItemsPage from "@/pages/popular-items-page";
import OnSalePage from "@/pages/on-sale-page";
import RecyclingGuidePage from "@/pages/recycling-guide-page";
import DropoffLocationsPage from "@/pages/dropoff-locations-page";
import TradeInPage from "@/pages/trade-in-page";
import RepairServicesPage from "@/pages/repair-services-page";
import SustainabilityPage from "@/pages/sustainability-page";
import UserStatusPage from "@/pages/user-status-page";
import { Layout } from "@/components/layout/layout";

// Custom ProtectedRoute component
function ProtectedRoute({
  children,
  path
}: {
  children: React.ReactNode;
  path: string;
}) {
  const { user, isLoading } = useAuth();
  const [, navigate] = useLocation();

  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Route>
    );
  }

  // Check if we have a userId in localStorage even if user is not loaded yet
  const storedUserId = localStorage.getItem('userId');

  if (!user && !storedUserId) {
    console.log('No user or stored userId found, redirecting to auth page');
    // Use immediate redirect instead of setTimeout
    navigate("/auth");

    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Route>
    );
  }

  // If we have a stored userId but no user object yet, show loading
  if (!user && storedUserId) {
    console.log('Found stored userId but user not loaded yet, showing loading screen');
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading your profile...</span>
        </div>
      </Route>
    );
  }

  return (
    <Route path={path}>
      {children}
    </Route>
  );
}

// AppContent component wrapped by AuthProvider
function AppContent() {
  return (
    <Switch>
      <Route path="/">
        <Layout>
          <HomePage />
        </Layout>
      </Route>
      <Route path="/auth" component={AuthPage} />
      <Route path="/products" component={ProductPage} />
      <Route path="/products/:id" component={ProductDetailPage} />
      <Route path="/new-arrivals">
        <Layout>
          <NewArrivalsPage />
        </Layout>
      </Route>
      <Route path="/popular-items">
        <Layout>
          <PopularItemsPage />
        </Layout>
      </Route>
      <Route path="/on-sale">
        <Layout>
          <OnSalePage />
        </Layout>
      </Route>
      <ProtectedRoute path="/cart">
        <CartPage />
      </ProtectedRoute>
      <ProtectedRoute path="/checkout">
        <CheckoutPage />
      </ProtectedRoute>
      <ProtectedRoute path="/profile">
        <ProfilePage />
      </ProtectedRoute>
      <ProtectedRoute path="/wishlist">
        <Layout>
          <WishlistPage />
        </Layout>
      </ProtectedRoute>
      <ProtectedRoute path="/orders/:id">
        <OrderDetailsPage />
      </ProtectedRoute>
      <Route path="/recycling-guide">
        <Layout>
          <RecyclingGuidePage />
        </Layout>
      </Route>
      <Route path="/dropoff-locations">
        <Layout>
          <DropoffLocationsPage />
        </Layout>
      </Route>
      <Route path="/trade-in">
        <Layout>
          <TradeInPage />
        </Layout>
      </Route>
      <Route path="/repair-services">
        <Layout>
          <RepairServicesPage />
        </Layout>
      </Route>
      <Route path="/sustainability">
        <Layout>
          <SustainabilityPage />
        </Layout>
      </Route>
      <Route path="/user-status">
        <Layout>
          <UserStatusPage />
        </Layout>
      </Route>
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <AuthProvider>
      <WishlistProvider>
        <AppContent />
      </WishlistProvider>
    </AuthProvider>
  );
}

export default App;
