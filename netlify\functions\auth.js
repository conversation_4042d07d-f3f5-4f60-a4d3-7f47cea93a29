import express from 'express';
import serverless from 'serverless-http';
import cors from 'cors';
import { getSupabaseClient } from '../utils/supabase';

const app = express();

// Add CORS and JSON parsing middleware
app.use(cors());
app.use(express.json());

app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        error: 'Email and password are required'
      });
    }

    const supabase = getSupabaseClient();
    if (!supabase) {
      return res.status(500).json({
        error: 'Authentication service unavailable'
      });
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      return res.status(401).json({
        error: error.message || 'Invalid credentials'
      });
    }

    // Return user data with session
    return res.status(200).json({
      user: data.user,
      session: data.session
    });

  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      error: 'An unexpected error occurred'
    });
  }
});

export const handler = serverless(app);


