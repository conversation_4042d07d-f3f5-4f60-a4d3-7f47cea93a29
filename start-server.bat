@echo off
echo Starting server on port 3000...

REM Set environment variables
set NODE_TLS_REJECT_UNAUTHORIZED=0
set PGSSLMODE=no-verify
set PG_SSL_REJECT_UNAUTHORIZED=0
set VERCEL=true
set PORT=3000
set HOST=0.0.0.0

REM Run the server
echo Running: npx tsx server/index.ts
echo Server will be available at http://localhost:3000
npx tsx server/index.ts

REM If tsx fails, try with node directly
if %ERRORLEVEL% NEQ 0 (
  echo.
  echo tsx command failed, trying with node...
  node --import tsx server/index.ts
)

pause
