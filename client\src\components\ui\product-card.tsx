import { Link, useLocation } from "wouter";
import { Heart, Eye, ShoppingCart, Loader2 } from "lucide-react";
import { Product } from "@shared/schema";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useWishlist } from "@/hooks/use-wishlist";
import { useState } from "react";

type ProductCardProps = {
  product: Product;
};

export function ProductCard({ product }: ProductCardProps) {
  // Ensure rating and reviewCount have fallback values
  const rating = product.rating || 0;
  const reviewCount = product.reviewCount || 0;
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Use the useAuth hook to get user info
  const { user } = useAuth();

  // Use the useWishlist hook to get wishlist functionality
  const wishlistHook = useWishlist();

  // Safely access wishlist properties with fallbacks
  const isInWishlist = wishlistHook?.isInWishlist;
  const addToWishlist = wishlistHook?.addToWishlist;
  const removeFromWishlist = wishlistHook?.removeFromWishlist;
  const wishlistItems = wishlistHook?.wishlistItems || [];
  const isWishlistLoading = wishlistHook?.isLoading || false;

  // Determine if this product is in the wishlist
  const isProductInWishlist = isInWishlist ? isInWishlist(product.id) : false;

  // State to track loading state for wishlist operations
  const [isWishlistItemLoading, setIsWishlistItemLoading] = useState(false);

  // Add to cart mutation
  const addToCartMutation = useMutation({
    mutationFn: async (productId: number) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to add items to your cart");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      // Make a direct fetch request to ensure it works
      console.log(`Adding product ${productId} to cart for user ${user.id}`);

      const response = await fetch(`/api/cart?userId=${user.id}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: parseInt(String(user.id)),
          productId: parseInt(String(productId)),
          quantity: 1
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from server:', errorText);
        throw new Error(`Failed to add to cart: ${response.status} ${response.statusText}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log('Successfully added to cart:', data);

      if (user) {
        // Force invalidate the cart query to refresh the data
        queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
        queryClient.invalidateQueries({ queryKey: ["/api/cart", { userId: user.id }] });

        // Force refetch the cart data
        queryClient.refetchQueries({ queryKey: ["/api/cart"] });
        queryClient.refetchQueries({ queryKey: ["/api/cart", { userId: user.id }] });
      }

      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your cart.`,
      });
    },
    onError: (error) => {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add product to cart. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleAddToCart = () => {
    if (!user) {
      toast({
        title: "Login required",
        description: "Please login to add items to your cart",
        variant: "destructive",
      });
      navigate('/auth');
      return;
    }

    addToCartMutation.mutate(product.id);
  };

  // Handle toggling wishlist items
  const handleWishlistToggle = async () => {
    if (!user) {
      toast({
        title: "Login required",
        description: "Please login to add items to your wishlist",
        variant: "destructive",
      });
      navigate('/auth');
      return;
    }

    try {
      setIsWishlistItemLoading(true);

      if (isProductInWishlist && wishlistItems && removeFromWishlist) {
        // Find the wishlist item to remove
        const wishlistItem = wishlistItems.find((item: any) =>
          item.productId === product.id || (item.product && item.product.id === product.id)
        );
        if (wishlistItem) {
          await removeFromWishlist(wishlistItem.id);
          toast({
            title: "Removed from wishlist",
            description: `${product.name} has been removed from your wishlist.`,
          });
        }
      } else if (addToWishlist) {
        await addToWishlist(product.id);
        toast({
          title: "Added to wishlist",
          description: `${product.name} has been added to your wishlist.`,
        });
      } else {
        throw new Error("Wishlist functionality is not available");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsWishlistItemLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden product-card transition duration-300">
      <div className="relative">
        <Link href={`/products/${product.id}`}>
          <img
            src={product.imageUrl}
            alt={product.name}
            className="w-full h-48 sm:h-56 object-cover"
            onLoad={() => {
              console.log(`Successfully loaded image for product ${product.id} (${product.name})`);
            }}
            onError={(e) => {
              // If image fails to load, use category-specific fallback images
              const target = e.target as HTMLImageElement;
              console.log(`Image failed to load for product ${product.id} (${product.name}): ${product.imageUrl}`);

              // Try to fix the URL if it's malformed
              let fixedUrl = product.imageUrl;
              if (fixedUrl && fixedUrl.includes('?')) {
                fixedUrl = fixedUrl.split('?')[0];
                console.log(`Trying with fixed URL: ${fixedUrl}`);
                target.src = fixedUrl;
                return;
              }

              // Use category-specific fallback images without query parameters
              if (product.categoryId === 1) {
                // Recycled Electronics
                target.src = 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
              } else if (product.categoryId === 2) {
                // Upcycled Fashion
                target.src = 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
              } else if (product.categoryId === 3) {
                // Eco-friendly Home
                target.src = 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
              } else if (product.categoryId === 4) {
                // Recycled Accessories
                target.src = 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
              } else {
                // Default fallback
                target.src = 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
              }
              console.log(`Using fallback image for product ${product.id} (${product.name}) with category ${product.categoryId}`);
            }}
          />
        </Link>
        <div className="absolute top-2 right-2 flex flex-col gap-2">
          <Button
            variant="ghost"
            size="icon"
            className={`w-8 h-8 rounded-full bg-white shadow-sm ${isProductInWishlist ? 'text-red-500' : 'text-gray-500'} hover:text-secondary`}
            onClick={handleWishlistToggle}
            disabled={isWishlistItemLoading || isWishlistLoading}
          >
            {isWishlistItemLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Heart className={`w-4 h-4 ${isProductInWishlist ? 'fill-current' : ''}`} />
            )}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 rounded-full bg-white shadow-sm text-gray-500 hover:text-secondary"
            onClick={() => navigate(`/products/${product.id}`)}
          >
            <Eye className="w-4 h-4" />
          </Button>
        </div>
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {/* Show tags based on current page */}
          {location.pathname === '/popular-items' ? (
            product.isPopular && (
              <span className="inline-block px-2 py-1 bg-primary text-white text-xs font-medium rounded">Popular</span>
            )
          ) : location.pathname === '/on-sale' ? (
            product.isSale && (
              <span className="inline-block px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-md">On Sale</span>
            )
          ) : (
            <>
              {product.isNew && (
                <span className="inline-block px-2 py-1 bg-accent text-white text-xs font-medium rounded">RECENT</span>
              )}
              {product.isSale && (
                <span className="inline-block px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-md">On Sale</span>
              )}
              {product.isPopular && (
                <span className="inline-block px-2 py-1 bg-primary text-white text-xs font-medium rounded">Popular</span>
              )}
            </>
          )}
        </div>
      </div>
      <div className="p-4">
        <Link href={`/products?category=${product.categoryId}`} className="text-xs text-gray-500 hover:text-secondary transition">
          {/* Display recycled product categories */}
          {product.categoryId === 1 ? "Recycled Electronics" :
           product.categoryId === 2 ? "Upcycled Fashion" :
           product.categoryId === 3 ? "Eco-friendly Home" :
           product.categoryId === 4 ? "Recycled Accessories" : "Other"}
        </Link>
        <Link href={`/products/${product.id}`}>
          <h3 className="font-medium font-poppins text-primary mt-1 hover:text-secondary transition">
            {product.name}
          </h3>
        </Link>
        <div className="flex items-center mt-1 mb-2">
          <div className="flex text-accent text-sm">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill={i < Math.floor(rating) ? "currentColor" : "none"}
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={i < Math.floor(rating) ? "" : "text-gray-300"}
              >
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-1">({reviewCount})</span>
        </div>
        <div className="flex justify-between items-center">
          <div>
            {product.discountPrice ? (
              <div className="flex flex-col">
                <span className="font-semibold text-red-500 text-lg">${product.discountPrice.toFixed(2)}</span>
                <span className="text-sm text-gray-500 line-through">${product.price.toFixed(2)}</span>
              </div>
            ) : (
              <span className="font-medium text-primary text-lg">${product.price.toFixed(2)}</span>
            )}
          </div>
          <Button
            variant="secondary"
            size="icon"
            className="w-8 h-8 rounded-full bg-primary text-white hover:bg-primary/80"
            onClick={handleAddToCart}
            disabled={addToCartMutation.isPending}
          >
            {addToCartMutation.isPending ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <ShoppingCart className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}