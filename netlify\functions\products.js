import express from 'express';
import serverless from 'serverless-http';
import dotenv from 'dotenv';
import { getSupabaseClient, testDatabaseConnection, initializeDatabase } from './db-connection.js';

// Load environment variables
dotenv.config();

// Create express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// Initialize database tables if they don't exist
initializeDatabase().catch(err => {
  console.error('Database initialization error:', err);
});

// Get all products endpoint
app.get('/api/products', async (req, res) => {
  try {
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ 
        error: 'Database connection failed',
        message: 'Could not connect to the database. Check your environment variables.'
      });
    }
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*, categories(*)');
      
    if (error) {
      console.error('Error fetching products:', error);
      return res.status(500).json({ error: error.message });
    }
    
    if (!products || products.length === 0) {
      console.log('No products found, returning fallback data');
      // Return fallback product data when the database is empty
      return res.status(200).json([
        {
          id: 1,
          name: 'Refurbished Smartphone',
          description: 'Like-new refurbished phone with 1-year warranty.',
          price: 329.99,
          rating: 4.5,
          reviewCount: 34,
          imageUrl: 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg',
          categoryId: 1,
          isNew: true,
          categories: { id: 1, name: 'Recycled Electronics' }
        },
        {
          id: 2,
          name: 'Recycled Plastic Watch',
          description: 'Stylish watch made from ocean plastic.',
          price: 79.99,
          rating: 4.1, 
          reviewCount: 27,
          imageUrl: 'https://images.pexels.com/photos/3908800/pexels-photo-3908800.jpeg',
          categoryId: 4,
          isNew: true,
          categories: { id: 4, name: 'Recycled Accessories' }
        }
      ]);
    }
    
    return res.status(200).json(products);
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Get categories endpoint
app.get('/api/categories', async (req, res) => {
  try {
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ 
        error: 'Database connection failed',
        message: 'Could not connect to the database. Check your environment variables.'
      });
    }
    
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*');
      
    if (error) {
      console.error('Error fetching categories:', error);
      return res.status(500).json({ error: error.message });
    }
    
    if (!categories || categories.length === 0) {
      console.log('No categories found, returning fallback data');
      // Return fallback category data when the database is empty
      return res.status(200).json([
        { id: 1, name: 'Recycled Electronics', imageUrl: 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg' },
        { id: 2, name: 'Upcycled Fashion', imageUrl: 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg' },
        { id: 3, name: 'Eco-friendly Home', imageUrl: 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg' },
        { id: 4, name: 'Recycled Accessories', imageUrl: 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg' }
      ]);
    }
    
    return res.status(200).json(categories);
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Get product by ID
app.get('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const supabase = getSupabaseClient();
    
    if (!supabase) {
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    const { data: product, error } = await supabase
      .from('products')
      .select('*, categories(*)')
      .eq('id', id)
      .single();
      
    if (error) {
      console.error(`Error fetching product ${id}:`, error);
      return res.status(500).json({ error: error.message });
    }
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    return res.status(200).json(product);
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'An unexpected error occurred' });
  }
});

// Export the serverless handler
export const handler = serverless(app);
