// Direct database initialization API endpoint
// This endpoint directly initializes the database with sample data


import { Request, Response } from 'express';
import postgres from 'postgres';

// Set SSL settings for Postgres
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
process.env.PGSSLMODE = 'no-verify';

// Sample data
const categories = [
  {
    name: "Recycled Electronics",
    imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg"
  },
  {
    name: "Upcycled Fashion",
    imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg"
  },
  {
    name: "Eco-friendly Home",
    imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg"
  },
  {
    name: "Recycled Accessories",
    imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg"
  }
];

const products = [
  {
    name: "Refurbished Smartphone",
    description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
    price: 329.99,
    discountPrice: 299.99,
    rating: 4.5,
    reviewCount: 34,
    imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
    categoryId: 1,
    inStock: true,
    isNew: true,
    isPopular: false,
    isSale: true
  },
  {
    name: "Upcycled Denim Jacket",
    description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
    price: 89.99,
    discountPrice: null,
    rating: 4.8,
    reviewCount: 42,
    imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
    categoryId: 2,
    inStock: true,
    isNew: true,
    isPopular: true,
    isSale: false
  },
  {
    name: "Recycled Metal Water Bottle",
    description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
    price: 29.99,
    discountPrice: 24.99,
    rating: 4.9,
    reviewCount: 120,
    imageUrl: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg",
    categoryId: 4,
    inStock: true,
    isNew: false,
    isPopular: true,
    isSale: true
  },
  {
    name: "Bamboo Desk Organizer",
    description: "Sustainable bamboo desk organizer with multiple compartments for all your office essentials.",
    price: 49.99,
    discountPrice: null,
    rating: 4.6,
    reviewCount: 28,
    imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg",
    categoryId: 3,
    inStock: true,
    isNew: false,
    isPopular: false,
    isSale: false
  }
];

// Initialize database
async function directInitializeDatabase(force = false) {
  console.log('Attempting to connect to database in Vercel environment');
  console.log('Database URL configured:', process.env.DATABASE_URL ? '✓ Present' : '✗ Missing');
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('SSL Mode:', process.env.PGSSLMODE || 'default');
  console.log('SSL Reject Unauthorized:', process.env.NODE_TLS_REJECT_UNAUTHORIZED || 'default');

  try {
    // Get connection string
    const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('No database connection string found');
    }

    console.log('Connecting to PostgreSQL database from Vercel...');

    // Configure connection
    const sql = postgres(connectionString, {
      ssl: { rejectUnauthorized: false },
      max: 1,
      idle_timeout: 5,
      connect_timeout: 10
    });

    console.log('Successfully connected to PostgreSQL database from Vercel');
    console.log('Starting direct database initialization...');

    // Check if we need to initialize
    if (!force) {
      const categoriesCount = await sql`SELECT COUNT(*) FROM categories`;
      const productsCount = await sql`SELECT COUNT(*) FROM products`;
      console.log('Current database state:', categoriesCount[0].count, 'categories,', productsCount[0].count, 'products');

      if (categoriesCount[0].count > 0 && productsCount[0].count > 0) {
        console.log('Database already has data, skipping initialization (use force=true to override)');
        await sql.end();
        return true;
      }
    }

    // Clear existing data
    console.log('Clearing existing data...');
    try {
      await sql`TRUNCATE TABLE "wishlist_items" CASCADE`;
      await sql`TRUNCATE TABLE "cart_items" CASCADE`;
      await sql`TRUNCATE TABLE "order_items" CASCADE`;
      await sql`TRUNCATE TABLE "orders" CASCADE`;
      await sql`TRUNCATE TABLE "products" CASCADE`;
      await sql`TRUNCATE TABLE "categories" CASCADE`;
      await sql`TRUNCATE TABLE "users" CASCADE`;
      console.log('Existing data cleared successfully');
    } catch (clearError) {
      console.error('Error clearing existing data:', clearError);
      // Continue with initialization even if clearing fails
    }

    // Add categories
    console.log('Adding categories...');
    for (const category of categories) {
      try {
        await sql`
          INSERT INTO "categories" ("name", "image_url")
          VALUES (${category.name}, ${category.imageUrl})
        `;
      } catch (categoryError) {
        console.error('Error adding category:', category.name, categoryError);
      }
    }
    console.log('Categories added successfully');

    // Add products
    console.log('Adding products...');
    for (const product of products) {
      try {
        await sql`
          INSERT INTO "products" (
            "name", "description", "price", "discount_price", "rating",
            "review_count", "image_url", "category_id", "in_stock",
            "is_new", "is_popular", "is_sale"
          )
          VALUES (
            ${product.name}, ${product.description}, ${product.price},
            ${product.discountPrice}, ${product.rating}, ${product.reviewCount},
            ${product.imageUrl}, ${product.categoryId}, ${product.inStock},
            ${product.isNew}, ${product.isPopular}, ${product.isSale}
          )
        `;
      } catch (productError) {
        console.error('Error adding product:', product.name, productError);
      }
    }
    console.log('Products added successfully');

    // Demo user and sample orders have been removed

    console.log('Direct database initialization complete');
    await sql.end();
    return true;
  } catch (error) {
    console.error('Error during direct database initialization:', error);
    return false;
  }
}

// API handler for direct database initialization
export default async function handler(req: Request, res: Response) {
  try {
    const force = req.query.force === 'true';
    console.log(`Direct database initialization requested (force=${force})`);

    const success = await directInitializeDatabase(force);

    if (success) {
      res.status(200).json({ success: true, message: 'Database initialized successfully' });
    } else {
      res.status(500).json({ success: false, message: 'Failed to initialize database' });
    }
  } catch (error) {
    console.error('Error in direct-init-db API handler:', error);
    res.status(500).json({ success: false, message: 'Error initializing database', error: String(error) });
  }
}
