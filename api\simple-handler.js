// Simple serverless handler for Vercel
import express from 'express';
import dotenv from 'dotenv';

// Load environment variables if needed
if (!process.env.DATABASE_URL) {
  try {
    dotenv.config({ path: '.env.production' });
    console.log('Loaded .env.production file');
  } catch (error) {
    console.error('Error loading environment variables');
  }
}

// Create Express app
const app = express();

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ 
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Debug endpoint
app.get('/api/debug', (req, res) => {
  res.status(200).json({
    environment: process.env.NODE_ENV || 'development',
    vercel: process.env.VERCEL ? true : false,
    database: {
      url: process.env.DATABASE_URL ? 'configured' : 'missing',
      host: process.env.POSTGRES_HOST || 'not configured'
    },
    timestamp: new Date().toISOString()
  });
});

// Default handler for all API routes
app.use('/api/*', (req, res) => {
  res.status(200).json({ 
    message: 'API endpoint reached successfully', 
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Static file fallback
app.use('*', (req, res) => {
  res.status(200).send(`
    <html>
      <head>
        <title>E-commerce App</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
          h1 { color: #333; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        </style>
      </head>
      <body>
        <h1>E-commerce Application</h1>
        <div class="card">
          <h2>Deployment Status</h2>
          <p>Your application is deployed and the frontend is ready.</p>
          <p>API functionality is being configured for production.</p>
        </div>
        <div class="card">
          <h2>Environment</h2>
          <pre>NODE_ENV: ${process.env.NODE_ENV || 'development'}</pre>
          <pre>Database: ${process.env.DATABASE_URL ? 'Configured' : 'Not configured'}</pre>
          <pre>Vercel: ${process.env.VERCEL ? 'Yes' : 'No'}</pre>
        </div>
      </body>
    </html>
  `);
});

// Vercel serverless handler
export default function handler(req, res) {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  return app(req, res);
}
