# Production environment variables for Vercel deployment

# Recommended for most uses
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# For uses requiring a connection without pgbouncer
DATABASE_URL_UNPOOLED=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Parameters for constructing your own connection string
PGHOST=ep-ancient-bonus-a5s8n9fm-pooler.us-east-2.aws.neon.tech
PGHOST_UNPOOLED=ep-ancient-bonus-a5s8n9fm.us-east-2.aws.neon.tech
PGUSER=neondb_owner
PGDATABASE=neondb
PGPASSWORD=npg_jvl0CPOQ2DrT

# Parameters for Vercel Postgres Templates
POSTGRES_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_URL_NON_POOLING=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_USER=neondb_owner
POSTGRES_HOST=ep-ancient-bonus-a5s8n9fm-pooler.us-east-2.aws.neon.tech
POSTGRES_PASSWORD=npg_jvl0CPOQ2DrT
POSTGRES_DATABASE=neondb
POSTGRES_URL_NO_SSL=postgres://neondb_owner:<EMAIL>/neondb
POSTGRES_PRISMA_URL=postgres://neondb_owner:<EMAIL>/neondb?connect_timeout=15&sslmode=require

# SSL settings
NODE_TLS_REJECT_UNAUTHORIZED=0
PGSSLMODE=no-verify
PG_SSL_REJECT_UNAUTHORIZED=0

# Environment settings
NODE_ENV=production
