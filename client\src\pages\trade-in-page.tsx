import { ChevronRight, Check, ArrowRight, HelpCircle, RefreshCw, BadgePercent } from "lucide-react";
import { ContentOnlyLayout } from "@/components/layout/content-only-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "wouter";

interface TradeInCategory {
  id: number;
  name: string;
  description: string;
  acceptedItems: string[];
  estimatedValue: string;
  icon: JSX.Element;
}

const tradeInCategories: TradeInCategory[] = [
  {
    id: 1,
    name: "Electronics",
    description: "Trade in your used electronics for store credit or cash. We accept a wide range of devices in working condition.",
    acceptedItems: [
      "Smartphones (less than 5 years old)",
      "Tablets & iPads (less than 5 years old)",
      "Laptops & computers (less than 7 years old)",
      "Digital cameras",
      "Gaming consoles",
      "Smart watches",
    ],
    estimatedValue: "₹500 - ₹20,000 depending on device condition and age",
    icon: <RefreshCw className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 2,
    name: "Appliances",
    description: "Get value for your functioning appliances. Perfect when upgrading to newer, more energy-efficient models.",
    acceptedItems: [
      "Refrigerators (less than 10 years old)",
      "Washing machines & dryers",
      "Dishwashers",
      "Microwaves",
      "Air conditioners",
      "Kitchen appliances (coffee makers, blenders, etc.)",
    ],
    estimatedValue: "₹1,000 - ₹15,000 depending on appliance type, age, and condition",
    icon: <RefreshCw className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 3,
    name: "Furniture",
    description: "Trade in your gently used furniture when purchasing new items from our sustainable furniture collection.",
    acceptedItems: [
      "Sofas & armchairs (no major damage)",
      "Tables & desks",
      "Bed frames & mattresses (clean, no stains)",
      "Bookshelves & storage units",
      "Office chairs",
      "Dining sets",
    ],
    estimatedValue: "₹800 - ₹10,000 based on condition, brand, and material",
    icon: <RefreshCw className="h-8 w-8 text-[#8bbe1b]" />
  },
  {
    id: 4,
    name: "Clothing & Textiles",
    description: "Give your clothing a second life. Receive store credit for your gently used garments and textiles.",
    acceptedItems: [
      "Clean clothing (all sizes, adult and children)",
      "Shoes in wearable condition",
      "Handbags & accessories",
      "Bed linens & towels (clean, no stains)",
      "Curtains & drapes",
      "Decorative pillows & throws",
    ],
    estimatedValue: "₹50 - ₹2,000 based on brand, condition, and current market value",
    icon: <RefreshCw className="h-8 w-8 text-[#8bbe1b]" />
  },
];

const TradeInPage = () => {
  return (
    <ContentOnlyLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-[#8bbe1b] transition-colors">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <span>Trade-in Program</span>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
              Trade-in Program
            </h1>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Give your used items a second life and earn store credit or cash. 
              Our trade-in program helps reduce waste while providing value for your unwanted items.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
            <div className="bg-amber-50 border border-amber-100 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="bg-[#8bbe1b] rounded-full p-2">
                  <BadgePercent className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-800">Program Benefits</h2>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-[#8bbe1b] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Get store credit or cash for your unwanted items</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-[#8bbe1b] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Reduce landfill waste by giving items a second life</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-[#8bbe1b] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Earn 10% bonus credit when trading in for sustainable products</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-[#8bbe1b] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Free pickup available for larger items like furniture and appliances</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-[#8bbe1b] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Tax deduction receipts available for donated items</span>
                </li>
              </ul>
            </div>

            <div className="bg-amber-50 border border-amber-100 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="bg-[#8bbe1b] rounded-full p-2">
                  <ArrowRight className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-800">How It Works</h2>
              </div>
              <ol className="space-y-4">
                <li className="flex items-start gap-3">
                  <div className="bg-[#8bbe1b] rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Assess Your Items</h3>
                    <p className="text-gray-600 text-sm">Check our accepted items list to see if your items qualify for the trade-in program.</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="bg-[#8bbe1b] rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Get a Quote</h3>
                    <p className="text-gray-600 text-sm">Submit photos and details of your items online or bring them to any W2W location for assessment.</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="bg-[#8bbe1b] rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Accept Offer</h3>
                    <p className="text-gray-600 text-sm">If you're satisfied with our quote, accept the offer and choose between store credit or cash.</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="bg-[#8bbe1b] rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">4</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Drop Off or Schedule Pickup</h3>
                    <p className="text-gray-600 text-sm">Drop off smaller items at any W2W location or schedule a free pickup for larger items.</p>
                  </div>
                </li>
              </ol>
            </div>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800 text-center">What We Accept</h2>
            <Tabs defaultValue="1" className="w-full">
              <TabsList className="w-full grid grid-cols-2 md:grid-cols-4 mb-6">
                {tradeInCategories.map((category) => (
                  <TabsTrigger key={category.id} value={category.id.toString()} className="text-sm">
                    {category.name}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {tradeInCategories.map((category) => (
                <TabsContent key={category.id} value={category.id.toString()}>
                  <Card className="border-amber-100">
                    <CardHeader>
                      <div className="flex items-center gap-4">
                        {category.icon}
                        <div>
                          <CardTitle>{category.name}</CardTitle>
                          <CardDescription>{category.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-sm font-semibold text-gray-700 mb-2">Accepted Items:</h3>
                          <ul className="space-y-1">
                            {category.acceptedItems.map((item, index) => (
                              <li key={index} className="flex items-start gap-2 text-sm">
                                <Check className="h-4 w-4 text-[#8bbe1b] mt-1 flex-shrink-0" />
                                <span className="text-gray-600">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h3 className="text-sm font-semibold text-gray-700 mb-2">Estimated Value Range:</h3>
                          <p className="text-gray-600 text-sm">{category.estimatedValue}</p>
                          <div className="mt-6">
                            <Button className="bg-[#8bbe1b] hover:bg-[#7aa518] text-white">
                              Get a Quote
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Frequently Asked Questions</h2>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-amber-200">
                <AccordionTrigger className="text-gray-800 hover:text-[#8bbe1b]">
                  How is the value of my items determined?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600">
                  We assess items based on their condition, age, brand, market demand, and resale potential. Our expert team uses industry-standard valuation tools to provide fair and competitive offers.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-amber-200">
                <AccordionTrigger className="text-gray-800 hover:text-[#8bbe1b]">
                  What happens if my item doesn't qualify for trade-in?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600">
                  If an item doesn't meet our trade-in criteria, we can still help with responsible disposal or recycling. We partner with specialized recycling facilities to ensure items are handled in an environmentally friendly manner.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-amber-200">
                <AccordionTrigger className="text-gray-800 hover:text-[#8bbe1b]">
                  Is store credit worth more than cash?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600">
                  Yes! Choosing store credit gives you an additional 10% bonus on your trade-in value. Plus, when you use your credit to purchase sustainable or eco-friendly products, you'll receive an additional 5% discount.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-amber-200">
                <AccordionTrigger className="text-gray-800 hover:text-[#8bbe1b]">
                  How long does the trade-in process take?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600">
                  For in-store assessments, you'll receive an offer immediately. Online quotes typically take 1-2 business days. Once you accept an offer, payment is processed within 3-5 business days for cash options, or store credit is available immediately.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="border-amber-200">
                <AccordionTrigger className="text-gray-800 hover:text-[#8bbe1b]">
                  What if I'm not satisfied with my trade-in offer?
                </AccordionTrigger>
                <AccordionContent className="text-gray-600">
                  There's no obligation to accept our offers. If you're not satisfied, you can decline and either take your items back or let us recycle them responsibly for you at no cost.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>

          <div className="bg-[#8bbe1b]/10 rounded-lg p-6 mb-8">
            <div className="flex items-start gap-4">
              <div className="bg-[#8bbe1b] rounded-full p-2 flex-shrink-0">
                <HelpCircle className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2 text-gray-800">Need Assistance?</h3>
                <p className="text-gray-600 mb-4">
                  Our trade-in specialists are here to help with any questions or concerns about the program.
                  Contact us via phone, email, or visit any W2W location for personalized assistance.
                </p>
                <div className="flex flex-wrap gap-3">
                  <Button className="bg-[#8bbe1b] hover:bg-[#7aa518] text-white">
                    Contact Support
                  </Button>
                  <Button variant="outline" className="border-[#8bbe1b] text-[#8bbe1b]">
                    Schedule an Appraisal
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h3 className="text-xl font-bold mb-4 text-gray-800">Explore Related Sustainability Resources</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/recycling-guide" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Recycling Guide</h4>
                <p className="text-sm text-gray-600">Learn how to recycle properly</p>
              </Link>
              <Link href="/dropoff-locations" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Drop-off Locations</h4>
                <p className="text-sm text-gray-600">Find nearby recycling centers</p>
              </Link>
              <Link href="/repair-services" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Repair Services</h4>
                <p className="text-sm text-gray-600">Fix instead of replace</p>
              </Link>
              <Link href="/sustainability" className="p-4 border border-amber-100 rounded-lg hover:border-[#8bbe1b] hover:bg-[#8bbe1b]/5 transition-colors text-center">
                <h4 className="font-semibold text-gray-800">Sustainability Tips</h4>
                <p className="text-sm text-gray-600">Live more eco-friendly</p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ContentOnlyLayout>
  );
};

export default TradeInPage;