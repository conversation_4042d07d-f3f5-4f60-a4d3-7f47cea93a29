import { useQuery } from "@tanstack/react-query";
import { Link } from "wouter";
import { Product, Category } from "@shared/schema";
import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { ImagePreloader } from "@/components/ui/lazy-image";

import { ProductCard } from "@/components/ui/product-card";
import { CategoryCard } from "@/components/ui/category-card";
import { FeatureCard } from "@/components/ui/feature-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Truck,
  RefreshCcw,
  RecycleIcon,
  Headphones,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// W2W marquee item component (similar to header)
const W2WMarqueeItem = ({ size = "lg", color = "text-white" }) => {
  // Determine icon size based on text size
  let iconSize = "4";
  let textClass = "sm";
  let spacing = "mx-8";

  if (size === "xl") {
    iconSize = "5";
    textClass = "sm";
  } else if (size === "2xl") {
    iconSize = "6";
    textClass = "base";
  } else if (size === "3xl") {
    iconSize = "7";
    textClass = "lg";
  } else if (size === "4xl") {
    iconSize = "9";
    textClass = "2xl";
    spacing = "mx-14";
  }

  return (
    <div className={`flex items-center ${spacing}`}>
      <RecycleIcon className={`${color} w-${iconSize} h-${iconSize} mr-2`} />
      <div className="flex items-center">
        <span className={`text-${size} font-bold font-poppins ${color}`}>W</span>
        <span className={`text-${size} font-bold font-poppins text-amber-100 mx-0.5`}>2</span>
        <span className={`text-${size} font-bold font-poppins ${color}`}>W</span>
      </div>
      <span className={`text-${textClass} font-semibold ${color} ml-2`}>WASTE TO WEALTH</span>
    </div>
  );
};

export default function HomePage() {
  // Create arrays of repeating items for marquee rows
  // These variables are no longer used after removing W2W animation
  // const marqueeItems1 = Array.from({ length: 12 }, (_, i) => <W2WMarqueeItem key={`item1-${i}`} size="4xl" />);
  // const marqueeItems2 = Array.from({ length: 12 }, (_, i) => <W2WMarqueeItem key={`item2-${i}`} size="4xl" />);

  // Add state for tracking sticky navigation
  const [isSticky, setIsSticky] = useState(false);
  const [headerHeight, setHeaderHeight] = useState(0);
  const categoryNavRef = useRef<HTMLDivElement>(null);

  // Fallback categories in case API fails
  const fallbackCategories = [
    {
      id: 1,
      name: "Recycled Electronics",
      imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg"
    },
    {
      id: 2,
      name: "Upcycled Fashion",
      imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg"
    },
    {
      id: 3,
      name: "Eco-friendly Home",
      imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg"
    },
    {
      id: 4,
      name: "Recycled Accessories",
      imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg"
    }
  ];

  // Placeholder products with realistic names and images
  const sampleProducts = [
    {
      id: 1,
      title: "Recycled Denim Backpack",
      price: 79.99,
      originalPrice: 99.99,
      discountPercentage: 20,
      rating: 4.8,
      imageUrl: "/api/placeholder/600/600", // Placeholder for 4K image
      category: "Accessories",
      tags: ["recycled", "sustainable", "backpack"],
      isPopular: true,
      isNew: true
    },
    {
      id: 2,
      title: "Refurbished iPhone 14 Pro",
      price: 699.99,
      originalPrice: 999.99,
      discountPercentage: 30,
      rating: 4.7,
      imageUrl: "/api/placeholder/600/600", // Placeholder for 4K image
      category: "Electronics",
      tags: ["refurbished", "tech", "apple"],
      isPopular: true,
      isNew: false
    },
    {
      id: 3,
      title: "Upcycled Wooden Coffee Table",
      price: 289.99,
      originalPrice: 339.99,
      discountPercentage: 15,
      rating: 4.9,
      imageUrl: "/api/placeholder/600/600", // Placeholder for 4K image
      category: "Furniture",
      tags: ["upcycled", "wooden", "sustainable"],
      isPopular: true,
      isNew: true
    },
    {
      id: 4,
      title: "Eco-Friendly Water Bottle",
      price: 29.99,
      originalPrice: 34.99,
      discountPercentage: 14,
      rating: 4.6,
      imageUrl: "/api/placeholder/600/600", // Placeholder for 4K image
      category: "Home",
      tags: ["eco-friendly", "reusable", "BPA-free"],
      isPopular: true,
      isNew: false
    }
  ];

  // Fetch categories - use fallback categories if there's an error
  const { data: categories, isLoading: categoriesLoading } = useQuery<Category[]>({
    queryKey: ["/api/categories"],
    staleTime: 15 * 60 * 1000, // 15 minutes - categories don't change often
    gcTime: 30 * 60 * 1000, // 30 minutes garbage collection
    refetchOnWindowFocus: false, // Don't refetch on window focus for static data
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    onError: (error) => {
      console.error("Error fetching categories:", error);
      console.log("Using fallback categories due to error");
    },
    // Use fallback categories if there's an error
    placeholderData: fallbackCategories
  });

  // Fetch popular products - use sample products if there's an error
  const { data: popularProducts, isLoading: productsLoading } = useQuery<Product[]>({
    queryKey: ["/api/products", { isPopular: true }],
    staleTime: 10 * 60 * 1000, // 10 minutes - products can be cached longer
    gcTime: 20 * 60 * 1000, // 20 minutes garbage collection
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    onError: (error) => {
      console.error("Error fetching popular products:", error);
      console.log("Using sample products due to error");
    },
    // Use sample products if there's an error
    placeholderData: sampleProducts
  });

  // Preload critical images on component mount
  useEffect(() => {
    ImagePreloader.preloadCriticalImages().catch(console.error);
  }, []);

  // Calculate and track header height
  useEffect(() => {
    const updateHeaderHeight = () => {
      const mainHeader = document.getElementById("main-header");
      const height = mainHeader ? mainHeader.offsetHeight : 0;
      setHeaderHeight(height);
    };

    // Initial calculation
    updateHeaderHeight();

    // Recalculate on resize
    window.addEventListener('resize', updateHeaderHeight);

    // Check for header changes
    const observer = new MutationObserver(updateHeaderHeight);
    const mainHeader = document.getElementById("main-header");
    if (mainHeader) {
      observer.observe(mainHeader, { attributes: true, childList: true, subtree: true });
    }

    return () => {
      window.removeEventListener('resize', updateHeaderHeight);
      observer.disconnect();
    };
  }, []);

  // Effect for handling scroll events
  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (!ticking) {
        window.requestAnimationFrame(() => {
          const mainHeader = document.getElementById("main-header");
          const mainHeaderIsSticky = mainHeader && window.getComputedStyle(mainHeader).position === 'sticky';

          if (!categoryNavRef.current) return;

          const navOffsetTop = categoryNavRef.current.offsetTop;
          const scrollPosition = currentScrollY + (mainHeaderIsSticky ? headerHeight : 0);

          // Use a threshold for better control
          const threshold = 150;
          
          if (currentScrollY > threshold) {
            // Hide navigation bar (will be replaced by navigation items in header)
            if (categoryNavRef.current.style.display !== 'none') {
              categoryNavRef.current.style.display = 'none';
              setIsSticky(false);
            }
          } else {
            // Show navigation bar when scrolling back up
            if (categoryNavRef.current.style.display !== 'block') {
              categoryNavRef.current.style.display = 'block';
            }

            // Handle regular sticky behavior for smaller scroll positions
            const shouldBeSticky = scrollPosition > navOffsetTop;
            if (shouldBeSticky !== isSticky) {
              setIsSticky(shouldBeSticky);
            }
          }

          lastScrollY = currentScrollY;
          ticking = false;
        });

        ticking = true;
      }
    };

    // Throttled scroll handler
    let scrollTimeout: NodeJS.Timeout;
    const throttledScrollHandler = () => {
      if (!scrollTimeout) {
        scrollTimeout = setTimeout(() => {
          handleScroll();
          scrollTimeout = null;
        }, 16); // Approximately 60fps
      }
    };

    // Initial state
    handleScroll();
    
    window.addEventListener('scroll', throttledScrollHandler, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', throttledScrollHandler);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [headerHeight, isSticky]);

  // We've removed ScrollReveal to fix React hooks issues

  // Calculate the placeholder height
  const getPlaceholderHeight = () => {
    if (categoryNavRef.current) {
      return categoryNavRef.current.offsetHeight;
    }
    return 0;
  };

  return (
    <>
    {/* Hero Section */}
    <section className="relative h-[70vh] min-h-[500px] overflow-hidden" aria-label="Sustainable products showcase">
      <div className="absolute inset-0 z-0">
        {/* Background with image */}
        <div className="absolute inset-0">
          <img
            src="https://png.pngtree.com/background/20230527/original/pngtree-mobile-phone-with-a-shopping-cart-on-it-picture-image_2762466.jpg"
            alt="Ecommerce shopping with mobile phone"
            className="w-full h-full object-cover"
            style={{ filter: 'blur(5px)', transform: 'scale(1.03)' }}
          />
        </div>

        {/* Gradient overlay for better text contrast */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-black/30"></div>
      </div>

      <div className="container relative z-20 h-full flex items-center px-4">
        <div className="max-w-2xl text-white">
          <motion.div
            className="mb-6 flex items-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <RecycleIcon className="h-12 w-12 text-white mr-3" />

            <h1 className="text-4xl font-bold hero-title text-white">
              ReNewLife
            </h1>
          </motion.div>
          <motion.h2
            className="text-2xl md:text-3xl font-semibold mb-6 hero-subtitle"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Premium Refurbished & Recycled Goods
          </motion.h2>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Button
              asChild
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-6 text-lg hero-cta"
            >
              <Link href="/products">
                Shop Sustainable Goods
              </Link>
            </Button>
          </motion.div>
        </div>
      </div>
    </section>

    {/* Category Navigation - with sticky functionality and black border */}
    <section
      ref={categoryNavRef}
      className={`category-nav py-4 bg-white transition-all duration-300 ${
        isSticky ? 'fixed z-40 left-0 right-0 shadow-lg animate-slideDown' : ''
      }`}
      style={{
        top: isSticky ? `${headerHeight}px` : '0',
        borderBottom: '2px solid black',
        borderTop: '2px solid black',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      }}
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-center items-center">
          <div className="flex space-x-8 md:space-x-12">
            {/* Home link shifted slightly to the left */}
            <div className="flex items-center justify-end">
              <Link href="/" className="p-3 hover:bg-gray-50 hover:shadow-md rounded-lg transition-all duration-300 transform hover:-translate-y-1 border border-transparent hover:border-black">
                <span className="text-black font-semibold">Home</span>
              </Link>
            </div>

            {/* Other links shifted to the right */}
            <div className="flex items-center space-x-6 md:space-x-8">
              <Link href="/new-arrivals" className="p-3 hover:bg-gray-50 hover:shadow-md rounded-lg transition-all duration-300 transform hover:-translate-y-1 border border-transparent hover:border-black">
                <span className="text-black font-semibold">Recently Landed</span>
              </Link>
              <Link href="/popular-items" className="p-3 hover:bg-gray-50 hover:shadow-md rounded-lg transition-all duration-300 transform hover:-translate-y-1 border border-transparent hover:border-black">
                <span className="text-black font-semibold">Popular Products</span>
              </Link>
              <Link href="/on-sale" className="p-3 hover:bg-gray-50 hover:shadow-md rounded-lg transition-all duration-300 transform hover:-translate-y-1 border border-transparent hover:border-black">
                <span className="text-black font-semibold">On Sale</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>

    {/* Add a placeholder div when nav is sticky to prevent content jump */}
    {isSticky && (
      <div className="category-nav-placeholder" style={{ height: `${getPlaceholderHeight()}px` }}></div>
    )}

    {/* Categories Section */}
    <section className="py-10 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-end mb-8">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold font-poppins text-green-600 section-title">Shop By Category</h2>
            <p className="text-gray-600 mt-2 section-content">Explore our sustainable and recycled product categories</p>
          </div>
          <div>
            <Button variant="outline" asChild className="border-green-600 text-green-600 hover:bg-green-50 section-content">
              <Link href="/products">View All Categories</Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {categoriesLoading ? (
            // Loading skeleton for categories
            <>
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm overflow-hidden h-40 animate-pulse border border-gray-200">
                  <div className="bg-gray-200 h-28" />
                  <div className="p-3">
                    <div className="h-4 bg-gray-200 rounded w-3/5"></div>
                  </div>
                </div>
              ))}
            </>
          ) : (
            // Use categories from API or fallback
            (categories || fallbackCategories).map((category) => (
              <Link key={category.id} href={`/products?category=${category.id}`}>
                <div className="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg border border-gray-200 h-48 section-content">
                  <div className="h-36 overflow-hidden">
                    <img
                      src={category.imageUrl || '/placeholder-category.jpg'}
                      alt={category.name}
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                    />
                  </div>
                  <div className="p-3 bg-white text-center">
                    <h3 className="font-medium text-gray-900">{category.name}</h3>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      </div>
    </section>

    {/* Products Section - Changed background from blue to white */}
    <section className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-end mb-8">
          <div>
            <motion.h2
              className="text-2xl md:text-3xl font-bold font-poppins text-green-600 section-title"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              Recycled Products
            </motion.h2>
            <motion.p
              className="text-gray-600 mt-2 section-content"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Environmentally friendly products for a sustainable future
            </motion.p>
          </div>
          <motion.div
            className="hidden md:flex space-x-2 section-content"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
          >
            <Button variant="outline" size="icon" className="rounded-full text-green-600 border-green-600">
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button variant="outline" size="icon" className="rounded-full text-green-600 border-green-600">
              <ChevronRight className="h-5 w-5" />
            </Button>
          </motion.div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
          {productsLoading ? (
            // Loading skeleton
            <>
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={i}
                  className="bg-white rounded-lg shadow-sm overflow-hidden h-80 animate-pulse border border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: i * 0.1 }}
                >
                  <div className="bg-gray-200 h-48 sm:h-56" />
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-2/5 mb-2"></div>
                    <div className="h-5 bg-gray-200 rounded w-4/5 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/5"></div>
                  </div>
                </motion.div>
              ))}
            </>
          ) : popularProducts && popularProducts.length > 0 ? (
            // Use products from API
            popularProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                <ProductCard key={product.id} product={product} />
              </motion.div>
            ))
          ) : (
            // If no products from API, show sample products
            sampleProducts.map((product, index) => (
              <motion.div
                key={product.id}
                className="bg-white rounded-lg shadow-md overflow-hidden h-full border border-gray-200 transition-all duration-300 hover:shadow-lg product-card"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                <div className="relative aspect-square overflow-hidden">
                  <img
                    src={product.imageUrl}
                    alt={product.title}
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                  />
                  {product.discountPercentage > 0 && (
                    <motion.div
                      className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2 + index * 0.1 }}
                    >
                      -{product.discountPercentage}%
                    </motion.div>
                  )}
                  {product.isNew && (
                    <motion.div
                      className="absolute top-2 left-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                    >
                      RECENT
                    </motion.div>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-sm sm:text-base text-gray-900 mb-1">{product.title}</h3>
                  <div className="flex items-center gap-2 mb-2">
                    <motion.span
                      className="font-bold text-green-600"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                    >
                      ${product.price.toFixed(2)}
                    </motion.span>
                    {product.originalPrice > product.price && (
                      <motion.span
                        className="text-gray-400 text-sm line-through"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.5 + index * 0.1 }}
                      >
                        ${product.originalPrice.toFixed(2)}
                      </motion.span>
                    )}
                  </div>
                  <motion.div
                    className="flex items-center text-sm text-yellow-500"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                  >
                    {'★'.repeat(Math.floor(product.rating))}
                    {'☆'.repeat(5 - Math.floor(product.rating))}
                    <span className="text-gray-600 ml-1">{product.rating}</span>
                  </motion.div>
                </div>
              </motion.div>
            ))
          )}
        </div>

        <div className="text-center mt-8">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.4 }}
            whileHover={{ scale: 1.05 }}
          >
            <Button variant="outline" asChild className="border-green-600 text-green-600 hover:bg-green-50">
              <Link href="/popular-items">View All Recycled Products</Link>
            </Button>
          </motion.div>
        </div>
      </div>
    </section>

    {/* Features */}
    <section className="py-10 bg-gray-50 border-t border-gray-200">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
            whileHover={{ y: -5 }}
          >
            <FeatureCard
              icon={<Truck className="h-6 w-6 text-green-600" />}
              title="Eco-Friendly Delivery"
              description="Carbon-neutral shipping options"
            />
          </motion.div>

          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            whileHover={{ y: -5 }}
          >
            <FeatureCard
              icon={<RefreshCcw className="h-6 w-6 text-green-600" />}
              title="Circular Economy"
              description="Products that support recycling"
            />
          </motion.div>

          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            whileHover={{ y: -5 }}
          >
            <FeatureCard
              icon={<RecycleIcon className="h-6 w-6 text-green-600" />}
              title="Recycled Materials"
              description="Made from 100% recycled materials"
            />
          </motion.div>

          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            whileHover={{ y: -5 }}
          >
            <FeatureCard
              icon={<Headphones className="h-6 w-6 text-green-600" />}
              title="Sustainability Support"
              description="Expert advice on sustainable living"
            />
          </motion.div>
        </div>
      </div>
    </section>

    {/* Newsletter */}
    <section className="py-12 bg-green-600">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold font-poppins text-white mb-3 section-title">Join Our Green Community</h2>
          <p className="text-white/80 mb-6 section-content">Stay updated with recycling tips, sustainability news, and exclusive eco-friendly product deals</p>
          <form className="flex flex-col sm:flex-row gap-3 max-w-xl mx-auto section-content">
            <Input
              type="email"
              placeholder="Your email address"
              className="flex-1 rounded-md px-4 py-3 border-0"
            />
            <Button type="submit" className="bg-yellow-500 hover:bg-yellow-600 text-green-800">
              Subscribe
            </Button>
          </form>
          <p className="text-white/60 text-sm mt-4 section-content">By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.</p>
        </div>
      </div>
    </section>

    {/* Add animation styles for the category nav */}
    <style>{`
      @keyframes slideDown {
        from {
          transform: translateY(-100%);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      .animate-slideDown {
        animation: slideDown 0.3s ease-out forwards;
      }
    `}</style>
    </>
  );
}
