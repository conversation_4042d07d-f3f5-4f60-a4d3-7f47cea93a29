// Netlify Build Plugin to prepare the environment and handle database migrations
module.exports = {
  onPreBuild: async ({ utils }) => {
    try {
      console.log('Setting up environment for Netlify build...');
      
      // Ensure database connection and migrations are handled
      if (process.env.DATABASE_URL) {
        console.log('Database URL detected, preparing to run migrations...');
        await utils.run.command('node apply-migrations.js');
        console.log('Database migrations completed successfully');
      } else {
        console.warn('No DATABASE_URL found. Skipping migrations.');
      }
      
    } catch (error) {
      console.error('Error during pre-build setup:', error);
      // Don't fail the build if migrations fail
      // utils.build.failBuild('Failed to run migrations', { error });
    }
  },
};
