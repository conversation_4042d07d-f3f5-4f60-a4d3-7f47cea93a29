@echo off
echo Setting up environment and running server...

REM Check if tsx is installed globally
where tsx >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo tsx not found, installing globally...
  npm install -g tsx
)

REM Set environment variables
set NODE_TLS_REJECT_UNAUTHORIZED=0
set PGSSLMODE=no-verify
set PG_SSL_REJECT_UNAUTHORIZED=0
set VERCEL=true
set PORT=3000
set HOST=0.0.0.0

REM Run the server
echo Running server...
node run-server.js

pause
