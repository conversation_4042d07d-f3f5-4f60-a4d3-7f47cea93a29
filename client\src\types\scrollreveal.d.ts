declare module 'scrollreveal' {
  interface ScrollRevealOptions {
    reset?: boolean;
    origin?: string;
    distance?: string;
    duration?: number;
    delay?: number;
    interval?: number;
    rotate?: { x?: number; y?: number; z?: number };
    opacity?: number;
    scale?: number;
    easing?: string;
    mobile?: boolean;
    container?: Element;
    desktop?: boolean;
    useDelay?: string;
    viewFactor?: number;
    viewOffset?: { top?: number; right?: number; bottom?: number; left?: number };
    afterReveal?: (domEl: HTMLElement) => void;
    afterReset?: (domEl: HTMLElement) => void;
    beforeReveal?: (domEl: HTMLElement) => void;
    beforeReset?: (domEl: HTMLElement) => void;
    cleanUp?: () => void;
  }

  interface ScrollRevealObject {
    reveal(selector: string | Element | NodeList, options?: ScrollRevealOptions): ScrollRevealObject;
    reveal(selector: string | Element | NodeList, interval?: number): ScrollRevealObject;
    sync(): void;
    destroy(): void;
  }

  function scrollReveal(options?: ScrollRevealOptions): ScrollRevealObject;
  export default scrollReveal;
}