import { Link } from "wouter";
import { Facebook, Twitter, Instagram, MapPin, Phone, Mail, Clock, RecycleIcon } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-amber-100 text-gray-700 pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
  <Link href="/" className="font-bold font-poppins flex items-center">
    <div className="bg-[#8bbe1b] rounded-full w-10 h-10 flex items-center justify-center shadow-md">
      <RecycleIcon className="text-white w-6 h-6" />
    </div>
    <div className="ml-3">
      <div className="flex items-center">
        <span className="text-3xl font-bold font-poppins text-[#8bbe1b]">W</span>
        <span className="text-3xl font-bold font-poppins text-gray-800 mx-0.5">2</span>
        <span className="text-3xl font-bold font-poppins text-[#8bbe1b]">W</span>
      </div>
      <p className="text-sm font-semibold text-gray-700 tracking-wide text-center">
        WASTE TO WEALTH
      </p>
    </div>
  </Link>
            
            <p className="text-gray-600 mt-4">
              Welcome to W2W - Your premier destination for sustainable shopping. We specialize in high-quality recycled and refurbished products, turning waste into valuable treasures while promoting environmental consciousness and circular economy.
            </p>
            <div className="flex space-x-4 mt-6">
              <a href="#" className="w-8 h-8 rounded-full bg-[#8bbe1b]/10 flex items-center justify-center text-[#8bbe1b] hover:bg-[#8bbe1b] hover:text-white transition-all duration-300">
                <Facebook className="w-4 h-4" />
              </a>
              <a href="#" className="w-8 h-8 rounded-full bg-[#8bbe1b]/10 flex items-center justify-center text-[#8bbe1b] hover:bg-[#8bbe1b] hover:text-white transition-all duration-300">
                <Twitter className="w-4 h-4" />
              </a>
              <a href="#" className="w-8 h-8 rounded-full bg-[#8bbe1b]/10 flex items-center justify-center text-[#8bbe1b] hover:bg-[#8bbe1b] hover:text-white transition-all duration-300">
                <Instagram className="w-4 h-4" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium font-poppins mb-4 text-[#6a9816]">Our Services</h3>
            <ul className="space-y-2">
              <li><Link href="/recycling-guide" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Recycling Guide</Link></li>
              <li><Link href="/dropoff-locations" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Drop-off Locations</Link></li>
              <li><Link href="/trade-in" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Trade-in Program</Link></li>
              <li><Link href="/repair-services" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Repair Services</Link></li>
              <li><Link href="/sustainability" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Sustainability Tips</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-medium font-poppins mb-4 text-[#6a9816]">W2W Categories</h3>
            <ul className="space-y-2">
              <li><Link href="/new-arrivals" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Recently Landed</Link></li>
              <li><Link href="/popular-items" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Popular W2W Items</Link></li>
              <li><Link href="/on-sale" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">W2W Deals</Link></li>
              <li><Link href="/wishlist" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">Saved W2W Items</Link></li>
              <li><Link href="/profile" className="text-gray-600 hover:text-[#8bbe1b] transition-colors duration-300">My W2W Orders</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-medium font-poppins mb-4 text-[#6a9816]">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="w-5 h-5 mt-1 mr-3 text-[#8bbe1b]" />
                <span className="text-gray-600">
                  12th floor, Excelling Geo & Engineering Consultant Pvt. Ltd., Building A Bussiness coworking, Vatika Mindscapes, Sector 27D, Faridabad, Haryana
                </span>
              </li>
              <li className="flex items-center">
                <Phone className="w-5 h-5 mr-3 text-[#8bbe1b]" />
                <span className="text-gray-600">+91 9519564623</span>
              </li>
              <li className="flex items-center">
                <Mail className="w-5 h-5 mr-3 text-[#8bbe1b]" />
                <span className="text-gray-600"><EMAIL></span>
              </li>
              <li className="flex items-center">
                <Clock className="w-5 h-5 mr-3 text-[#8bbe1b]" />
                <span className="text-gray-600">Monday-Friday: 9AM - 6PM</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-amber-200 mt-8 pt-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm">&copy; {new Date().getFullYear()} Waste to Wealth (W2W). All rights reserved.</p>
            <div className="mt-4 md:mt-0 flex space-x-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                <rect width="40" height="24" rx="4" fill="#1A1F71"/>
                <path d="M16.9 7H23.1L20 17H13.8L16.9 7Z" fill="#FFFFFF"/>
                <path d="M26.2 7C25.2 7 24.3 7.6 24 8.5L20 17H24.5L25 15.5H29L29.3 17H33.5L31 7H26.2ZM26 13L27 10L28 13H26Z" fill="#FFFFFF"/>
                <path d="M11.8 7L8.6 14L8.2 12.5L8.2 12.5L7 8C6.9 7.4 6.4 7 5.8 7H0.2L0.1 7.2C1.1 7.4 2.3 7.9 3 8.2L6.5 17H11L16.5 7H11.8Z" fill="#FFFFFF"/>
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                <rect width="40" height="24" rx="4" fill="#252525"/>
                <path d="M25.5 7C23.6 7 22 7.7 22 9.9C22 11.9 24.8 12.2 24.8 13.4C24.8 13.9 24.2 14.3 23.2 14.3C21.8 14.3 21 13.8 21 13.8L20.5 16.3C20.5 16.3 21.5 17 23.2 17C25.5 17 27.5 15.9 27.5 13.6C27.5 11.4 24.7 11 24.7 10C24.7 9.6 25.1 9.2 26.1 9.2C27.1 9.2 28 9.7 28 9.7L28.5 7.3C28.5 7.3 27.7 7 25.5 7Z" fill="#FFFFFF"/>
                <path d="M34.7 7.1H32.2C31.5 7.1 31 7.3 30.7 8L27 17H29.7L30.2 15.5H33.5L33.8 17H36.2L34.7 7.1ZM30.7 13.5L32 9.5L32.8 13.5H30.7Z" fill="#FFFFFF"/>
                <path d="M20.4 7.1L18.2 17H20.8L23 7.1H20.4Z" fill="#FFFFFF"/>
                <path d="M16.9 7.1L14.2 13.9L13.4 8.4C13.3 7.6 12.7 7.1 12 7.1H7.2L7.1 7.3C8.1 7.5 9.1 7.9 9.9 8.3L12.2 17H15L19.7 7.1H16.9Z" fill="#FFFFFF"/>
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                <rect width="40" height="24" rx="4" fill="#003087"/>
                <path d="M17.1 9.8H14.9C14.8 9.8 14.6 9.9 14.6 10L13.5 16.4C13.5 16.5 13.6 16.6 13.7 16.6H14.8C14.9 16.6 15.1 16.5 15.1 16.4L15.4 14.7C15.4 14.6 15.5 14.5 15.7 14.5H16.4C18 14.5 19 13.6 19.3 12.1C19.4 11.5 19.3 10.9 19 10.5C18.6 10.1 18 9.8 17.1 9.8ZM17.4 12.1C17.2 13.0 16.5 13.0 15.8 13.0H15.5L15.8 11.4C15.8 11.3 15.9 11.3 16 11.3H16.1C16.6 11.3 17 11.3 17.2 11.6C17.4 11.7 17.4 11.9 17.4 12.1Z" fill="#FFFFFF"/>
                <path d="M24.8 12H23.6C23.5 12 23.4 12.1 23.4 12.1L23.3 12.3L23.2 12.1C23 11.7 22.4 11.6 21.8 11.6C20.5 11.6 19.4 12.5 19.1 13.9C19 14.5 19.1 15.1 19.4 15.6C19.7 16 20.1 16.2 20.7 16.2C21.7 16.2 22.3 15.6 22.3 15.6L22.2 15.8C22.2 15.9 22.3 16 22.4 16H23.5C23.6 16 23.8 15.9 23.8 15.8L24.5 12.2C24.5 12.1 24.4 12 24.8 12ZM22.6 13.9C22.5 14.6 21.9 15.1 21.2 15.1C20.9 15.1 20.6 15 20.5 14.8C20.4 14.6 20.3 14.3 20.4 14C20.5 13.3 21.1 12.8 21.8 12.8C22.1 12.8 22.3 12.9 22.5 13.1C22.6 13.3 22.6 13.6 22.6 13.9Z" fill="#FFFFFF"/>
                <path d="M30.2 12H29C28.9 12 28.8 12 28.7 12.1L26.6 13.6L25.9 12.2C25.8 12.1 25.7 12 25.6 12H24.4C24.3 12 24.2 12.1 24.2 12.2L25.5 15.9L24.3 16.8C24.2 16.9 24.2 17 24.3 17.1C24.3 17.1 24.4 17.2 24.5 17.2H25.7C25.8 17.2 25.9 17.1 26 17.1L30.3 12.3C30.3 12.2 30.3 12.1 30.2 12Z" fill="#FFFFFF"/>
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="24" viewBox="0 0 40 24" fill="none">
                <rect width="40" height="24" rx="4" fill="#FFB600"/>
                <path d="M20 16C22.2091 16 24 14.2091 24 12C24 9.79086 22.2091 8 20 8C17.7909 8 16 9.79086 16 12C16 14.2091 17.7909 16 20 16Z" fill="#FF5F00"/>
                <path d="M19.2 12C19.2 10.8 19.8 9.6 20.7 8.9C19.9 8.3 18.8 8 17.7 8C15.1 8 13 9.8 13 12C13 14.2 15.1 16 17.7 16C18.8 16 19.9 15.7 20.7 15.1C19.8 14.4 19.2 13.2 19.2 12Z" fill="#EB001B"/>
                <path d="M27 12C27 14.2 24.9 16 22.3 16C21.2 16 20.1 15.7 19.3 15.1C20.3 14.4 20.8 13.2 20.8 12C20.8 10.8 20.2 9.6 19.3 8.9C20.1 8.3 21.2 8 22.3 8C24.9 8 27 9.8 27 12Z" fill="#F79E1B"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
