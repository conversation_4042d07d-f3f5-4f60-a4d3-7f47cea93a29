// Script to update the database with specific products and categories
import dotenv from 'dotenv';
import pkg from 'postgres';
const postgres = pkg;

// Load environment variables
dotenv.config();

// Set environment variables for database connection
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
process.env.PGSSLMODE = 'no-verify';
process.env.PG_SSL_REJECT_UNAUTHORIZED = '0';

// The exact categories and products from database-storage.ts
const categories = [
  {
    name: "Recycled Electronics",
    imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    name: "Upcycled Fashion",
    imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    name: "Eco-friendly Home",
    imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    name: "Recycled Accessories",
    imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  }
];

const products = [
  {
    name: "Refurbished Smartphone",
    description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
    price: 329.99,
    discountPrice: null,
    rating: 4.5,
    reviewCount: 34,
    imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    categoryId: 1, // Recycled Electronics
    inStock: true,
    isNew: true,
    isPopular: false,
    isSale: false
  },
  {
    name: "Recycled Plastic Watch",
    description: "Stylish watch made from ocean plastic. Water-resistant and eco-conscious design.",
    price: 79.99,
    discountPrice: null,
    rating: 4.1,
    reviewCount: 27,
    imageUrl: "https://images.pexels.com/photos/3908800/pexels-photo-3908800.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    categoryId: 4, // Recycled Accessories
    inStock: true,
    isNew: true,
    isPopular: false,
    isSale: false
  },
  {
    name: "Sustainable Bamboo Toothbrush",
    description: "Eco-friendly bamboo toothbrush with biodegradable handle. Reduces plastic waste.",
    price: 6.99,
    discountPrice: null,
    rating: 4.6,
    reviewCount: 42,
    imageUrl: "https://images.pexels.com/photos/3737593/pexels-photo-3737593.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    categoryId: 3, // Eco-friendly Home
    inStock: true,
    isNew: true,
    isPopular: false,
    isSale: false
  },
  {
    name: "Upcycled Denim Backpack",
    description: "Handcrafted backpack made from recycled denim jeans. Each piece is unique.",
    price: 59.99,
    discountPrice: null,
    rating: 4.8,
    reviewCount: 56,
    imageUrl: "https://images.pexels.com/photos/6567059/pexels-photo-6567059.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    categoryId: 2, // Upcycled Fashion
    inStock: true,
    isNew: false,
    isPopular: true,
    isSale: false
  },
  {
    name: "Vintage Console",
    description: "Refurbished gaming console from the 90s. Includes classic games and accessories.",
    price: 149.99,
    discountPrice: 129.99,
    rating: 4.7,
    reviewCount: 32,
    imageUrl: "https://images.pexels.com/photos/3945657/pexels-photo-3945657.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    categoryId: 1, // Recycled Electronics
    inStock: true,
    isNew: false,
    isPopular: false,
    isSale: true
  }
];

async function updateDatabase() {
  console.log('Starting database update process...');
  
  try {
    // Get connection string
    const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('No database connection string found');
    }
    
    console.log('Connecting to database...');
    
    // Configure connection
    const sql = postgres(connectionString, {
      ssl: { rejectUnauthorized: false },
      max: 3,
      idle_timeout: 20,
      connect_timeout: 10
    });
    
    // Test connection
    const testResult = await sql`SELECT 1 as test`;
    console.log('Database connection successful:', testResult[0].test === 1);
    
    // Clear existing data
    console.log('Clearing existing data...');
    try {
      // Delete in the correct order to avoid foreign key constraints
      await sql`TRUNCATE TABLE "wishlist_items" CASCADE`;
      await sql`TRUNCATE TABLE "cart_items" CASCADE`;
      await sql`TRUNCATE TABLE "order_items" CASCADE`;
      await sql`TRUNCATE TABLE "orders" CASCADE`;
      await sql`TRUNCATE TABLE "products" CASCADE`;
      await sql`TRUNCATE TABLE "categories" CASCADE`;
      
      console.log('Existing data cleared successfully');
    } catch (clearError) {
      console.error('Error clearing existing data:', clearError);
      console.log('Attempting to continue with the update...');
    }
    
    // Reset sequences
    try {
      await sql`ALTER SEQUENCE categories_id_seq RESTART WITH 1`;
      await sql`ALTER SEQUENCE products_id_seq RESTART WITH 1`;
      console.log('Sequences reset successfully');
    } catch (seqError) {
      console.error('Error resetting sequences:', seqError);
    }
    
    // Add categories
    console.log('Adding categories...');
    for (const category of categories) {
      try {
        await sql`
          INSERT INTO categories (name, image_url)
          VALUES (${category.name}, ${category.imageUrl})
        `;
      } catch (categoryError) {
        console.error(`Error adding category "${category.name}":`, categoryError);
      }
    }
    
    // Verify categories were added
    const addedCategories = await sql`SELECT * FROM categories ORDER BY id`;
    console.log(`Added ${addedCategories.length} categories:`);
    for (const category of addedCategories) {
      console.log(`- ID ${category.id}: ${category.name}`);
    }
    
    // Add products
    console.log('\nAdding products...');
    for (const product of products) {
      try {
        await sql`
          INSERT INTO products (
            name, description, price, discount_price, rating,
            review_count, image_url, category_id, in_stock,
            is_new, is_popular, is_sale
          )
          VALUES (
            ${product.name}, ${product.description}, ${product.price},
            ${product.discountPrice}, ${product.rating}, ${product.reviewCount},
            ${product.imageUrl}, ${product.categoryId}, ${product.inStock},
            ${product.isNew}, ${product.isPopular}, ${product.isSale}
          )
        `;
        console.log(`- Added product: ${product.name}`);
      } catch (productError) {
        console.error(`Error adding product "${product.name}":`, productError);
      }
    }
    
    // Verify products were added
    const addedProducts = await sql`SELECT * FROM products ORDER BY id`;
    console.log(`\nAdded ${addedProducts.length} products in total`);
    
    // Close the database connection
    await sql.end();
    console.log('Database connection closed');
    
    console.log('\nDatabase update completed successfully!');
    return true;
  } catch (error) {
    console.error('Error updating database:', error);
    return false;
  }
}

// Run the update function
updateDatabase()
  .then(success => {
    if (success) {
      console.log('Database update completed successfully');
      process.exit(0);
    } else {
      console.error('Database update failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error during database update:', error);
    process.exit(1);
  });
