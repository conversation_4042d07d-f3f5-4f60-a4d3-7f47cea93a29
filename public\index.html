<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ecommerce App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .links {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }
        .links a {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        .links a:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>Welcome to the Ecommerce App</h1>
        <p class="success">✅ Server is running correctly!</p>
        <p>This is a temporary page to help you navigate to different parts of the application.</p>
        
        <div class="links">
            <a href="/test">Test API Endpoint</a>
            <a href="/api/categories">View Categories (JSON)</a>
            <a href="/api/products">View Products (JSON)</a>
        </div>
    </div>
    
    <div class="card">
        <h2>Server Status</h2>
        <p>The server is running on port 3000 and is connected to the database.</p>
        <p>You can access the API endpoints at:</p>
        <ul>
            <li><code>/api/categories</code> - Get all categories</li>
            <li><code>/api/products</code> - Get all products</li>
            <li><code>/api/user</code> - Get user information (requires authentication)</li>
        </ul>
    </div>
</body>
</html>
