import { useState, useEffect } from "react";
import { useLocation, useSearch } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { ProductCard } from "@/components/ui/product-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Layout } from "@/components/layout/layout";
import {
  Search,
  Filter,
  X,
  ChevronRight,
  Grid2X2,
  LayoutList
} from "lucide-react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationEllipsis } from "@/components/ui/pagination";

// Parse search params helper
function parseSearchParams(search: string) {
  const params = new URLSearchParams(search);
  return {
    category: params.get("category"),
    search: params.get("search"),
    isNew: params.get("isNew") === "true",
    isPopular: params.get("isPopular") === "true",
    isSale: params.get("isSale") === "true",
    minPrice: params.get("minPrice") ? Number(params.get("minPrice")) : undefined,
    maxPrice: params.get("maxPrice") ? Number(params.get("maxPrice")) : undefined,
    sort: params.get("sort") || "newest",
    page: params.get("page") ? Number(params.get("page")) : 1,
  };
}

export default function ProductPage() {
  const [location, setLocation] = useLocation();
  const search = useSearch();
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  const [isGridView, setIsGridView] = useState(true);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 500]);
  const [searchQuery, setSearchQuery] = useState("");

  // Parse search params from URL
  const {
    category,
    search: searchParam,
    isNew,
    isPopular,
    isSale,
    minPrice,
    maxPrice,
    sort,
    page
  } = parseSearchParams(search);

  // Set initial search query from URL
  useEffect(() => {
    if (searchParam) {
      setSearchQuery(searchParam);
    }
  }, [searchParam]);

  // Set initial price range from URL
  useEffect(() => {
    setPriceRange([minPrice || 0, maxPrice || 500]);
  }, [minPrice, maxPrice]);

  // Fallback categories in case API fails
  const fallbackCategories = [
    {
      id: 629,
      name: "Recycled Electronics",
      imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg"
    },
    {
      id: 630,
      name: "Upcycled Fashion",
      imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg"
    },
    {
      id: 631,
      name: "Eco-friendly Home",
      imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg"
    },
    {
      id: 632,
      name: "Recycled Accessories",
      imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg"
    }
  ];

  // Fetch categories
  const { data: categories, error: categoriesError } = useQuery({
    queryKey: ["/api/categories"],
    retry: 3,
    staleTime: 0,
    onError: (error) => {
      console.error("Error fetching categories:", error);
    }
  });

  // Get current category name from categories or fallback categories
  const allCategories = categories || fallbackCategories;
  const currentCategory = allCategories.find(c => c.id === Number(category))?.name || "All Products";

  // Fallback products in case API fails
  const fallbackProducts = [
    {
      id: 741,
      name: "Refurbished Smartphone",
      description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
      price: 329.99,
      discountPrice: 299.99,
      rating: 4.5,
      reviewCount: 34,
      imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
      categoryId: 629,
      inStock: true,
      isNew: true,
      isPopular: false,
      isSale: true
    },
    {
      id: 742,
      name: "Upcycled Denim Jacket",
      description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
      price: 89.99,
      discountPrice: null,
      rating: 4.8,
      reviewCount: 42,
      imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
      categoryId: 630,
      inStock: true,
      isNew: true,
      isPopular: true,
      isSale: false
    },
    {
      id: 743,
      name: "Recycled Metal Water Bottle",
      description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
      price: 29.99,
      discountPrice: 24.99,
      rating: 4.9,
      reviewCount: 120,
      imageUrl: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg",
      categoryId: 632,
      inStock: true,
      isNew: false,
      isPopular: true,
      isSale: true
    },
    {
      id: 744,
      name: "Bamboo Desk Organizer",
      description: "Sustainable bamboo desk organizer with multiple compartments for all your office essentials.",
      price: 49.99,
      discountPrice: null,
      rating: 4.6,
      reviewCount: 28,
      imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg",
      categoryId: 631,
      inStock: true,
      isNew: false,
      isPopular: false,
      isSale: false
    }
  ];

  // Fetch products with filters
  const { data: products, isLoading, error: productsError } = useQuery({
    queryKey: ["/api/products", {
      category: category ? Number(category) : undefined,
      search: searchParam,
      minPrice: priceRange[0],
      maxPrice: priceRange[1]
    }],
    staleTime: 3 * 60 * 1000, // 3 minutes - products can be cached for a reasonable time
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    queryFn: async () => {
      const params = new URLSearchParams();
      if (category) params.set('category', category);
      if (searchParam) params.set('search', searchParam);
      params.set('minPrice', priceRange[0].toString());
      params.set('maxPrice', priceRange[1].toString());

      const response = await fetch(`/api/products?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
      }
      return response.json();
    },
    onError: (error) => {
      console.error("Error fetching products:", error);
    }
  });

  // Apply search filter
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    const params = new URLSearchParams(search);
    if (searchQuery) {
      params.set("search", searchQuery);
    } else {
      params.delete("search");
    }
    params.set("page", "1"); // Reset to first page on new search

    setLocation(`/products?${params.toString()}`);
  };

  // Apply price filter
  const handlePriceFilter = () => {
    const params = new URLSearchParams(search);
    params.set("minPrice", priceRange[0].toString());
    params.set("maxPrice", priceRange[1].toString());
    params.set("page", "1"); // Reset to first page on filter change

    setLocation(`/products?${params.toString()}`);
  };

  // Apply sort
  const handleSort = (value: string) => {
    const params = new URLSearchParams(search);
    params.set("sort", value);

    setLocation(`/products?${params.toString()}`);
  };

  // Filter by a category
  const handleCategoryFilter = (categoryId: string) => {
    const params = new URLSearchParams(search);
    params.set("category", categoryId);
    params.set("page", "1"); // Reset to first page on filter change

    setLocation(`/products?${params.toString()}`);
  };

  // Toggle filter
  const toggleFilter = (filter: string, value: boolean) => {
    const params = new URLSearchParams(search);

    if (value) {
      params.set(filter, "true");
    } else {
      params.delete(filter);
    }

    // Reset to first page on filter change
    params.set("page", "1");

    setLocation(`/products?${params.toString()}`);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setLocation("/products");
    setPriceRange([0, 500]);
    setSearchQuery("");
  };

  // Sort products based on the sort parameter
  // Use fallback products if API fails
  const sortedProducts = products ? [...products] : [...fallbackProducts];
  if (sort === "price-asc") {
    sortedProducts.sort((a, b) => {
      const priceA = a.discountPrice || a.price;
      const priceB = b.discountPrice || b.price;
      return priceA - priceB;
    });
  } else if (sort === "price-desc") {
    sortedProducts.sort((a, b) => {
      const priceA = a.discountPrice || a.price;
      const priceB = b.discountPrice || b.price;
      return priceB - priceA;
    });
  } else if (sort === "rating") {
    sortedProducts.sort((a, b) => b.rating - a.rating);
  }

  // Pagination
  const itemsPerPage = 12;
  const totalPages = Math.ceil((sortedProducts?.length || 0) / itemsPerPage);
  const currentPage = page || 1;

  const paginatedProducts = sortedProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle page change
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(search);
    params.set("page", newPage.toString());

    setLocation(`/products?${params.toString()}`);
    window.scrollTo(0, 0);
  };

  return (
    <Layout>
      <main className="bg-background">
        {/* Breadcrumb */}
        <div className="bg-white border-y border-gray-200">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center text-sm">
              <a href="/" className="text-gray-500 hover:text-secondary">Home</a>
              <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
              <a href="/products" className={`${category ? 'text-gray-500 hover:text-secondary' : 'text-primary font-medium'}`}>All Products</a>
              {category && (
                <>
                  <ChevronRight className="h-3 w-3 mx-2 text-gray-400" />
                  <span className="text-primary font-medium">{currentCategory}</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start mb-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold font-poppins text-primary">
                {currentCategory}
              </h1>
              <p className="text-gray-500 mt-1">
                {isLoading
                  ? "Loading products..."
                  : `Showing ${paginatedProducts.length} of ${products?.length || 0} products`
                }
              </p>
            </div>

            <div className="flex items-center space-x-3 mt-4 md:mt-0">
              <Button
                variant="outline"
                size="sm"
                className="md:hidden flex items-center"
                onClick={() => setIsMobileFilterOpen(true)}
              >
                <Filter className="w-4 h-4 mr-2" /> Filters
              </Button>

              <div className="border rounded-md flex">
                <Button
                  variant={isGridView ? "default" : "ghost"}
                  size="sm"
                  className="rounded-none border-0"
                  onClick={() => setIsGridView(true)}
                >
                  <Grid2X2 className="w-4 h-4" />
                </Button>
                <Button
                  variant={!isGridView ? "default" : "ghost"}
                  size="sm"
                  className="rounded-none border-0"
                  onClick={() => setIsGridView(false)}
                >
                  <LayoutList className="w-4 h-4" />
                </Button>
              </div>

              <Select defaultValue={sort} onValueChange={handleSort}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest</SelectItem>
                  <SelectItem value="price-asc">Price: Low to High</SelectItem>
                  <SelectItem value="price-desc">Price: High to Low</SelectItem>
                  <SelectItem value="rating">Best Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Desktop Filters Sidebar */}
            <div className="hidden md:block w-64 shrink-0">
              <div className="bg-white rounded-lg shadow-sm p-5">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-medium text-lg">Filters</h3>
                  {(category || isNew || isPopular || isSale || minPrice || maxPrice) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearAllFilters}
                      className="text-sm text-gray-500 hover:text-secondary"
                    >
                      Clear All
                    </Button>
                  )}
                </div>

                <form onSubmit={handleSearch} className="mb-6">
                  <div className="relative">
                    <Input
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pr-10"
                    />
                    <Button
                      type="submit"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 text-gray-500"
                    >
                      <Search className="w-4 h-4" />
                    </Button>
                  </div>
                </form>

                <Accordion type="multiple" defaultValue={["categories", "price", "status"]} className="space-y-4">
                  <AccordionItem value="categories" className="border-b-0">
                    <AccordionTrigger className="py-3 text-base font-medium">
                      Categories
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2">
                        {(categories || fallbackCategories).map((cat) => (
                          <div key={cat.id} className="flex items-center">
                            <Button
                              variant="ghost"
                              className={`w-full justify-start px-2 py-1 text-sm ${Number(category) === cat.id ? 'text-secondary font-medium' : 'text-gray-700'}`}
                              onClick={() => handleCategoryFilter(cat.id.toString())}
                            >
                              {cat.name}
                            </Button>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="price" className="border-b-0">
                    <AccordionTrigger className="py-3 text-base font-medium">
                      Price Range
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4">
                        <Slider
                          value={priceRange}
                          min={0}
                          max={500}
                          step={5}
                          onValueChange={(value: [number, number]) => setPriceRange(value)}
                        />
                        <div className="flex items-center justify-between">
                          <span className="text-sm">${priceRange[0]}</span>
                          <span className="text-sm">${priceRange[1]}</span>
                        </div>
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={handlePriceFilter}
                        >
                          Apply
                        </Button>
                      </div>
                    </AccordionContent>
                  </AccordionItem>


                </Accordion>
              </div>
            </div>

            {/* Mobile Filters Sidebar */}
            {isMobileFilterOpen && (
              <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex md:hidden">
                <div className="bg-white h-full w-4/5 max-w-xs overflow-y-auto">
                  <div className="flex justify-between items-center p-4 border-b">
                    <h2 className="font-poppins font-bold text-lg">Filters</h2>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setIsMobileFilterOpen(false)}
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  <div className="p-4">
                    <form onSubmit={handleSearch} className="mb-6">
                      <div className="relative">
                        <Input
                          type="text"
                          placeholder="Search products..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pr-10"
                        />
                        <Button
                          type="submit"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 text-gray-500"
                        >
                          <Search className="w-4 h-4" />
                        </Button>
                      </div>
                    </form>

                    <Accordion type="multiple" defaultValue={["categories", "price", "status"]} className="space-y-4">
                      <AccordionItem value="categories" className="border-b-0">
                        <AccordionTrigger className="py-3 text-base font-medium">
                          Categories
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            {(categories || fallbackCategories).map((cat) => (
                              <div key={cat.id} className="flex items-center">
                                <Button
                                  variant="ghost"
                                  className={`w-full justify-start px-2 py-1 text-sm ${Number(category) === cat.id ? 'text-secondary font-medium' : 'text-gray-700'}`}
                                  onClick={() => {
                                    handleCategoryFilter(cat.id.toString());
                                    setIsMobileFilterOpen(false);
                                  }}
                                >
                                  {cat.name}
                                </Button>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="price" className="border-b-0">
                        <AccordionTrigger className="py-3 text-base font-medium">
                          Price Range
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4">
                            <Slider
                              value={priceRange}
                              min={0}
                              max={500}
                              step={5}
                              onValueChange={(value: [number, number]) => setPriceRange(value)}
                            />
                            <div className="flex items-center justify-between">
                              <span className="text-sm">${priceRange[0]}</span>
                              <span className="text-sm">${priceRange[1]}</span>
                            </div>
                            <Button
                              size="sm"
                              className="w-full"
                              onClick={() => {
                                handlePriceFilter();
                                setIsMobileFilterOpen(false);
                              }}
                            >
                              Apply
                            </Button>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="status" className="border-b-0">
                        <AccordionTrigger className="py-3 text-base font-medium">
                          Product Status
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="mobile-new"
                                checked={isNew}
                                onCheckedChange={(checked) => {
                                  toggleFilter("isNew", !!checked);
                                  setIsMobileFilterOpen(false);
                                }}
                              />
                              <label htmlFor="mobile-new" className="text-sm font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                New Arrivals
                              </label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="mobile-sale"
                                checked={isSale}
                                onCheckedChange={(checked) => {
                                  toggleFilter("isSale", !!checked);
                                  setIsMobileFilterOpen(false);
                                }}
                              />
                              <label htmlFor="mobile-sale" className="text-sm font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                On Sale
                              </label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="mobile-popular"
                                checked={isPopular}
                                onCheckedChange={(checked) => {
                                  toggleFilter("isPopular", !!checked);
                                  setIsMobileFilterOpen(false);
                                }}
                              />
                              <label htmlFor="mobile-popular" className="text-sm font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                Popular Items
                              </label>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <div className="mt-6 grid grid-cols-2 gap-3">
                      <Button
                        variant="outline"
                        onClick={() => {
                          clearAllFilters();
                          setIsMobileFilterOpen(false);
                        }}
                      >
                        Clear All
                      </Button>
                      <Button onClick={() => setIsMobileFilterOpen(false)}>
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Products Grid */}
            <div className="flex-1">
              {isLoading ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="bg-white rounded-lg shadow-sm overflow-hidden h-80 animate-pulse">
                      <div className="bg-gray-200 h-48 sm:h-56" />
                      <div className="p-4">
                        <div className="h-4 bg-gray-200 rounded w-2/5 mb-2"></div>
                        <div className="h-5 bg-gray-200 rounded w-4/5 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/5"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : paginatedProducts.length === 0 ? (
                <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                  <div className="max-w-md mx-auto">
                    <h3 className="text-xl font-medium text-primary mb-2">No products found</h3>
                    <p className="text-gray-500 mb-6">
                      We couldn't find any products matching your current filters.
                    </p>
                    <Button onClick={clearAllFilters}>Clear All Filters</Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className={isGridView
                    ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6"
                    : "space-y-4"
                  }>
                    {isGridView ? (
                      paginatedProducts.map((product) => (
                        <ProductCard key={product.id} product={product} />
                      ))
                    ) : (
                      paginatedProducts.map((product) => (
                        <div key={product.id} className="bg-white rounded-lg shadow-sm overflow-hidden product-card transition duration-300 flex">
                          <div className="w-1/3">
                            <img
                              src={product.imageUrl}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="p-4 flex-1">
                            <a href="#" className="text-xs text-gray-500 hover:text-secondary transition">
                              {product.categoryId === 1 ? "Men's Fashion" :
                              product.categoryId === 2 ? "Women's Fashion" :
                              product.categoryId === 3 ? "Electronics" :
                              product.categoryId === 4 ? "Home & Decor" : "Other"}
                            </a>
                            <a href={`/products/${product.id}`}>
                              <h3 className="font-medium font-poppins text-primary mt-1 hover:text-secondary transition">
                                {product.name}
                              </h3>
                            </a>
                            <p className="text-sm text-gray-500 mt-1 line-clamp-2">{product.description}</p>
                            <div className="flex items-center mt-2">
                              <div className="flex text-accent text-sm">
                                {[...Array(5)].map((_, i) => (
                                  <svg
                                    key={i}
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill={i < Math.floor(product.rating) ? "currentColor" : "none"}
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className={i < Math.floor(product.rating) ? "" : "text-gray-300"}
                                  >
                                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                  </svg>
                                ))}
                              </div>
                              <span className="text-xs text-gray-500 ml-1">({product.reviewCount})</span>
                            </div>
                            <div className="flex justify-between items-center mt-4">
                              <div>
                                <span className="font-medium text-primary">${product.discountPrice?.toFixed(2) || product.price.toFixed(2)}</span>
                                {product.discountPrice && (
                                  <span className="text-sm text-gray-500 line-through ml-1">${product.price.toFixed(2)}</span>
                                )}
                              </div>
                              <Button size="sm" variant="secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                  <circle cx="9" cy="21" r="1"></circle>
                                  <circle cx="20" cy="21" r="1"></circle>
                                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                                </svg>
                                Add to Cart
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <Pagination className="mt-8">
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationLink
                            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                          >
                            Previous
                          </PaginationLink>
                        </PaginationItem>

                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          // Logic to display page numbers around current page
                          let pageNum = i + 1;
                          if (totalPages > 5) {
                            if (currentPage <= 3) {
                              // Show first 5 pages
                              pageNum = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              // Show last 5 pages
                              pageNum = totalPages - 4 + i;
                            } else {
                              // Show 2 pages before and after current page
                              pageNum = currentPage - 2 + i;
                            }
                          }

                          return (
                            <PaginationItem key={pageNum}>
                              <PaginationLink
                                isActive={currentPage === pageNum}
                                onClick={() => handlePageChange(pageNum)}
                              >
                                {pageNum}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        })}

                        {totalPages > 5 && currentPage < totalPages - 2 && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}

                        <PaginationItem>
                          <PaginationLink
                            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                          >
                            Next
                          </PaginationLink>
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </main>
    </Layout>
  );
}
