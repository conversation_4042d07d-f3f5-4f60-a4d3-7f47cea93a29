// Debug information endpoint
import { createClient } from '@supabase/supabase-js';
import pkg from 'pg';
const { Pool } = pkg;

export const handler = async (event, context) => {
  let databaseStatus = 'unknown';
  let errorMessage = null;
  let supabaseStatus = 'unknown';
  let supabaseError = null;
  
  // Test PostgreSQL connection
  try {
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false, // Required for Supabase connections with self-signed certs
      },
      // Set shorter timeouts for quick diagnostics
      connectionTimeoutMillis: 5000,
    });
    
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    await client.release();
    
    databaseStatus = 'connected';
  } catch (error) {
    databaseStatus = 'error';
    errorMessage = error.message;
    console.error('Database connection error:', error);
  }
  
  // Test Supabase connection
  try {
    const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      supabaseStatus = 'missing credentials';
    } else {
      const supabase = createClient(supabaseUrl, supabaseKey);
      const { data, error } = await supabase.from('categories').select('count').limit(1);
      
      if (error) {
        throw error;
      }
      
      supabaseStatus = 'connected';
    }
  } catch (error) {
    supabaseStatus = 'error';
    supabaseError = error.message;
    console.error('Supabase connection error:', error);
  }
  
  // Get environment variables (masked for security)
  const envVars = {
    DATABASE_URL: process.env.DATABASE_URL ? 'configured' : 'missing',
    SUPABASE_URL: process.env.SUPABASE_URL ? 'configured' : 'missing',
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing',
    POSTGRES_HOST: process.env.POSTGRES_HOST || 'not configured',
    POSTGRES_USER: process.env.POSTGRES_USER ? 'configured' : 'missing',
    POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD ? 'configured' : 'missing',
    POSTGRES_DATABASE: process.env.POSTGRES_DATABASE || 'not configured',
    NODE_ENV: process.env.NODE_ENV || 'development',
  };
  
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      environment: process.env.NODE_ENV || 'development',
      netlify: true,
      database: {
        status: databaseStatus,
        error: errorMessage,
      },
      supabase: {
        status: supabaseStatus,
        error: supabaseError,
      },
      env: envVars,
      timestamp: new Date().toISOString()
    })
  };
};
