import { useAuth } from "@/hooks/use-auth";
import { Check, X } from "lucide-react";

interface UserStatusProps {
  showLabel?: boolean;
  className?: string;
}

export function UserStatus({ showLabel = true, className = "" }: UserStatusProps) {
  const { user } = useAuth();
  const isLoggedIn = !!user;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`flex items-center justify-center w-6 h-6 rounded-full ${isLoggedIn ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
        {isLoggedIn ? <Check className="w-4 h-4" /> : <X className="w-4 h-4" />}
      </div>
      {showLabel && (
        <span className="text-sm font-medium">
          {isLoggedIn ? 'Logged In' : 'Logged Out'}
        </span>
      )}
      {showLabel && isLoggedIn && (
        <span className="text-sm text-gray-500 ml-1">
          ({user.username})
        </span>
      )}
    </div>
  );
} 