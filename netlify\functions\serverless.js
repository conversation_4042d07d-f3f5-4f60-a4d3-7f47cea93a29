import express from 'express';
import serverless from 'serverless-http';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create express app
const app = express();

// Middleware to parse JSON requests
app.use(express.json());

// Main API router
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    netlify: true
  });
});

app.get('/api/debug', (req, res) => {
  res.status(200).json({
    environment: process.env.NODE_ENV || 'development',
    netlify: true,
    database: {
      url: process.env.DATABASE_URL ? 'configured' : 'missing',
      host: process.env.POSTGRES_HOST || 'not configured'
    },
    timestamp: new Date().toISOString()
  });
});

// Default handler for all other routes
app.all('/api/*', (req, res) => {
  res.status(200).json({
    message: 'API endpoint reached successfully',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Export the serverless handler
export const handler = serverless(app);
