import nodemailer from 'nodemailer';

export function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

export async function sendEmailOTP(email: string, otp: string) {
  const transporter = nodemailer.createTransport({
    // Configure your email service
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });

  const mailOptions = {
    from: process.env.SMTP_FROM,
    to: email,
    subject: 'Login Verification Code',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Your Login Verification Code</h2>
        <p>Use the following code to complete your login:</p>
        <h1 style="font-size: 36px; letter-spacing: 5px; text-align: center; padding: 20px; background: #f5f5f5; border-radius: 10px;">
          ${otp}
        </h1>
        <p>This code will expire in 5 minutes.</p>
        <p>If you didn't request this code, please ignore this email.</p>
      </div>
    `
  };

  await transporter.sendMail(mailOptions);
}