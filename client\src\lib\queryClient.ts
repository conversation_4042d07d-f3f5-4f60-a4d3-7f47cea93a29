import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    // Try to parse the error response as JSON first
    let errorMessage = res.statusText;

    try {
      const contentType = res.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await res.json();
        errorMessage = errorData.message || JSON.stringify(errorData);
      } else {
        // Check if it's HTML response
        const text = await res.text();
        // Filter out HTML responses which might contain <!DOCTYPE html>
        if (text.includes('<!DOCTYPE') || text.includes('<html')) {
          errorMessage = "Server error occurred. Please try again.";
        } else {
          errorMessage = text || res.statusText;
        }
      }
    } catch (e) {
      // If JSON parsing fails, fall back to text
      try {
        const text = await res.text();
        if (text.includes('<!DOCTYPE') || text.includes('<html')) {
          errorMessage = "Server error occurred. Please try again.";
        } else {
          errorMessage = text || res.statusText;
        }
      } catch (e2) {
        // If all else fails, use status text
        errorMessage = res.statusText || "Server error occurred";
      }
    }

    throw new Error(errorMessage);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: any,
  options?: RequestInit & { maxRetries?: number }
): Promise<Response> {
  // Ensure URL starts with a slash to make it relative to the server
  if (!url.startsWith('/')) {
    url = '/' + url;
  }

  // Extract maxRetries from options or use default
  const maxRetries = options?.maxRetries || 3;
  const { maxRetries: _, ...restOptions } = options || {};

  // Build request options
  const requestOptions: RequestInit = {
    method: method,
    credentials: "include",
    ...restOptions,
    headers: {
      "Accept": "application/json",
      ...restOptions?.headers,
    }
  };

  // Add JSON body if data is present
  if (data) {
    requestOptions.headers = {
      ...requestOptions.headers,
      "Content-Type": "application/json",
    };
    requestOptions.body = JSON.stringify(data);
  }

  console.log(`Sending ${method} request to ${url}`);

  let lastError: any;
  // Implement retry logic
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Add a small delay before retries (not on first attempt)
      if (attempt > 0) {
        const delayMs = Math.min(1000 * 2 ** attempt, 10000); // Exponential backoff with max 10s
        console.log(`Retry attempt ${attempt} for ${method} ${url} after ${delayMs}ms delay`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }

      // Make sure we're using the correct base URL
      const baseUrl = window.location.origin;
      const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;

      console.log(`Full URL for API request: ${fullUrl}`);

      const res = await fetch(fullUrl, requestOptions);

      // Special handling for 401 Unauthorized in production
      if (res.status === 401 && window.location.hostname !== 'localhost') {
        console.warn('Authentication error detected, redirecting to login');
        // Redirect to auth page after a short delay
        setTimeout(() => {
          window.location.href = '/auth';
        }, 100);
      }

      await throwIfResNotOk(res);
      return res;
    } catch (error) {
      lastError = error;
      console.error(`API request error for ${method} ${url} (attempt ${attempt + 1}/${maxRetries}):`, error);

      // Don't retry if it's a client error (4xx) except for 429 (Too Many Requests)
      if (error instanceof Error && error.message.includes('status code 4') && !error.message.includes('status code 429')) {
        break;
      }
    }
  }

  // If we've exhausted all retries, throw the last error
  console.error(`All ${maxRetries} retry attempts failed for ${method} ${url}`);
  throw lastError;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Get the URL from the first element of queryKey and ensure it starts with a slash
    let url = queryKey[0] as string;
    if (!url.startsWith('/')) {
      url = '/' + url;
    }

    try {
      console.log(`Fetching data from: ${url}`, {
        environment: process.env.NODE_ENV,
        isProduction: process.env.NODE_ENV === 'production',
        hostname: window.location.hostname,
        queryKey
      });

      // Make sure we're using the correct base URL
      const baseUrl = window.location.origin;

      // Add userId parameter for user-related endpoints
      let fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;

      // Add userId parameter for endpoints that need it
      if ((url.includes('/api/user') || url.includes('/api/cart') || url.includes('/api/wishlist'))
          && !url.includes('userId=') && localStorage.getItem('userId')) {
        const separator = url.includes('?') ? '&' : '?';
        fullUrl = `${fullUrl}${separator}userId=${localStorage.getItem('userId')}`;
        console.log(`Added userId parameter to URL: ${fullUrl}`);
      }

      // Add userId to query parameters object if it exists in the queryKey
      if (queryKey.length > 1 && typeof queryKey[1] === 'object' && queryKey[1] !== null) {
        const params = queryKey[1] as Record<string, any>;
        if (!params.userId && localStorage.getItem('userId')) {
          params.userId = localStorage.getItem('userId');
          console.log(`Added userId to query parameters:`, params);
        }
      }

      console.log(`Full URL: ${fullUrl}`);

      const res = await fetch(fullUrl, {
        credentials: "include",
        // Add cache control headers to prevent caching issues in production
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      // Special handling for 401 Unauthorized
      if (res.status === 401) {
        if (unauthorizedBehavior === "returnNull") {
          console.log(`Received 401 for ${url}, returning null as configured`);
          return null;
        } else if (window.location.hostname !== 'localhost') {
          // In production, redirect to auth page for unauthorized requests
          console.warn('Authentication error detected, redirecting to login');
          setTimeout(() => {
            window.location.href = '/auth';
          }, 100);
        }
      }

      await throwIfResNotOk(res);

      // Parse JSON with error handling
      try {
        const data = await res.json();
        console.log(`Received data from ${url}:`, {
          dataType: typeof data,
          isArray: Array.isArray(data),
          dataLength: Array.isArray(data) ? data.length : null,
          sampleData: Array.isArray(data) && data.length > 0 ? data[0] : data
        });
        return data;
      } catch (jsonError) {
        console.error(`Error parsing JSON from ${url}:`, jsonError);
        // Return empty array or object as fallback
        return Array.isArray(queryKey[1]) ? [] : {};
      }
    } catch (error) {
      console.error(`Error in query function for ${url}:`, error);

      // For categories and products endpoints, use fallback data
      if (url.includes('/api/categories')) {
        console.log(`Using fallback categories for ${url} due to error`);
        return [
          { id: 1, name: "Recycled Electronics", imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg" },
          { id: 2, name: "Upcycled Fashion", imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg" },
          { id: 3, name: "Eco-friendly Home", imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg" },
          { id: 4, name: "Recycled Accessories", imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg" }
        ];
      } else if (url.includes('/api/products')) {
        console.log(`Using fallback products for ${url} due to error`);
        return [
          {
            id: 1,
            name: "Refurbished Smartphone",
            description: "Like-new refurbished phone with 1-year warranty.",
            price: 329.99,
            discountPrice: 299.99,
            rating: 4.5,
            reviewCount: 34,
            imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
            categoryId: 1,
            inStock: true,
            isNew: true,
            isPopular: true,
            isSale: true
          },
          {
            id: 2,
            name: "Recycled Denim Backpack",
            description: "Stylish backpack made from recycled denim materials.",
            price: 79.99,
            discountPrice: null,
            rating: 4.8,
            reviewCount: 12,
            imageUrl: "https://images.pexels.com/photos/1545499/pexels-photo-1545499.jpeg",
            categoryId: 2,
            inStock: true,
            isNew: false,
            isPopular: true,
            isSale: false
          }
        ];
      } else if (url.includes('/api/wishlist') || url.includes('/api/cart')) {
        console.log(`Returning empty array for ${url} due to error`);
        return [];
      }

      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: true, // Enable refetch on window focus for better data freshness
      staleTime: 60000, // 1 minute instead of Infinity to ensure data is refreshed
      retry: 3, // Increase retry attempts
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff with max 30s
      onError: (error) => {
        console.error('Query error:', error);
      },
    },
    mutations: {
      retry: 2, // Increase retry attempts for mutations
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff with max 10s
      onError: (error) => {
        console.error('Mutation error:', error);
      },
    },
  },
});
