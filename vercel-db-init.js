// Vercel database initialization script
// This script is designed to be run as a Vercel build step

// Set environment variables for database connection
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
process.env.PGSSLMODE = 'no-verify';
process.env.PG_SSL_REJECT_UNAUTHORIZED = '0';
process.env.NODE_ENV = 'production';

// Import required modules
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import postgres from 'postgres';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env.production
const envPath = path.join(__dirname, '.env.production');
if (fs.existsSync(envPath)) {
  console.log('Loading environment variables from .env.production');
  dotenv.config({ path: envPath });
} else {
  console.log('No .env.production file found, using default environment variables');
  dotenv.config();
}

console.log('Vercel database initialization environment:');
console.log({
  NODE_ENV: process.env.NODE_ENV,
  DATABASE_URL: process.env.DATABASE_URL ? '✓ Present' : '✗ Missing',
  POSTGRES_URL: process.env.POSTGRES_URL ? '✓ Present' : '✗ Missing',
  SSL_SETTINGS: {
    NODE_TLS_REJECT_UNAUTHORIZED: process.env.NODE_TLS_REJECT_UNAUTHORIZED,
    PGSSLMODE: process.env.PGSSLMODE
  }
});

// Sample data
const categories = [
  {
    name: "Recycled Electronics",
    imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg"
  },
  {
    name: "Upcycled Fashion",
    imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg"
  },
  {
    name: "Eco-friendly Home",
    imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg"
  },
  {
    name: "Recycled Accessories",
    imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg"
  }
];

const products = [
  {
    name: "Refurbished Smartphone",
    description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
    price: 329.99,
    discountPrice: 299.99,
    rating: 4.5,
    reviewCount: 34,
    imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
    categoryId: 1,
    inStock: true,
    isNew: true,
    isPopular: false,
    isSale: true
  },
  {
    name: "Upcycled Denim Jacket",
    description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
    price: 89.99,
    discountPrice: null,
    rating: 4.8,
    reviewCount: 42,
    imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
    categoryId: 2,
    inStock: true,
    isNew: true,
    isPopular: true,
    isSale: false
  },
  {
    name: "Recycled Metal Water Bottle",
    description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
    price: 29.99,
    discountPrice: 24.99,
    rating: 4.9,
    reviewCount: 120,
    imageUrl: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg",
    categoryId: 4,
    inStock: true,
    isNew: false,
    isPopular: true,
    isSale: true
  },
  {
    name: "Bamboo Desk Organizer",
    description: "Sustainable bamboo desk organizer with multiple compartments for all your office essentials.",
    price: 49.99,
    discountPrice: null,
    rating: 4.6,
    reviewCount: 28,
    imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg",
    categoryId: 3,
    inStock: true,
    isNew: false,
    isPopular: false,
    isSale: false
  }
];

// Initialize database
async function initializeDatabase() {
  try {
    console.log('Testing database connection...');

    // Get connection string
    const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('No database connection string found');
    }

    // Configure connection
    const sql = postgres(connectionString, {
      ssl: { rejectUnauthorized: false },
      max: 1,
      idle_timeout: 5,
      connect_timeout: 10
    });

    // Test connection
    const testResult = await sql`SELECT 1 as test`;
    console.log('Database connection successful:', testResult[0].test === 1);

    // Clear existing data
    console.log('Clearing existing data...');
    try {
      await sql`TRUNCATE TABLE "wishlist_items" CASCADE`;
      await sql`TRUNCATE TABLE "cart_items" CASCADE`;
      await sql`TRUNCATE TABLE "order_items" CASCADE`;
      await sql`TRUNCATE TABLE "orders" CASCADE`;
      await sql`TRUNCATE TABLE "products" CASCADE`;
      await sql`TRUNCATE TABLE "categories" CASCADE`;
      await sql`TRUNCATE TABLE "users" CASCADE`;
      console.log('Existing data cleared successfully');
    } catch (clearError) {
      console.error('Error clearing existing data:', clearError);
      // Continue with initialization even if clearing fails
    }

    // Add categories
    console.log('Adding categories...');
    for (const category of categories) {
      try {
        await sql`
          INSERT INTO "categories" ("name", "image_url")
          VALUES (${category.name}, ${category.imageUrl})
        `;
      } catch (categoryError) {
        console.error('Error adding category:', category.name, categoryError);
      }
    }

    // Add products
    console.log('Adding products...');
    for (const product of products) {
      try {
        await sql`
          INSERT INTO "products" (
            "name", "description", "price", "discount_price", "rating",
            "review_count", "image_url", "category_id", "in_stock",
            "is_new", "is_popular", "is_sale"
          )
          VALUES (
            ${product.name}, ${product.description}, ${product.price},
            ${product.discountPrice}, ${product.rating}, ${product.reviewCount},
            ${product.imageUrl}, ${product.categoryId}, ${product.inStock},
            ${product.isNew}, ${product.isPopular}, ${product.isSale}
          )
        `;
      } catch (productError) {
        console.error('Error adding product:', product.name, productError);
      }
    }

    // Demo user and sample orders have been removed

    console.log('Database initialization complete');
    return true;
  } catch (error) {
    console.error('Error during database initialization:', error);
    return false;
  }
}

// Run the initialization function
initializeDatabase()
  .then(success => {
    if (success) {
      console.log('Database initialization verified for Vercel deployment');
      process.exit(0);
    } else {
      console.error('Failed to initialize database. Check your environment variables.');
      // Don't exit with error to allow deployment to continue
      process.exit(0);
    }
  })
  .catch(error => {
    console.error('Unexpected error during initialization:', error);
    // Don't exit with error to allow deployment to continue
    process.exit(0);
  });
