@echo off
echo Starting server on port 3000...

REM Set environment variables
set NODE_TLS_REJECT_UNAUTHORIZED=0
set PGSSLMODE=no-verify
set PG_SSL_REJECT_UNAUTHORIZED=0
set VERCEL=true
set PORT=3000
set HOST=0.0.0.0

REM Check if local tsx exists
if exist "node_modules\.bin\tsx.cmd" (
  echo Using local tsx installation...
  echo Running: node_modules\.bin\tsx server/index.ts
  node_modules\.bin\tsx server/index.ts
) else (
  echo Local tsx not found, trying with node...
  echo Running: node --loader tsx server/index.ts
  node --loader tsx server/index.ts
  
  REM If that fails, try with --import
  if %ERRORLEVEL% NEQ 0 (
    echo.
    echo First approach failed, trying with --import...
    node --import tsx server/index.ts
  )
)

pause
