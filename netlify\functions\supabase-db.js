// Specialized Supabase database connection module optimized for Netlify serverless functions
import { createClient } from '@supabase/supabase-js';
import { Pool } from 'pg';

// Cached connections to support connection reuse in serverless environment
let supabaseClient = null;
let pgPool = null;

// Create a Supabase client using the admin key for direct database access
export function getSupabaseClient() {
  if (supabaseClient) return supabaseClient;

  const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    return null;
  }

  supabaseClient = createClient(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: false
    }
  });

  return supabaseClient;
}

// Direct PostgreSQL connection with Node.js native driver
export function getPgPool() {
  if (pgPool) return pgPool;

  try {
    const connectionString = process.env.DATABASE_URL || process.env.POSTGRES_URL;
    
    if (!connectionString) {
      console.error('Missing database connection string');
      return null;
    }

    // Create connection pool with SSL settings for Supabase
    pgPool = new Pool({
      connectionString,
      ssl: {
        rejectUnauthorized: false, // Required for self-signed certs
      },
      max: 1, // Smaller pool for serverless
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
    });

    return pgPool;
  } catch (error) {
    console.error('Error creating PG pool:', error);
    return null;
  }
}

// Test the database connection
export async function testConnection() {
  let error = null;
  let connectedViaPg = false;
  let connectedViaSupabase = false;
  
  // Try direct PostgreSQL connection
  try {
    const pool = getPgPool();
    if (pool) {
      const client = await pool.connect();
      await client.query('SELECT 1');
      client.release();
      connectedViaPg = true;
    }
  } catch (err) {
    error = `PG error: ${err.message}`;
    console.error('PG connection test failed:', err);
  }
  
  // Try Supabase client connection
  try {
    const supabase = getSupabaseClient();
    if (supabase) {
      const { data, error: queryError } = await supabase.from('users').select('id').limit(1);
      if (!queryError) {
        connectedViaSupabase = true;
      } else {
        console.error('Supabase query error:', queryError);
      }
    }
  } catch (err) {
    if (!error) error = `Supabase error: ${err.message}`;
    console.error('Supabase connection test failed:', err);
  }
  
  return {
    success: connectedViaPg || connectedViaSupabase,
    connectedViaPg,
    connectedViaSupabase,
    error
  };
}

// Close all database connections (useful for cleanup)
export async function closeConnections() {
  if (pgPool) {
    await pgPool.end();
    pgPool = null;
  }
  supabaseClient = null;
}
