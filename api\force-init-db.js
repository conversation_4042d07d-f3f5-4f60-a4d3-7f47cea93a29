// Script to force initialize the database
import { db } from './db.js';
import { initializeDatabase } from './init-db.js';

async function main() {
  try {
    console.log('Starting database force initialization...');
    
    // Force initialize the database
    await initializeDatabase(true);
    
    console.log('Database force initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error during database force initialization:', error);
    process.exit(1);
  }
}

// Run the initialization function
main();
