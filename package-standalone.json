{"name": "rest-express", "version": "1.0.0", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "vercel-dev": "node vercel-dev.js", "vercel-like": "node direct-dev.js", "vercel-setup": "node vercel-setup.js", "vercel-build": "npm run build && node vercel-deploy.js && node vercel-db-init.js && node copy-frontend.js", "simple-dev": "node simple-dev.js", "port3000": "cross-env PORT=3000 VERCEL=true NODE_TLS_REJECT_UNAUTHORIZED=0 PGSSLMODE=no-verify node --import tsx server/index.ts", "fix-db": "node fix-db-connection.js", "direct-run": "node direct-run.js", "run-server": "node run-server.js", "standalone": "node standalone-server.js", "deploy": "node vercel-deploy.js", "simple": "node simple-server.js", "simple-fixed": "node simple-server-fixed.js", "direct": "node direct-server.js", "copy-frontend": "node copy-frontend.js", "init-db": "node direct-db-init.js", "standalone-init-db": "node standalone-db-init.js", "push-db": "node push-db-direct.js", "push-db-simple": "node push-db-simple.js", "manual-db-init": "node manual-db-init.js", "standalone-express": "node standalone-express.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@lottiefiles/dotlottie-web": "^0.42.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@tanstack/react-query": "^5.60.5", "@types/bcrypt": "^5.0.2", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.11", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.18.2", "gsap": "^3.12.7", "input-otp": "^1.4.2", "lottie-react": "^2.4.1", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.14.1", "postgres": "^3.4.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "react-use": "^17.6.0", "recharts": "^2.13.0", "scrollreveal": "^4.0.9", "stripe": "^17.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "twilio": "^5.5.1", "vaul": "^1.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}