import { createContext, useContext, ReactNode } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { insertWishlistItemSchema } from "@shared/schema";

type WishlistContextType = {
  wishlistItems: any[];
  isLoading: boolean;
  isError: boolean;
  isInWishlist: (productId: number) => boolean;
  addToWishlist: (productId: number) => Promise<void>;
  removeFromWishlist: (itemId: number) => Promise<void>;
};

const WishlistContext = createContext<WishlistContextType | null>(null);

export function WishlistProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: wishlistItems = [],
    isLoading,
    isError,
    refetch: refetchWishlist
  } = useQuery({
    queryKey: ["/api/wishlist", user ? { userId: user.id } : null],
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes - wishlist doesn't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    onError: (error) => {
      console.error('Error fetching wishlist:', error);
      toast({
        title: "Wishlist Error",
        description: "Failed to load wishlist items. Please try again.",
        variant: "destructive",
      });
    },
    // Ensure we always get an array even if the API fails
    select: (data) => {
      if (!data || !Array.isArray(data)) {
        console.warn('Wishlist data is not an array, returning empty array');
        return [];
      }
      return data;
    }
  });

  const addToWishlistMutation = useMutation({
    mutationFn: async (productId: number) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to add items to your wishlist");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      // Make a direct fetch request to ensure it works
      console.log(`Adding product ${productId} to wishlist for user ${user.id}`);

      const response = await fetch('/api/wishlist', {
        method: 'POST',
        credentials: 'include', // Important: include credentials for cookies
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          productId: parseInt(String(productId))
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to add to wishlist: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully added product ${productId} to wishlist`);
      return response.json();
    },
    onSuccess: (data) => {
      console.log('Successfully added to wishlist:', data);

      if (user) {
        // Use optimistic updates instead of aggressive invalidation
        queryClient.setQueryData(["/api/wishlist", { userId: user.id }], (oldData: any) => {
          if (!oldData || !Array.isArray(oldData)) return [data];
          return [...oldData, data];
        });

        // Only invalidate the specific user's wishlist query
        queryClient.invalidateQueries({
          queryKey: ["/api/wishlist", { userId: user.id }],
          exact: true
        });
      }

      toast({
        title: "Added to wishlist",
        description: "Item added to your wishlist successfully.",
      });
    },
    onError: (error) => {
      console.error('Error adding to wishlist:', error);
      toast({
        title: "Wishlist Error",
        description: "Failed to add item to wishlist. Please try again.",
        variant: "destructive",
      });
    }
  });

  const removeFromWishlistMutation = useMutation({
    mutationFn: async (itemId: number) => {
      if (!user || !user.id) {
        throw new Error("You must be logged in to remove items from your wishlist");
      }

      // Store userId in localStorage for future requests
      localStorage.setItem('userId', user.id.toString());

      console.log(`Removing wishlist item with ID: ${itemId} for user: ${user.id}`);

      // Use direct fetch with credentials to ensure cookies are sent
      const response = await fetch(`/api/wishlist/${itemId}`, {
        method: 'DELETE',
        credentials: 'include', // Important: include credentials for cookies
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from server (${response.status}):`, errorText);
        throw new Error(`Failed to remove from wishlist: ${response.status} ${response.statusText}`);
      }

      console.log(`Successfully removed wishlist item ${itemId}, status: ${response.status}`);
      return true;
    },
    onSuccess: (_, itemId) => {
      console.log('Successfully removed from wishlist');

      if (user) {
        // Use optimistic updates to remove the item immediately
        queryClient.setQueryData(["/api/wishlist", { userId: user.id }], (oldData: any) => {
          if (!oldData || !Array.isArray(oldData)) return [];
          return oldData.filter((item: any) => item.id !== itemId);
        });

        // Only invalidate the specific user's wishlist query
        queryClient.invalidateQueries({
          queryKey: ["/api/wishlist", { userId: user.id }],
          exact: true
        });
      }

      toast({
        title: "Removed from wishlist",
        description: "Item removed from your wishlist.",
      });
    },
    onError: (error) => {
      console.error('Error removing from wishlist:', error);
      toast({
        title: "Wishlist Error",
        description: "Failed to remove item from wishlist. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Safely check if an item is in the wishlist
  const isInWishlist = (productId: number): boolean => {
    if (!wishlistItems || !Array.isArray(wishlistItems)) return false;

    try {
      return wishlistItems.some((item: any) => {
        // Handle different possible data structures
        if (!item) return false;

        // Direct match on productId
        if (item.productId === productId) return true;

        // Match on nested product.id
        if (item.product && typeof item.product === 'object' && item.product.id === productId) return true;

        return false;
      });
    } catch (error) {
      console.error('Error checking wishlist status:', error);
      return false; // Fail safely
    }
  };

  const addToWishlist = async (productId: number) => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please login to add items to your wishlist",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log(`Adding product ${productId} to wishlist for user ${user.id}`);
      await addToWishlistMutation.mutateAsync(productId);
    } catch (error) {
      console.error('Error in addToWishlist:', error);
      // Error is already handled in mutation
    }
  };

  const removeFromWishlist = async (itemId: number) => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please login to manage your wishlist",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log(`Removing item ${itemId} from wishlist for user ${user.id}`);
      await removeFromWishlistMutation.mutateAsync(itemId);
    } catch (error) {
      console.error('Error in removeFromWishlist:', error);
      // Error is already handled in mutation
    }
  };

  const value: WishlistContextType = {
    wishlistItems: Array.isArray(wishlistItems) ? wishlistItems : [],
    isLoading,
    isError,
    isInWishlist,
    addToWishlist,
    removeFromWishlist,
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist() {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error("useWishlist must be used within a WishlistProvider");
  }
  return context;
}