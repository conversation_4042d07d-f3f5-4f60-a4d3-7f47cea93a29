// This script initializes the database by calling the initialize method from the database storage class
// We'll use ESM syntax here
import { dbStorage } from './server/database-storage.js';

async function initializeDatabase() {
  try {
    console.log("Starting database initialization...");
    
    // Call the database initialization method
    await dbStorage.initialize();
    
    console.log("Database initialization completed successfully!");
  } catch (error) {
    console.error("Error initializing database:", error);
  }
}

// Run the initialization function
initializeDatabase();