const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get database connection string from environment
const dbUrl = process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/ecommerce';

// Define the migration file to run
const migrationFile = path.join(__dirname, 'migrations', 'add_is_logged_in.sql');

// Check if the file exists
if (!fs.existsSync(migrationFile)) {
  console.error(`Migration file ${migrationFile} does not exist`);
  process.exit(1);
}

// Run the migration using psql
try {
  console.log(`Running migration: ${migrationFile}`);
  execSync(`psql "${dbUrl}" -f "${migrationFile}"`, { stdio: 'inherit' });
  console.log('Migration completed successfully');
} catch (error) {
  console.error('Migration failed:', error.message);
  process.exit(1);
} 