// Database connection utility for Netlify functions
import { Pool } from 'pg';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Cached connections to support connection reuse in serverless environment
let supabaseClient = null;
let pgPool = null;

// Get Supabase client
export function getSupabaseClient() {
  if (supabaseClient) return supabaseClient;

  const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    return null;
  }

  try {
    supabaseClient = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false
      }
    });
    return supabaseClient;
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    return null;
  }
}

// Get PostgreSQL connection pool
export function getPgPool() {
  if (pgPool) return pgPool;

  try {
    const connectionString = process.env.DATABASE_URL || process.env.POSTGRES_URL;
    
    if (!connectionString) {
      console.error('Missing database connection string');
      return null;
    }

    // Create connection pool with SSL settings for Supabase
    pgPool = new Pool({
      connectionString,
      ssl: {
        rejectUnauthorized: false, // Required for self-signed certs
      },
      max: 3, // Smaller pool for serverless
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
    });

    return pgPool;
  } catch (error) {
    console.error('Error creating PG pool:', error);
    return null;
  }
}

// Test the database connection
export async function testDatabaseConnection() {
  let error = null;
  let success = false;
  
  try {
    const pool = getPgPool();
    if (pool) {
      const client = await pool.connect();
      await client.query('SELECT 1');
      client.release();
      success = true;
    }
  } catch (err) {
    error = err.message;
    console.error('Database connection test failed:', err);
  }
  
  return { success, error };
}

// Function to check table existence and initialize if needed
export async function initializeDatabase() {
  try {
    // First verify connection
    const { success, error } = await testDatabaseConnection();
    
    if (!success) {
      console.error('Cannot initialize database - connection failed:', error);
      return false;
    }
    
    const pool = getPgPool();
    const client = await pool.connect();
    
    try {
      // Check if categories table exists
      const result = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public'
          AND table_name = 'categories'
        );
      `);
      
      const tablesExist = result.rows[0].exists;
      
      if (!tablesExist) {
        console.log('Creating initial database tables...');
        
        // Create categories table
        await client.query(`
          CREATE TABLE IF NOT EXISTS categories (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            imageUrl TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
        `);
        
        // Create products table 
        await client.query(`
          CREATE TABLE IF NOT EXISTS products (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            price DECIMAL(10, 2) NOT NULL,
            discountPrice DECIMAL(10, 2),
            rating DECIMAL(3, 1),
            reviewCount INTEGER DEFAULT 0,
            imageUrl TEXT,
            categoryId INTEGER REFERENCES categories(id),
            inStock BOOLEAN DEFAULT true,
            isNew BOOLEAN DEFAULT false,
            isPopular BOOLEAN DEFAULT false,
            isSale BOOLEAN DEFAULT false,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
        `);
        
        // Insert sample categories if no data
        await client.query(`
          INSERT INTO categories (name, imageUrl) VALUES
          ('Recycled Electronics', 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg'),
          ('Upcycled Fashion', 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg'),
          ('Eco-friendly Home', 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg'),
          ('Recycled Accessories', 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg');
        `);
        
        // Insert sample products if no data
        await client.query(`
          INSERT INTO products (name, description, price, rating, reviewCount, imageUrl, categoryId, isNew) VALUES
          ('Refurbished Smartphone', 'Like-new refurbished phone with 1-year warranty.', 329.99, 4.5, 34, 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg', 1, true),
          ('Recycled Plastic Watch', 'Stylish watch made from ocean plastic.', 79.99, 4.1, 27, 'https://images.pexels.com/photos/3908800/pexels-photo-3908800.jpeg', 4, true),
          ('Sustainable Bamboo Toothbrush', 'Eco-friendly bamboo toothbrush with biodegradable handle.', 6.99, 4.6, 42, 'https://images.pexels.com/photos/3737593/pexels-photo-3737593.jpeg', 3, true),
          ('Upcycled Denim Backpack', 'Unique backpack made from recycled denim.', 89.99, 4.8, 19, 'https://images.pexels.com/photos/5704412/pexels-photo-5704412.jpeg', 2, true);
        `);
        
        console.log('Database initialized with tables and sample data');
      } else {
        console.log('Database tables already exist - skipping initialization');
      }
      
      return true;
    } finally {
      client.release();
    }
  } catch (err) {
    console.error('Error initializing database:', err);
    return false;
  }
}
