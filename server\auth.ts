import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { sendOTP } from "./email.js";
import { Express } from "express";
import session from "express-session";
import { dbStorage as storage } from "./database-storage.js";
// Already using database storage - no change needed
// Import schema from local compatibility layer instead of @shared/schema.js
import { User as SelectUser } from "./schema.js";
import { generateOTP, sendEmailOTP } from './utils/otp';

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

// Removed hashing functions
// Using plain text passwords instead

export function setupAuth(app: Express) {
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "elite-shop-secret-key",
    resave: true, // Changed to true to ensure session is saved on each request
    saveUninitialized: true, // Changed to true to ensure new sessions are saved
    store: storage.sessionStore,
    cookie: {
      maxAge: 1000 * 60 * 60 * 24 * 7, // 1 week
      secure: false, // Set this to false for both development and production for now
      httpOnly: true,
      sameSite: 'lax',
    }
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        const user = await storage.getUserByUsername(username);
        // Compare passwords directly without hashing
        if (!user || user.password !== password) {
          return done(null, false, { message: "Invalid username or password" });
        } else {
          return done(null, user);
        }
      } catch (err) {
        return done(err);
      }
    }),
  );

  passport.serializeUser((user, done) => done(null, user.id));

  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (err) {
      done(err);
    }
  });

  app.post("/api/register", async (req, res, next) => {
    try {
      const { username, email, password, fullName } = req.body;
      
      console.log('Received registration data:', { username, email, fullName }); // Debug log

      // Validate required fields
      if (!username || !email || !password || !fullName) {
        console.log('Missing required fields:', { username, email, fullName }); // Debug log
        return res.status(400).json({ 
          message: "All fields are required",
          missing: {
            username: !username,
            email: !email,
            password: !password,
            fullName: !fullName
          }
        });
      }

      // Check if username already exists
      const existingUsername = await storage.getUserByUsername(username);
      if (existingUsername) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Check if email already exists
      const existingEmail = await storage.getUserByEmail(email);
      if (existingEmail) {
        return res.status(400).json({ message: "Email already exists" });
      }

      // Create user
      const user = await storage.createUser({
        username,
        email,
        password,
        fullName,
        isLoggedIn: true
      });

      // Remove password from the response
      const { password: _, ...userWithoutPassword } = user;

      console.log('User created successfully:', userWithoutPassword); // Debug log

      // Return success response
      return res.status(201).json({
        message: "Registration successful",
        user: userWithoutPassword
      });
      
    } catch (error) {
      console.error("Registration error:", error);
      return res.status(500).json({ 
        message: "Registration failed. Please try again.",
        error: error.message 
      });
    }
  });

  // OTP functionality removed

  app.post("/api/login", (req, res, next) => {
    passport.authenticate("local", (err: Error | null, user: Express.User | undefined, info: { message: string } | undefined) => {
      if (err) {
        return next(err);
      }

      if (!user) {
        return res.status(401).json({ message: info?.message || "Authentication failed" });
      }

      req.login(user, (err) => {
        if (err) {
          return next(err);
        }

        // Update user status in database to logged in (true)
        storage.updateUser(user.id, { isLoggedIn: true })
          .then(() => {
            // Remove password from the response
            const { password, ...userWithoutPassword } = user;
            return res.status(200).json(userWithoutPassword);
          })
          .catch(error => {
            console.error("Failed to update login status:", error);
            // Still return success even if status update fails
            const { password, ...userWithoutPassword } = user;
            return res.status(200).json(userWithoutPassword);
          });
      });
    })(req, res, next);
  });

  app.post("/api/logout", (req, res, next) => {
    // Store the user ID before logging out
    const userId = req.user?.id;

    req.logout((err: Error | null | undefined) => {
      if (err) return next(err);

      // Update user status in database to logged out (false) if we have a user ID
      if (userId) {
        storage.updateUser(userId, { isLoggedIn: false })
          .then(() => {
            res.sendStatus(200);
          })
          .catch(error => {
            console.error("Failed to update logout status:", error);
            res.sendStatus(200); // Still return success even if status update fails
          });
      } else {
        res.sendStatus(200);
      }
    });
  });

  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    // Remove password from the response
    const { password, ...userWithoutPassword } = req.user as SelectUser;
    res.json(userWithoutPassword);
  });

  // Store OTPs temporarily (in production, use Redis or similar)
  const otpStore = new Map<string, { otp: string; expires: number }>();

  app.post('/api/auth/verify-credentials', async (req, res) => {
    try {
      const { username, password } = req.body;
      
      // Get user from database
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        return res.status(401).json({ message: 'Invalid username or password' });
      }

      // Verify password (use proper password hashing in production)
      const isValidPassword = await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return res.status(401).json({ message: 'Invalid username or password' });
      }

      // Return success with email for OTP
      return res.json({ 
        success: true, 
        email: user.email 
      });
    } catch (error) {
      console.error('Auth error:', error);
      return res.status(500).json({ message: 'Authentication failed' });
    }
  });

  app.post('/api/auth/send-otp', async (req, res) => {
    try {
      const { email } = req.body;
      
      // Generate 6-digit OTP
      const otp = generateOTP();
      
      // Store OTP with 5-minute expiration
      otpStore.set(email, {
        otp,
        expires: Date.now() + 5 * 60 * 1000
      });

      // Send OTP via email
      await sendEmailOTP(email, otp);

      return res.json({ 
        success: true, 
        message: 'OTP sent successfully' 
      });
    } catch (error) {
      console.error('OTP error:', error);
      return res.status(500).json({ message: 'Failed to send OTP' });
    }
  });

  app.post('/api/auth/verify-otp', async (req, res) => {
    try {
      const { email, otp } = req.body;
      
      const storedOTP = otpStore.get(email);
      
      if (!storedOTP || storedOTP.expires < Date.now()) {
        return res.status(401).json({ message: 'OTP expired' });
      }

      if (storedOTP.otp !== otp) {
        return res.status(401).json({ message: 'Invalid OTP' });
      }

      // Clear OTP after successful verification
      otpStore.delete(email);

      // Get user and create session
      const user = await storage.getUserByEmail(email);
      req.session.user = user;

      return res.json({ 
        success: true, 
        user 
      });
    } catch (error) {
      console.error('OTP verification error:', error);
      return res.status(500).json({ message: 'OTP verification failed' });
    }
  });
}


