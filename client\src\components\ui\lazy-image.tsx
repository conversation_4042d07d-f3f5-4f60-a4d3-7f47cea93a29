import { useState, useRef, useEffect } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  placeholder?: string;
  onLoad?: () => void;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  style?: React.CSSProperties;
}

export function LazyImage({
  src,
  alt,
  className = '',
  fallbackSrc,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzlmYTZiMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
  onLoad,
  onError,
  style
}: LazyImageProps) {
  const [imageSrc, setImageSrc] = useState<string>(placeholder);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px' // Start loading 50px before the image comes into view
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Load the actual image when it comes into view
  useEffect(() => {
    if (isInView && !isLoaded && !hasError) {
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
        onLoad?.();
      };
      
      img.onerror = () => {
        setHasError(true);
        if (fallbackSrc) {
          setImageSrc(fallbackSrc);
        }
      };
      
      img.src = src;
    }
  }, [isInView, src, fallbackSrc, isLoaded, hasError, onLoad]);

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    if (!hasError && fallbackSrc) {
      setHasError(true);
      setImageSrc(fallbackSrc);
    }
    onError?.(e);
  };

  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-70'} ${className}`}
      style={style}
      onError={handleError}
      loading="lazy" // Native lazy loading as fallback
    />
  );
}

// Higher-order component for optimized image loading
interface OptimizedImageProps extends LazyImageProps {
  categoryId?: number;
  productId?: number;
}

export function OptimizedImage({
  categoryId,
  productId,
  src,
  alt,
  className,
  onError,
  ...props
}: OptimizedImageProps) {
  // Generate category-specific fallback images
  const getCategoryFallback = (catId?: number): string => {
    switch (catId) {
      case 1:
        return 'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg';
      case 2:
        return 'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg';
      case 3:
        return 'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg';
      case 4:
        return 'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg';
      default:
        return 'https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg';
    }
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = e.target as HTMLImageElement;
    
    // Try to fix malformed URLs first
    if (src && src.includes('?')) {
      const fixedUrl = src.split('?')[0];
      console.log(`Trying with fixed URL: ${fixedUrl}`);
      target.src = fixedUrl;
      return;
    }

    // Use category-specific fallback
    const fallbackUrl = getCategoryFallback(categoryId);
    console.log(`Using fallback image for product ${productId} with category ${categoryId}: ${fallbackUrl}`);
    target.src = fallbackUrl;
    
    onError?.(e);
  };

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={className}
      fallbackSrc={getCategoryFallback(categoryId)}
      onError={handleImageError}
      {...props}
    />
  );
}

// Image preloader utility for critical images
export class ImagePreloader {
  private static preloadedImages = new Set<string>();

  static preload(urls: string[]): Promise<void[]> {
    const promises = urls.map(url => {
      if (this.preloadedImages.has(url)) {
        return Promise.resolve();
      }

      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          this.preloadedImages.add(url);
          resolve();
        };
        img.onerror = reject;
        img.src = url;
      });
    });

    return Promise.all(promises);
  }

  static preloadCriticalImages() {
    // Preload hero image and common fallback images
    const criticalImages = [
      'https://png.pngtree.com/background/20230527/original/pngtree-mobile-phone-with-a-shopping-cart-on-it-picture-image_2762466.jpg',
      'https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg',
      'https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg',
      'https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg',
      'https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg'
    ];

    return this.preload(criticalImages);
  }
}
