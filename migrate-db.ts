import postgres from 'postgres';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory path of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get database connection string from environment
const dbUrl = process.env.DATABASE_URL;

if (!dbUrl) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Configure PostgreSQL client
const sql = postgres(dbUrl, {
  ssl: {
    rejectUnauthorized: process.env.NODE_ENV === 'production',
  },
  max: 1, // Use a single connection
  idle_timeout: 30, // Reduced for serverless environments
  connect_timeout: 10 // Connection timeout in seconds
});

const migrationsDir = path.join(__dirname, 'migrations');

async function runMigrations() {
  try {
    // Read all migration files
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Ensure they're run in order

    console.log(`Found ${migrationFiles.length} migration files`);

    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      const filePath = path.join(migrationsDir, file);
      const sqlContent = fs.readFileSync(filePath, 'utf8');
      
      // Split statements by statement-breakpoint
      const statements = sqlContent.split('-->');
      
      for (const statement of statements) {
        const trimmedStatement = statement.trim();
        if (trimmedStatement && !trimmedStatement.startsWith('statement-breakpoint')) {
          try {
            await sql.unsafe(trimmedStatement);
            console.log(`Executed SQL statement from ${file}`);
          } catch (error) {
            // Log the error but continue (some errors like 'table already exists' are expected)
            console.warn(`Warning executing statement: ${error.message}`);
          }
        }
      }
      
      console.log(`Migration ${file} completed`);
    }

    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sql.end();
  }
}

runMigrations();
